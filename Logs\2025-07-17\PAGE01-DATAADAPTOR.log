17/07/2025 : 08:18:02.942 : Entering
17/07/2025 : 08:18:02.947 :     Client           = 
17/07/2025 : 08:18:02.949 :     DateFactureStart = 
17/07/2025 : 08:18:02.950 :     DateFactureEnd   = 
17/07/2025 : 08:18:10.958 :     Count = 8832132
17/07/2025 : 08:18:10.958 : Leaving
17/07/2025 : 08:18:19.285 : Entering
17/07/2025 : 08:18:19.286 :     Client           = 
17/07/2025 : 08:18:19.286 :     DateFactureStart = 
17/07/2025 : 08:18:19.287 :     DateFactureEnd   = 
17/07/2025 : 08:18:50.028 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
17/07/2025 : 08:18:50.105 :     Entering
17/07/2025 : 08:18:50.105 :         Client           = 
17/07/2025 : 08:18:50.105 :         DateFactureStart = 
17/07/2025 : 08:18:50.106 :         DateFactureEnd   = 
17/07/2025 : 08:19:20.511 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
17/07/2025 : 08:19:20.558 :         Entering
17/07/2025 : 08:19:20.558 :             Client           = 
17/07/2025 : 08:19:20.559 :             DateFactureStart = 
17/07/2025 : 08:19:20.559 :             DateFactureEnd   = 
17/07/2025 : 08:19:51.023 :             Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
17/07/2025 : 08:20:20.183 :             Entering
17/07/2025 : 08:20:20.184 :                 Client           = 
17/07/2025 : 08:20:20.184 :                 DateFactureStart = 
17/07/2025 : 08:20:20.185 :                 DateFactureEnd   = 
17/07/2025 : 08:20:22.542 :                 Count = 8832132
17/07/2025 : 08:20:22.543 :             Leaving
17/07/2025 : 08:20:22.582 :             Entering
17/07/2025 : 08:20:22.588 :                 Client           = 
17/07/2025 : 08:20:22.626 :                 DateFactureStart = 
17/07/2025 : 08:20:22.626 :                 DateFactureEnd   = 
17/07/2025 : 08:32:31.041 : Entering
17/07/2025 : 08:32:31.042 :     Client           = 
17/07/2025 : 08:32:31.043 :     DateFactureStart = 
17/07/2025 : 08:32:31.043 :     DateFactureEnd   = 
17/07/2025 : 08:32:33.891 :     Count = 8832132
17/07/2025 : 08:32:33.892 : Leaving
17/07/2025 : 08:32:34.096 : Entering
17/07/2025 : 08:32:34.096 :     Client           = 
17/07/2025 : 08:32:34.098 :     DateFactureStart = 
17/07/2025 : 08:32:34.099 :     DateFactureEnd   = 
17/07/2025 : 08:32:36.042 :     Count = 8832132
17/07/2025 : 08:32:36.042 : Leaving
17/07/2025 : 08:32:43.311 : Entering
17/07/2025 : 08:32:43.312 :     Client           = 
17/07/2025 : 08:32:43.313 :     DateFactureStart = 
17/07/2025 : 08:32:43.314 :     DateFactureEnd   = 
17/07/2025 : 08:33:13.561 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
17/07/2025 : 08:33:13.599 :     Entering
17/07/2025 : 08:33:13.600 :         Client           = 
17/07/2025 : 08:33:13.601 :         DateFactureStart = 
17/07/2025 : 08:33:13.602 :         DateFactureEnd   = 
17/07/2025 : 08:33:43.758 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
17/07/2025 : 08:48:15.699 :         Entering
17/07/2025 : 08:48:15.699 :             Client           = 
17/07/2025 : 08:48:15.700 :             DateFactureStart = 
17/07/2025 : 08:48:15.702 :             DateFactureEnd   = 
17/07/2025 : 08:48:16.502 :             Unable to cast object of type 'System.String' to type 'System.DateTime'.
17/07/2025 : 08:48:16.523 :             Entering
17/07/2025 : 08:48:16.524 :                 Client           = 
17/07/2025 : 08:48:16.524 :                 DateFactureStart = 
17/07/2025 : 08:48:16.525 :                 DateFactureEnd   = 
17/07/2025 : 08:48:34.722 :                 Unable to cast object of type 'System.String' to type 'System.DateTime'.
17/07/2025 : 08:50:19.571 :                 Entering
17/07/2025 : 08:50:19.571 :                     Client           = 
17/07/2025 : 08:50:19.573 :                     DateFactureStart = 
17/07/2025 : 08:50:19.573 :                     DateFactureEnd   = 
17/07/2025 : 08:50:19.638 :                     Unable to cast object of type 'System.String' to type 'System.DateTime'.
17/07/2025 : 08:50:19.665 :                     Entering
17/07/2025 : 08:50:19.666 :                         Client           = 
17/07/2025 : 08:50:19.668 :                         DateFactureStart = 
17/07/2025 : 08:50:19.669 :                         DateFactureEnd   = 
17/07/2025 : 08:50:19.731 :                         Unable to cast object of type 'System.String' to type 'System.DateTime'.
17/07/2025 : 08:56:21.113 : Entering
17/07/2025 : 08:56:21.117 :     Client           = 
17/07/2025 : 08:56:21.118 :     DateFactureStart = 
17/07/2025 : 08:56:21.118 :     DateFactureEnd   = 
17/07/2025 : 08:56:24.340 :     Unable to cast object of type 'System.String' to type 'System.DateTime'.
17/07/2025 : 08:56:24.718 :     Entering
17/07/2025 : 08:56:24.718 :         Client           = 
17/07/2025 : 08:56:24.720 :         DateFactureStart = 
17/07/2025 : 08:56:24.721 :         DateFactureEnd   = 
17/07/2025 : 08:56:41.540 :         Unable to cast object of type 'System.String' to type 'System.DateTime'.
17/07/2025 : 09:08:56.006 : Entering
17/07/2025 : 09:08:56.010 :     Client           = 
17/07/2025 : 09:08:56.011 :     DateFactureStart = 
17/07/2025 : 09:08:56.012 :     DateFactureEnd   = 
17/07/2025 : 09:09:00.875 :     Count = 8832132
17/07/2025 : 09:09:00.876 : Leaving
17/07/2025 : 09:09:01.239 : Entering
17/07/2025 : 09:09:01.240 :     Client           = 
17/07/2025 : 09:09:01.241 :     DateFactureStart = 
17/07/2025 : 09:09:01.241 :     DateFactureEnd   = 
17/07/2025 : 09:09:16.951 :     Count = 8832132
17/07/2025 : 09:09:16.951 : Leaving
17/07/2025 : 09:10:16.060 : Entering
17/07/2025 : 09:10:16.061 :     Client           = 
17/07/2025 : 09:10:16.062 :     DateFactureStart = 
17/07/2025 : 09:10:16.064 :     DateFactureEnd   = 
17/07/2025 : 09:10:37.951 :     Count = 8832132
17/07/2025 : 09:10:37.951 : Leaving
17/07/2025 : 09:10:38.005 : Entering
17/07/2025 : 09:10:38.005 :     Client           = 
17/07/2025 : 09:10:38.007 :     DateFactureStart = 
17/07/2025 : 09:10:38.008 :     DateFactureEnd   = 
17/07/2025 : 09:10:50.135 :     Count = 8832132
17/07/2025 : 09:10:50.136 : Leaving
17/07/2025 : 09:10:50.156 : Entering
17/07/2025 : 09:10:50.156 :     Client           = 
17/07/2025 : 09:10:50.157 :     DateFactureStart = 
17/07/2025 : 09:10:50.158 :     DateFactureEnd   = 
17/07/2025 : 09:11:02.321 :     Count = 8832132
17/07/2025 : 09:11:02.322 : Leaving
17/07/2025 : 09:11:02.344 : Entering
17/07/2025 : 09:11:02.344 :     Client           = 
17/07/2025 : 09:11:02.345 :     DateFactureStart = 
17/07/2025 : 09:11:02.346 :     DateFactureEnd   = 
17/07/2025 : 09:11:04.267 :     Count = 8832132
17/07/2025 : 09:11:04.267 : Leaving
17/07/2025 : 09:11:28.805 : Entering
17/07/2025 : 09:11:28.806 :     Client           = 
17/07/2025 : 09:11:28.810 :     DateFactureStart = 
17/07/2025 : 09:11:28.812 :     DateFactureEnd   = 
17/07/2025 : 09:11:41.336 :     Count = 8832132
17/07/2025 : 09:11:41.336 : Leaving
17/07/2025 : 09:11:41.362 : Entering
17/07/2025 : 09:11:41.363 :     Client           = 
17/07/2025 : 09:11:41.364 :     DateFactureStart = 
17/07/2025 : 09:11:41.365 :     DateFactureEnd   = 
17/07/2025 : 09:11:53.318 :     Count = 8832132
17/07/2025 : 09:11:53.320 : Leaving
17/07/2025 : 09:31:07.440 : Entering
17/07/2025 : 09:31:07.443 :     Client           = 
17/07/2025 : 09:31:07.444 :     DateFactureStart = 
17/07/2025 : 09:31:07.444 :     DateFactureEnd   = 
17/07/2025 : 09:31:12.296 :     Count = 8832132
17/07/2025 : 09:31:12.298 : Leaving
17/07/2025 : 09:31:12.937 : Entering
17/07/2025 : 09:31:12.937 :     Client           = 
17/07/2025 : 09:31:12.939 :     DateFactureStart = 
17/07/2025 : 09:31:12.940 :     DateFactureEnd   = 
17/07/2025 : 09:31:27.451 :     Count = 8832132
17/07/2025 : 09:31:27.453 : Leaving
