<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-CarSystemsWebApp-76bec111-a2a5-4d25-9607-ca66423d92fa</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Components\Pages\Ecriture PourL%27AnneeX.razor" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3" />
    <PackageReference Include="Syncfusion.Blazor.Buttons" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Calendars" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Charts" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.DataForm" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Diagram" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.DropDowns" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Grid" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Inputs" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Layouts" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Lists" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Navigations" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Popups" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.ProgressBar" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Schedule" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.Themes" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Blazor.TreeGrid" Version="28.1.33" />
    <PackageReference Include="Syncfusion.Pdf.Net.Core" Version="28.1.33" />
    <PackageReference Include="Syncfusion.XlsIO.Net.Core" Version="28.1.33" />
    <PackageReference Include="Syncfusion.XlsIORenderer.Net.Core" Version="28.1.33" />
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>

</Project>
