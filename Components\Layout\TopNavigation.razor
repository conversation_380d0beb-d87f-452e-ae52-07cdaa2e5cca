@using CarSystemsWebApp.Services
@inject PageTitleService PageTitleService
@inject NavigationManager Navigation
@implements IDisposable

<style>
/* ========================================= */
/* TOP NAVIGATION STYLING */
/* ========================================= */

.top-navigation {
    background: linear-gradient(135deg, #F8981D 0%, #E64C3C 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    max-width: 100%;
    margin: 0 auto;
}

/* Brand Section */
.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 0.5px;
}

.brand-icon {
    font-size: 1.5rem;
}

.brand-text {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Center Section - Page Title */
.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.page-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Right Section - Page Selector */
.nav-right {
    display: flex;
    align-items: center;
}

.page-selector {
    width: 300px;
}

/* Simple CSS Dropdown */
.nav-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
}

.nav-dropdown-btn {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    color: #333;
    font-weight: 500;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    width: 100%;
    outline: none;
    cursor: pointer;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-arrow {
    margin-left: 0.5rem;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    color: #333;
    font-weight: bold;
    line-height: 1;
}

.nav-dropdown-btn:hover {
    background: white;
    border-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.nav-dropdown:hover .dropdown-arrow {
    transform: rotate(90deg);
    color: #F8981D;
}

.nav-dropdown-content {
    display: none;
    position: absolute;
    background: white;
    min-width: 100%;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    z-index: 1000;
    top: 100%;
    left: 0;
    margin-top: -2px;
    max-height: 400px;
    overflow-y: auto;
}

.nav-dropdown-content::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #ddd;
    filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}

.nav-dropdown:hover .nav-dropdown-content {
    display: block;
}

.nav-dropdown-content a {
    color: #333;
    padding: 0.75rem 1rem;
    text-decoration: none;
    display: block;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.nav-dropdown-content a:hover {
    background: rgba(248, 152, 29, 0.1);
    color: #F8981D;
}

.nav-dropdown-content a:last-child {
    border-bottom: none;
}

/* Responsive Design */
@@media (max-width: 768px) {
    .nav-container {
        padding: 0.5rem 1rem;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .nav-right {
        order: 3;
        width: 100%;
        margin: 0.5rem 0 0 0;
        justify-content: center;
    }

    .page-selector {
        width: 100%;
        max-width: 300px;
    }

    .brand-text {
        display: none;
    }
}

@@media (max-width: 480px) {
    .page-selector {
        width: 100%;
    }
}
</style>

<nav class="top-navigation">
    <div class="nav-container">
        <!-- Brand Section -->
        <div class="nav-brand">
            <i class="fas fa-car brand-icon"></i>
            <span class="brand-text">CarSystems</span>
        </div>

        <!-- Center Section - Page Title -->
        <div class="nav-center">
            <div class="page-title">@currentPageTitle</div>
        </div>

        <!-- Right Section - Page Selector -->
        <div class="nav-right">
            <div class="page-selector">
                <div class="nav-dropdown">
                    <button class="nav-dropdown-btn">
                        <span>Sélectionner une page</span>
                        <span class="dropdown-arrow">►</span>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="/PageFactureAiAnnuel">Factures AI Annuel</a>
                        <a href="/ChiffreAffaireAnnuel">Chiffre d'Affaire Annuel</a>
                        <a href="/ContratLD">Contrats LD</a>
                        <a href="/MVT-Global">Mouvement Global</a>
                        <a href="/MVT">Mouvement Véhicule</a>
                        <a href="/immo">Immobilisations</a>
                        <a href="/ecritures">Écritures</a>
                        <a href="/ListParcGlobal">Liste Parc Global</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

@code {
    private string currentPageTitle = "CarSystems";

    protected override void OnInitialized()
    {
        PageTitleService.OnTitleChanged += UpdatePageTitle;
        currentPageTitle = PageTitleService.CurrentTitle;
    }

    private void UpdatePageTitle(string newTitle)
    {
        currentPageTitle = newTitle;
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        PageTitleService.OnTitleChanged -= UpdatePageTitle;
    }
}
