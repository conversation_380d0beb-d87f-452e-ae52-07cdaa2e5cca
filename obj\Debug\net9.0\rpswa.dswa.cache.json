{"GlobalPropertiesHash": "koTOGb9GU3pH0qjjiIS+DBqRyMEnYnR9rINamzSFRlY=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Mbd1SQp5tDYhLPK65pldP6JDXvCJ8j4EgCudXwPEwNI=", "EDPlau9U0lNw5ah+5qp2MIqrMliECGx+YCgDQ/t7Oi4=", "ao5K/ubk0YY+OyTZp2mSkTjsdWfw2NFpXR2GOOWbS60=", "vTXr3BWvzb8OTJT9hRzx1YI7vszl7Zbss7bOivmqDzU=", "1h5CT/VLyXG6uEF5pdhoempLOKr7DW7n50ZNf5MyD3s=", "Y07h4LgV7pT8HDenT5krmVRjxvtRaCIrGh93LRWN7zA=", "HP3L1mb0TuTjWlYJryeHjiZ5LXAOXjmRbO1A7z13rSw=", "yNEHqA9eTM0wZzEbcyn7xAAMmxXnOK+OFFqgPOYCFqk=", "IACocD3DP1lRHbc6p0gp6dZB9I2sQbdzTse9j4kZRdM=", "mvakWnptN34lL8liYc7jLXCI1CyaKbWS54uXfKhzPDA=", "bf56QWBiyWAlXJ9xo7rnjMwS74w5/X6T+P8nw7l8z2Q=", "dxAq71PzgyL8+5Ua/ajkQxU9TRkOHVeOsUIZY2Ce8NQ=", "dSC5KhAYOuVvhohAps2XxON2m4OhGVx3Qx+pYwV9Ag8=", "pV8MykcVIaxu9n4AUgQ5R5iKKw9FhJe9+48T9OBdQQk=", "N1GQSQDGV4OiTV0CXSQFZLycLOkQy8R2QTxrNM0CCYE=", "dr4PqEpiLAe1emH6sBoS2StyyhfOVP0P8Qlrgs1WVhU=", "t+uq0kXU65YZjTu5Fk4U9b1CEMnia9qGsIvmhR+6Q1w=", "S/BgWqsYaeZDY89ERzqVyoz/YKakD6s5aiocvi4jf+U=", "D+euXgaBUY3zq3FcoAL0KqCArI93dlge9aLId/W3qYA=", "O8fx9CZMMnJNx6qOPFuKat428IdO+b0uuT62ByZeR4Y=", "QfSLVUkPP7hkKQetui/l6x3hE7B3P3XxhpoIBD3C+pA=", "L1BaFV+pYRmDhUC3GEawxwTTuZO7g2v6jtoDBXVchgo=", "muO7t2juc3bWf33QjDDFnlRCr6lg51gFCWp4+E3P8oU=", "FkIbHrYkjCyJEA85Y0P24IfImvNi2Y5MVUc1S3Ap9XI=", "D7SGqPlfPQ+ZXd0iHJgk6vgUya9MxpdRXtiWEswoh6Q=", "DvaNrgrzFSrrqC8c9+8ki+FxiYLr8d4P6m3UdgVsEOU=", "E4lvZsrqOAN27i6JdA7hEftNWtP5IFBwGyymR9j3ftM=", "rEWKFzTjb6qKmcKvYL3Xa47Hc2b3Ho7P5UF7aISjhXQ=", "1xz4Xcqe0oIHj10DjbVJen7t8Q0VEf/NayQ7a8/v+yU=", "qnLUI9Q1HSUUiY9/xY81t59KhXlCJi6g8JpteoQ9MNY=", "iUjIydFZ/NoYgznf2w2boMtLdJPVPL2AosibR+lpNJ4=", "lAoC9cy6Le1fAyhBMnbiJzkPbHuEf+LXeUX5ASGfqoA=", "peBgh4fHzOzwOjY9WICQguFd0UQ2omL+7deo24yYtY8=", "/uK3z74/r31rMBlFP+EfWxm1GuByHzqaRGUyBBfidNI=", "Fhqf3fR8vrMr4najKNjXAoeILMo8ozgYYKEbjfp6NaA=", "PBLVuJABw7ZBWE7VIOO6x8mJMm0WHjtlYYDyr4Cq1N8=", "mVoFZVlXk93t2i63xEfc4YwmN7HXAp4FvoBW2EAIs9w=", "QKhDlim47bj6yTBS/WO7o7+ID53CE9RFj8AeHjuNvm8=", "/dFzYffZ3HIQ5HGaDnQlfQogt/8YCsgTCDJa5Qh4lTY=", "LMtg9HxyYVSsQwz4sB2YaFEsxr3Lc8ouyk+46rHZXtY=", "i7OQVPFURzBWwZ6IPTI44GDEAQDgi5LPBi6zc9OtK+o=", "YunBzaDyvDm8zrTE3aPYyMxg963XOnnTzWQKwcZwn2M=", "1Cme8UeOctsEiBiwIKpCknEleouBdJHRiyESAUt7E0s=", "Y73lf3LD2lhKT2koBDIrgpbxH9gTWu+SqRvBGphtVB4=", "vfNX/ym510z2WnBeAG6GYhRfhHBkpMQ8d6F5563GCRY=", "fg7dN+JiROGO3WTM/zPAoM9v2DVsWxJk+g+6DdGVXkY=", "OpfFKwtT9mKIVVAFiyjnoklN9t7MKwIWRVE8p6GOXwk=", "q+zyhkWRbO9pdTv6DXi67lE/5b/nyVn5GfsrCJ0c2EU=", "q/X8U9haeCRLO/rxP+frAA5wFr1qdCW10svRLkLKfCY=", "NT83IsyDrL7vn2sft2w9IZYOTRA8kP0fP/betVw+x+0=", "ZpN3bGSvS/xYA0pUhnFCMZ8COFUbcgSOeNiEkyCjxsc=", "KV/yb1ZMOTVl7SRSAaZZLjnEmKbG0o1bfCZuTjq1D8Q=", "cYj3yqpDqVGAYlMLAN4ZFgpJgj4AKV+z/AOHOOhMCrE=", "xusdRvf/6iuMnMW5s5mwxpnxpXpfxkoibfKfAnwHR8U=", "gVSO8iUCwIZHor3geEYgDqCGsgmYsTkf5S98OQAyFCE=", "dOSyOtXnOUshYYKKpwIld6xYt4bB75GmW3xaAFRZ2S0=", "p+6gjtPCdT12bbONvj0adeHACrRHf21YBxjXXvKyGbk=", "2uJNGOZiQLp3A+tV2EMcJrPPCmBuqeFIcyrUKfHq6NY=", "lfU/G4Ze0JTjMEZwfhErYyAtPSL7n7FadExUk7wjUug=", "xtv4gtLUrmq3d3hJLSn+L3Ed3Huut+emVRwkSFJ5tL4=", "/JE4uv3Wj/3/6EvIKV5On3BYL5bLddd4w0lWtAeS5uA=", "Mi7by7O4zQ8QxyAUbFYelFlZ6SC2rocyJAuck7eXmc8=", "ICOZCKq+9SLRHPICWyTOhjphM6LreWgXgOFq9u/qq7I=", "T+LRsBn3C1s9hO7p597tJOvZPmzmE+5Ceu0Lw98p7/g=", "zU07xaMC6eDwGRd30SbiRcTKW7f4jMXQ72FPuex/aGk=", "fT/3Y6cCcY2uFH/j/9UizR6bLzzRN9qkMEjarMiv+H8=", "eAy1+n9iFW8Hri4DvbuoOr61S4QJPQ2QkpFPgrNf5jo=", "RfCYWAFqKrtIvW0uwhNLJID7HoUA9EvjzsrozaIqeRA=", "TGoY6Xh4uaW5Tg9l6ue8J/g/T5MzG0MPRFRazpocpzQ=", "2GwYLb+jzkyvgX3RhKT09Bugzr3p792j6LkMopERWQU=", "VpIBYV9bwA8PY7A9uWdFXwMM/Wd0wj9bHGRD9ri9mdg=", "8Ys+4JGqBTBRIPL+2bEyig2bzoGXnbR/48MmI6XkZj4=", "+4FRhgv7GDQpd+MBlQr3FREAjanOPJiCEefFMj9fr7M=", "ZYHUgvsW2PcUmxmcTjwfqRyFnFPMGGUcnnLjixmhXAc=", "PvtLvVu6tTpxaXzXQ8mJt4oqBHfxEjNN1jNbkdYH7zo=", "UA+SHAE7c2QetDNxb3U9qs1vOoPDBu02QkL667y9dgE=", "U4ZXZpwpEiaqOoiyljQSmGhghJsVDr5zWNCB8jTHc5c=", "75YRuAFZbBWO2n6LnMxq41H5Wx81uvN3pLzc0BZWl9c=", "42lExLNyGO+q86+sjzVbCq2vMowiabKOA7HVdQ5XvAE=", "CprIQIdjPrj1mtU2VQhMhwFC0jNat2nTqV1sepSygYA=", "butMRK5iFUkxAp13h6r2mpfCL7BuBvqoT6SW+klnwXA=", "wQAeFVfT68GB7ZtQahji9djRwkGmHkXQ1sha5z/oPow=", "iWCzQtVkHTuoju+5vORJWF9hAhvNx9s54tGtT85V1yo=", "lGm7NLEvVyXWmL6scqudN2TK93cVCKAcVCz7pjX6InY=", "EDtDh5PY5+qCLB4KOKE26aVGDfxxK9zQBYkHZl+bDfk=", "igaUZbUnerTz9itWFCH+HzHCxP/EY2rwwt/ODebMlFY=", "bSddfppg174cMN9Br6+kSVAd/hmukYu27r003yZ04Z8=", "s3cJWJJ3GkDw6G/bJF9g7Zr1Fohtd3DGvIdQlA8rrVs=", "iJRQetRInHbo0cDOEvrgTBgKRVrbtSbOjMmLtSuG9Hk=", "uU3s9/NTyQ4dSKtLC0/s0LkYCPN64qjmY5KpNsKuzFM=", "qwHgejriYSZO1zmnha4tSJKdcgI38VUTFuhwvRstYmM=", "vfLERQldIMfgVcCpsQFoWuXMg+/KP40pMEhekmRWQ6g=", "091yiq7X5YhsVUcdbTkmcj/lo//p404YTMRTNNnldUA=", "+iZ51XcalAQVjz+xilbDLXz7XGxvm1XwRU8kr/fwU0s=", "1Wqo6P07bOSjWqeN3aEmQzkGm5vMISNOxAT6Z2+7hSY=", "e+csxZiQzDZXw5MuJrGt8H4FV+CR/rXev69HJtf/LKU=", "vcP/nn6s5L42xI9hJMHmAXngS10hiowexxXAXXIlc74=", "xacmEWspzs5kztGNtCzSQnRC4ASonXAa3+U1vkQ3fXg=", "9l1H7Dht3gUpH4XDEOSMwD8W9CQb3dJehG2NB6hQFpk=", "OShg/0zso3qAJgluRBz88n3PmMumlqVfVnslqFPzt0I=", "rDLgSnZSrouWXW7BDg9l9UK78eCW1tCNb7Qb71TWjjY=", "YHSNbo7rDo9Sbl0N3BAEygg0L8chWcHWYGeOr3GLSz0=", "KiHpDHdkqObFZYB3kbd5iJlfyvbyQqaUByaWRxjXUAE=", "C+JGeRv8g0G5jcoD/UitWv7GydPKx5LjCBeNUf2RMaA=", "Wdq/M6nFuDtREKeuz1PvNcynD1LiSuq3WCi1F7XwpRY="], "CachedAssets": {"OpfFKwtT9mKIVVAFiyjnoklN9t7MKwIWRVE8p6GOXwk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-14T10:14:24.2072527+00:00"}, "fg7dN+JiROGO3WTM/zPAoM9v2DVsWxJk+g+6DdGVXkY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-14T10:14:24.2036024+00:00"}, "vfNX/ym510z2WnBeAG6GYhRfhHBkpMQ8d6F5563GCRY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-14T10:14:24.1999832+00:00"}, "Y73lf3LD2lhKT2koBDIrgpbxH9gTWu+SqRvBGphtVB4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-14T10:14:24.1969733+00:00"}, "1Cme8UeOctsEiBiwIKpCknEleouBdJHRiyESAUt7E0s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-14T10:14:24.1942197+00:00"}, "YunBzaDyvDm8zrTE3aPYyMxg963XOnnTzWQKwcZwn2M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-14T10:14:24.1899906+00:00"}, "i7OQVPFURzBWwZ6IPTI44GDEAQDgi5LPBi6zc9OtK+o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-14T10:14:24.1864571+00:00"}, "LMtg9HxyYVSsQwz4sB2YaFEsxr3Lc8ouyk+46rHZXtY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-14T10:14:24.1831204+00:00"}, "/dFzYffZ3HIQ5HGaDnQlfQogt/8YCsgTCDJa5Qh4lTY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-14T10:14:24.17972+00:00"}, "QKhDlim47bj6yTBS/WO7o7+ID53CE9RFj8AeHjuNvm8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-14T10:14:24.1761985+00:00"}, "mVoFZVlXk93t2i63xEfc4YwmN7HXAp4FvoBW2EAIs9w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-14T10:14:24.1730745+00:00"}, "PBLVuJABw7ZBWE7VIOO6x8mJMm0WHjtlYYDyr4Cq1N8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-14T10:14:24.1688944+00:00"}, "Fhqf3fR8vrMr4najKNjXAoeILMo8ozgYYKEbjfp6NaA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-14T10:14:24.0624192+00:00"}, "/uK3z74/r31rMBlFP+EfWxm1GuByHzqaRGUyBBfidNI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-14T10:14:24.0598772+00:00"}, "peBgh4fHzOzwOjY9WICQguFd0UQ2omL+7deo24yYtY8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-14T10:14:24.0571958+00:00"}, "lAoC9cy6Le1fAyhBMnbiJzkPbHuEf+LXeUX5ASGfqoA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-14T10:14:24.053143+00:00"}, "iUjIydFZ/NoYgznf2w2boMtLdJPVPL2AosibR+lpNJ4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-14T10:14:24.0504452+00:00"}, "qnLUI9Q1HSUUiY9/xY81t59KhXlCJi6g8JpteoQ9MNY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-14T10:14:24.0463548+00:00"}, "1xz4Xcqe0oIHj10DjbVJen7t8Q0VEf/NayQ7a8/v+yU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-14T10:14:24.0432435+00:00"}, "rEWKFzTjb6qKmcKvYL3Xa47Hc2b3Ho7P5UF7aISjhXQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-14T10:14:24.0391109+00:00"}, "E4lvZsrqOAN27i6JdA7hEftNWtP5IFBwGyymR9j3ftM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-14T10:14:24.035782+00:00"}, "DvaNrgrzFSrrqC8c9+8ki+FxiYLr8d4P6m3UdgVsEOU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-14T10:14:24.0341356+00:00"}, "D7SGqPlfPQ+ZXd0iHJgk6vgUya9MxpdRXtiWEswoh6Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-14T10:14:24.0312786+00:00"}, "FkIbHrYkjCyJEA85Y0P24IfImvNi2Y5MVUc1S3Ap9XI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-14T10:14:24.029327+00:00"}, "muO7t2juc3bWf33QjDDFnlRCr6lg51gFCWp4+E3P8oU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-14T10:14:24.0267217+00:00"}, "L1BaFV+pYRmDhUC3GEawxwTTuZO7g2v6jtoDBXVchgo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-14T10:14:24.0238533+00:00"}, "QfSLVUkPP7hkKQetui/l6x3hE7B3P3XxhpoIBD3C+pA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-14T10:14:24.0207875+00:00"}, "O8fx9CZMMnJNx6qOPFuKat428IdO+b0uuT62ByZeR4Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-14T10:14:24.0168021+00:00"}, "D+euXgaBUY3zq3FcoAL0KqCArI93dlge9aLId/W3qYA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-14T10:14:24.0141501+00:00"}, "S/BgWqsYaeZDY89ERzqVyoz/YKakD6s5aiocvi4jf+U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-14T10:14:24.0104592+00:00"}, "t+uq0kXU65YZjTu5Fk4U9b1CEMnia9qGsIvmhR+6Q1w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-14T10:14:24.0064762+00:00"}, "dr4PqEpiLAe1emH6sBoS2StyyhfOVP0P8Qlrgs1WVhU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-14T10:14:23.9983634+00:00"}, "N1GQSQDGV4OiTV0CXSQFZLycLOkQy8R2QTxrNM0CCYE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-14T10:14:23.9956642+00:00"}, "pV8MykcVIaxu9n4AUgQ5R5iKKw9FhJe9+48T9OBdQQk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-14T10:14:23.9883609+00:00"}, "dSC5KhAYOuVvhohAps2XxON2m4OhGVx3Qx+pYwV9Ag8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-14T10:14:23.9839886+00:00"}, "dxAq71PzgyL8+5Ua/ajkQxU9TRkOHVeOsUIZY2Ce8NQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-14T10:14:23.976499+00:00"}, "bf56QWBiyWAlXJ9xo7rnjMwS74w5/X6T+P8nw7l8z2Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-14T10:14:23.9692255+00:00"}, "mvakWnptN34lL8liYc7jLXCI1CyaKbWS54uXfKhzPDA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-14T10:14:23.9622666+00:00"}, "IACocD3DP1lRHbc6p0gp6dZB9I2sQbdzTse9j4kZRdM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-14T10:14:23.9562769+00:00"}, "yNEHqA9eTM0wZzEbcyn7xAAMmxXnOK+OFFqgPOYCFqk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-14T10:14:23.9490705+00:00"}, "HP3L1mb0TuTjWlYJryeHjiZ5LXAOXjmRbO1A7z13rSw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-14T10:14:23.9419983+00:00"}, "Y07h4LgV7pT8HDenT5krmVRjxvtRaCIrGh93LRWN7zA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-14T10:14:23.9338507+00:00"}, "1h5CT/VLyXG6uEF5pdhoempLOKr7DW7n50ZNf5MyD3s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-14T10:14:23.927835+00:00"}, "vTXr3BWvzb8OTJT9hRzx1YI7vszl7Zbss7bOivmqDzU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-14T10:14:23.9218474+00:00"}, "ao5K/ubk0YY+OyTZp2mSkTjsdWfw2NFpXR2GOOWbS60=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\favicon.png", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-07-14T10:14:23.9154285+00:00"}, "Mbd1SQp5tDYhLPK65pldP6JDXvCJ8j4EgCudXwPEwNI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\app.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khy4lop6wu", "Integrity": "aNPcLFwdCCGS2v1guSR64Htd4Ly5uclT7taAptnMPbs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2900, "LastWriteTime": "2025-07-14T10:14:23.909961+00:00"}, "EDPlau9U0lNw5ah+5qp2MIqrMliECGx+YCgDQ/t7Oi4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\css\\filters.css", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "css/filters#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rnbh6v1352", "Integrity": "R8emYTG3S9tITAQaWa1l5IQTQpTdQt+GXtN1GUv2Cwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\filters.css", "FileLength": 5622, "LastWriteTime": "2025-07-27T22:30:51.1511052+00:00"}}, "CachedCopyCandidates": {}}