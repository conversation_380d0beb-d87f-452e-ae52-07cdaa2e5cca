@page "/immo"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using CarSystemsWebApp.Services
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection
@using Syncfusion.Drawing

@inject IJSRuntime      _jsRuntime
@inject SfDialogService _dialogService
@inject PageTitleService PageTitleService



<!-- ============== -->
<!-- PROFESSIONAL FILTER BAR -->
<!-- ============== -->

<div class="professional-filter-container">
    <div class="filter-header">
        <div class="filter-title">Filtres</div>
        <button class="btn-clear-all" @onclick="ClearAllFilters">Effacer tout</button>
    </div>

    <div class="row filter-row align-items-center">
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedCodeParc" ShowClearButton="true" Placeholder="Code parc..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date min achat..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateMinAchat"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date max achat..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateMaxAchat"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date min amortissement..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateMinAmortissement"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date max amortissement..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateMaxAmortissement"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfButton Type="button" @onclick="Helper_Refresh" CssClass="btn-inline">
                Appliquer
            </SfButton>
        </div>
        <div class="col-auto">
            <SfButton OnClick="OnButtonExport" CssClass="btn-inline-export">
                Export
            </SfButton>
        </div>
    </div>
</div>

<!-- ======= -->
<!-- RESULTS -->
<!-- ======= -->

@* <SfToolbar>
    <ToolbarItems>

        <ToolbarItem Id=HRRefreshGrid
        PrefixIcon="e-icons e-refresh"
        Tooltiptext="Actualiser"
        OnClick="OnToolbarButtonRefresh" />

        <ToolbarItem Type=ItemType.Separator />

    </ToolbarItems>
</SfToolbar> *@

@* style="height: calc(100vh - 205px) !important;" *@

<div>
    <SfGrid ID="MyGrid" 
    TValue="CS_IMMO_ITEM"
    @ref="@MyGrid"
    Query="@MyQuery"
    AllowPaging="true"
    AllowFiltering="false"
    AllowSorting="true"
    AllowResizing="true" EnablePersistence="true"
    ShowColumnChooser="true" Toolbar=@ToolbarItems
    EnableInfiniteScrolling="false"
    Height="100%">
        <GridEvents TValue="CS_IMMO_ITEM"
        RowSelected="OnRowSelected" 
        OnDataBound="DataBoundHandler"
        OnActionBegin="OnActionBegin"
        OnActionComplete="OnActionComplete"/>
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="22" />
        <GridColumns>
            <GridColumn Field=@nameof(CS_IMMO_ITEM.numimmo) HeaderText="numimmo" />
            <GridColumn Field=@nameof(CS_IMMO_ITEM.codeparc) HeaderText="codeparc" />
            <GridColumn Field=@nameof(CS_IMMO_ITEM.dateachat) HeaderText="dateachat" Format="dd/MM/yyyy" Type=ColumnType.DateOnly Width="145" />
            <GridColumn Field=@nameof(CS_IMMO_ITEM.libelle) HeaderText="libelle" />
            <GridColumn Field=@nameof(CS_IMMO_ITEM.baseamort) HeaderText="baseamort" />
            <GridColumn Field=@nameof(CS_IMMO_ITEM.datedepamort) HeaderText="datedepamort" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_IMMO_ITEM.amortexe) HeaderText="amortexe" />
            <GridColumn Field=@nameof(CS_IMMO_ITEM.valresdercalcul) HeaderText="valresdercalcul" />            
        </GridColumns>
    </SfGrid>
</div>

<script type="text/javascript">

    function saveAsFile(filename, bytesBase64) {
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++) {
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_IMMO_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;

    private DateTime?   SelectedDateMinAchat;
    private DateTime?   SelectedDateMaxAchat;
    private DateTime?   SelectedDateMinAmortissement;
    private DateTime?   SelectedDateMaxAmortissement;
    private string      SelectedCodeParc;


    protected override void OnInitialized()
    {
        // Set page title
        PageTitleService.SetTitle("Immobilisations");

        this.MyQuery = new Query()
            .AddParams("DateMinAchat", null)
            .AddParams("DateMaxAchat", null)
            .AddParams("DateMinAmortissement", null)
            .AddParams("DateMaxAmortissement", null)
            .AddParams("CodeParc", null);

        // this.Codes = new List<string>() {
        //     "WA", "AS", "AN", "EF", "OD",
        //     "FE", "CA", "EN", "V1", "DE",
        //     "PA", "FO", "BQ", "PR", "CV",
        //     "VV", "FR", "FV", "AP", "FL", "VT"
        // };

        // this.Codes.Sort();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_IMMO_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_IMMO_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_IMMO_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_IMMO_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["DateMinAchat"]          = this.SelectedDateMinAchat;
        MyQuery.Queries.Params["DateMaxAchat"]          = this.SelectedDateMaxAchat;
        MyQuery.Queries.Params["DateMinAmortissement"]  = this.SelectedDateMinAmortissement;
        MyQuery.Queries.Params["DateMaxAmortissement"]  = this.SelectedDateMaxAmortissement;
        MyQuery.Queries.Params["CodeParc"]              = this.SelectedCodeParc;

        // Reset pagination to first page when applying new filters
        await MyGrid.GoToPageAsync(1);
        await MyGrid.Refresh();
    }

    private async Task ClearAllFilters()
    {
        SelectedDateMinAchat = null;
        SelectedDateMaxAchat = null;
        SelectedDateMinAmortissement = null;
        SelectedDateMaxAmortissement = null;
        SelectedCodeParc = null;
        await Helper_Refresh();
    }

    private async void OnButtonExport()
    {
        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // Create a workbook with 1 worksheet
            IWorkbook workbook = application.Workbooks.Create(1);
            IWorksheet worksheet = workbook.Worksheets[0];
            worksheet.Name = "IMMOBILISATIONS";

            // Get data for export (large limit to get all data)
            string sortcolumn = "code_societe";
            string sortdir = "ascending";

            var items = MyCustomAdaptor.Helper_GetData(
                this.SelectedDateMinAchat,
                this.SelectedDateMaxAchat,
                this.SelectedDateMinAmortissement,
                this.SelectedDateMaxAmortissement,
                this.SelectedCodeParc,
                0, 100000,
                sortcolumn, sortdir, 900); // 15 minute timeout for export

            // Create a professional header design with WafaLLD orange branding
            // Main title with orange background
            worksheet["A1"].Text = "EXPORT IMMOBILISATIONS";
            worksheet["A1"].CellStyle.Font.Bold = true;
            worksheet["A1"].CellStyle.Font.Size = 16;
            worksheet["A1"].CellStyle.Font.Color = ExcelKnownColors.White;
            worksheet["A1"].CellStyle.Color = Color.FromArgb(255, 140, 0); // WafaLLD Orange background
            worksheet["A1"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            worksheet["A1"].CellStyle.VerticalAlignment = ExcelVAlign.VAlignCenter;
            worksheet.Range["A1:R1"].Merge();
            worksheet.Range["A1:R1"].RowHeight = 25;

            // Export info with light orange background
            worksheet["A2"].Text = $"Exporté le: {DateTime.Now:dd/MM/yyyy HH:mm}";
            worksheet["A2"].CellStyle.Font.Size = 10;
            worksheet["A2"].CellStyle.Font.Italic = true;
            worksheet["A2"].CellStyle.Color = Color.FromArgb(255, 228, 196); // Light orange background
            worksheet["A2"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A2:R2"].Merge();

            // Add filter criteria info
            string filterInfo = "Filtres appliqués: ";
            if (SelectedDateMinAchat.HasValue)
            {
                filterInfo += $"Date Min Achat: {SelectedDateMinAchat?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (SelectedDateMaxAchat.HasValue)
            {
                filterInfo += $"Date Max Achat: {SelectedDateMaxAchat?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (SelectedDateMinAmortissement.HasValue)
            {
                filterInfo += $"Date Min Amortissement: {SelectedDateMinAmortissement?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (SelectedDateMaxAmortissement.HasValue)
            {
                filterInfo += $"Date Max Amortissement: {SelectedDateMaxAmortissement?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (!string.IsNullOrEmpty(SelectedCodeParc))
            {
                filterInfo += $"Code Parc: {SelectedCodeParc} | ";
            }
            if (filterInfo.EndsWith(" | "))
            {
                filterInfo = filterInfo.Substring(0, filterInfo.Length - 3);
            }
            else if (filterInfo == "Filtres appliqués: ")
            {
                filterInfo = "Aucun filtre appliqué";
            }

            worksheet["A3"].Text = filterInfo;
            worksheet["A3"].CellStyle.Font.Size = 9;
            worksheet["A3"].CellStyle.Color = Color.FromArgb(255, 248, 240); // Very light orange/cream
            worksheet["A3"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A3:R3"].Merge();

            // Add a separator line with orange accent
            worksheet.Range["A4:R4"].CellStyle.Color = Color.FromArgb(255, 165, 0); // Orange accent line
            worksheet.Range["A4:R4"].RowHeight = 3;

            // Convert to DataTable for Excel export
            DataTable table = new DataTable();
            Type type = typeof(CS_IMMO_ITEM);
            PropertyInfo[] properties = type.GetProperties();

            // Create columns
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);

            // Add data rows
            foreach (var item in items)
            {
                DataRow row = table.NewRow();
                int k = 0;
                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }
                table.Rows.Add(row);
            }

            // Export data to Excel worksheet starting from row 6 to leave space for header design
            worksheet.ImportDataTable(table, true, 6, 1);

            // Style the data header row (column names) with orange theme
            IRange headerRange = worksheet.Range[6, 1, 6, properties.Length];
            headerRange.CellStyle.Font.Bold = true;
            headerRange.CellStyle.Font.Color = ExcelKnownColors.White;
            headerRange.CellStyle.Color = Color.FromArgb(255, 165, 0); // Medium orange background
            headerRange.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
            headerRange.RowHeight = 20;

            // Add alternating row colors and borders to all data
            if (items.Count > 0)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    int rowIndex = 7 + i; // Start from row 7 (after header)
                    IRange rowRange = worksheet.Range[rowIndex, 1, rowIndex, properties.Length];

                    // Alternating row colors with orange theme
                    if (i % 2 == 0)
                    {
                        rowRange.CellStyle.Color = Color.FromArgb(255, 250, 240); // Very light orange/cream
                    }
                    else
                    {
                        rowRange.CellStyle.Color = Color.White;
                    }

                    // Add borders
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
                }
            }

            worksheet.UsedRange.AutofitColumns();

            // Save and download
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                string filename = $"ExportImmobilisations_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "IMMO-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                List<CS_IMMO_ITEM> empty;
                empty = Enumerable.Empty<CS_IMMO_ITEM>().ToList();

                if (dm.Params is null || dm.Params.Count == 0)
                {
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Nothing to do", true);
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                    return dm.RequiresCounts ? new DataResult() { Result = empty, Count = 0 } : (object)empty;
                }

                var dateMinAchat            = dm.Params["DateMinAchat"] as DateTime?;
                var dateMaxAchat            = dm.Params["DateMaxAchat"] as DateTime?;
                var dateMinAmortissement    = dm.Params["DateMinAmortissement"] as DateTime?;
                var dateMaxAmortissement    = dm.Params["DateMaxAmortissement"] as DateTime?;
                var codeParc                = dm.Params["CodeParc"] as string;
                
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMinAchat = {dateMinAchat}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMaxAchat = {dateMaxAchat}", true);

                // string sortcolumn   = "code_societe";  // OLD: Hardcoded sorting
                // string sortdir      = "ascending";     // OLD: Hardcoded sorting

                // NEW: Dynamic sorting from DataManagerRequest
                string sortcolumn   = "codeparc";  // Default sort column
                string sortdir      = "ascending";  // Default sort direction

                // Extract sorting information from DataManagerRequest
                if (dm.Sorted != null && dm.Sorted.Count > 0)
                {
                    var sortInfo = dm.Sorted[0]; // Get the first sort column
                    sortcolumn = sortInfo.Name;
                    sortdir = sortInfo.Direction;
                }

                CarSystemsContext model;
                model = new CarSystemsContext();

                // Format dates properly for SQL Server
                object dateMinAchatParam = dateMinAchat.HasValue ? (object)dateMinAchat.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateMaxAchatParam = dateMaxAchat.HasValue ? (object)dateMaxAchat.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateMinAmortissementParam = dateMinAmortissement.HasValue ? (object)dateMinAmortissement.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateMaxAmortissementParam = dateMaxAmortissement.HasValue ? (object)dateMaxAmortissement.Value.ToString("yyyy-MM-dd") : DBNull.Value;

                var items = Helper_GetData(dateMinAchat, dateMaxAchat, dateMinAmortissement, dateMaxAmortissement, codeParc, dm.Skip, dm.Take, sortcolumn, sortdir);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetImmoItemsCount_V2 {0}, {1}, {2}, {3}, {4}",
                        dateMinAchatParam,
                        dateMaxAchatParam,
                        dateMinAmortissementParam,
                        dateMaxAmortissementParam,
                        codeParc)
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);

                return null;
            }
        }

        public static List<CS_IMMO_ITEM> Helper_GetData(DateTime? dateMinAchat, DateTime? dateMaxAchat, DateTime? dateMinAmortissement, DateTime? dateMaxAmortissement, string codeParc, int skip, int take, string sortcolumn, string sortdir, int timeoutSeconds = 30)
        {
            CarSystemsContext model;
            model = new CarSystemsContext();

            // Set command timeout for long-running operations
            model.Database.SetCommandTimeout(timeoutSeconds);

            // Format dates properly for SQL Server
            object dateMinAchatParam = dateMinAchat.HasValue ? (object)dateMinAchat.Value.ToString("yyyy-MM-dd") : DBNull.Value;
            object dateMaxAchatParam = dateMaxAchat.HasValue ? (object)dateMaxAchat.Value.ToString("yyyy-MM-dd") : DBNull.Value;
            object dateMinAmortissementParam = dateMinAmortissement.HasValue ? (object)dateMinAmortissement.Value.ToString("yyyy-MM-dd") : DBNull.Value;
            object dateMaxAmortissementParam = dateMaxAmortissement.HasValue ? (object)dateMaxAmortissement.Value.ToString("yyyy-MM-dd") : DBNull.Value;

            var items = model.CS_IMMO_ITEMS.FromSqlRaw("EXECUTE dbo.GetImmoItems_V2 {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}",
                dateMinAchatParam,
                dateMaxAchatParam,
                dateMinAmortissementParam,
                dateMaxAmortissementParam,
                codeParc,
                skip,
                take,
                sortcolumn,
                sortdir).ToList();

            return items;
        }
    }
}
