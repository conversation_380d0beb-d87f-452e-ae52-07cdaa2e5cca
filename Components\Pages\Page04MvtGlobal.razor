@page "/MVT-Global"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using CarSystemsWebApp.Services
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection
@using Syncfusion.Drawing

@inject IJSRuntime      _jsRuntime
@inject SfDialogService _dialogService
@inject PageTitleService PageTitleService



<!-- ============== -->
<!-- PROFESSIONAL FILTER BAR -->
<!-- ============== -->

<div class="professional-filter-container">
    <div class="filter-header">
        <div class="filter-title">Filtres</div>
        <button class="btn-clear-all" @onclick="ClearAllFilters">Effacer tout</button>
    </div>

    <!-- First Row: Text Filters -->
    <div class="row filter-row align-items-center">
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedCodeParc" ShowClearButton="true" Placeholder="Code parc..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedTypee" ShowClearButton="true" Placeholder="Type..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedDureeSejour" ShowClearButton="true" Placeholder="Durée séjour..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">           
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date début..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateDebut"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date fin..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateFin"
                         Width="240px"></SfDatePicker>
        </div>
    </div>

    <!-- Second Row: Date Filters and Actions -->
    <div class="row filter-row align-items-center" style="margin-top: 12px;">
        
        <div class="col-auto">
            <SfButton Type="button" @onclick="Helper_Refresh" CssClass="btn-inline">
                Appliquer
            </SfButton>
        </div>
        <div class="col-auto">
            <SfButton OnClick="OnButtonExport" CssClass="btn-inline-export">
                Export
            </SfButton>
        </div>
    </div>
</div>

<!-- ======= -->
<!-- RESULTS -->
<!-- ======= -->

@* <SfToolbar>
    <ToolbarItems>

        <ToolbarItem Id=HRRefreshGrid
        PrefixIcon="e-icons e-refresh"
        Tooltiptext="Actualiser"
        OnClick="OnToolbarButtonRefresh" />

        <ToolbarItem Type=ItemType.Separator />

    </ToolbarItems>
</SfToolbar> *@

@* style="height: calc(100vh - 205px) !important;" *@

<div>
    <SfGrid ID="MyGrid"
            TValue="CS_MVT_ITEM"
    @ref="@MyGrid"
    Query="@MyQuery"
    AllowPaging="true"
    AllowFiltering="false"
    AllowSorting="true"
    AllowResizing="true" EnablePersistence="true"
    ShowColumnChooser="true" Toolbar=@ToolbarItems
    EnableInfiniteScrolling="false"
    Height="100%">
        <GridEvents TValue="CS_MVT_ITEM"
        RowSelected="OnRowSelected" 
        OnDataBound="DataBoundHandler"
        OnActionBegin="OnActionBegin"
        OnActionComplete="OnActionComplete"/>
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="22" />
        <GridColumns>
            <GridColumn Field="@nameof(CS_MVT_ITEM.nummvt)" HeaderText="nummvt" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.codeparc)" HeaderText="codeparc" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.etat)" HeaderText="etat" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.typee)" HeaderText="typee" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.refmvt)" HeaderText="refmvt" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.numldct)" HeaderText="numldct" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.codeclient)" HeaderText="codeclient" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.numldanx)" HeaderText="numldanx" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.typecmdent)" HeaderText="typecmdent" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.numcmdent)" HeaderText="numcmdent" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.datedeb)" HeaderText="datedeb" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field="@nameof(CS_MVT_ITEM.heuredeb)" HeaderText="heuredeb" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.dateheuredeb)" HeaderText="dateheuredeb" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.codeagencedep)" HeaderText="codeagencedep" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.lieudep)" HeaderText="lieudep" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.relevecptdeb)" HeaderText="relevecptdeb" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.motifdeb)" HeaderText="motifdeb" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.niveaudeb)" HeaderText="niveaudeb" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.datefin)" HeaderText="datefin" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field="@nameof(CS_MVT_ITEM.heurefin)" HeaderText="heurefin" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.codeagenceret)" HeaderText="codeagenceret" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.lieuret)" HeaderText="lieuret" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.relevecptfin)" HeaderText="relevecptfin" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.motiffin)" HeaderText="motiffin" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.niveaufin)" HeaderText="niveaufin" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.datecrea)" HeaderText="datecrea" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field="@nameof(CS_MVT_ITEM.datemodi)" HeaderText="datemodi" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field="@nameof(CS_MVT_ITEM.codeutiliscrea)" HeaderText="codeutiliscrea" />
            <GridColumn Field="@nameof(CS_MVT_ITEM.codeutilismodi)" HeaderText="codeutilismodi" />
        </GridColumns>
        
    </SfGrid>

</div>

<script type="text/javascript">

    function saveAsFile(filename, bytesBase64) {
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++) {
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_MVT_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;

   
    private string SelectedCodeParc;
    private string SelectedTypee;
    private DateTime? SelectedDateDebut;
    private DateTime? SelectedDateFin;
    private string SelectedDureeSejour;

    protected override void OnInitialized()
    {
        // Set page title
        PageTitleService.SetTitle("Mouvement Global");

        this.MyQuery = new Query()
            .AddParams("CodeParc", null)
            .AddParams("Typee", null)
            .AddParams("DateDebut", null)
            .AddParams("DateFin", null)
            .AddParams("DureeSejour", null);
        // this.Codes = new List<string>() {
        //     "WA", "AS", "AN", "EF", "OD",
        //     "FE", "CA", "EN", "V1", "DE",
        //     "PA", "FO", "BQ", "PR", "CV",
        //     "VV", "FR", "FV", "AP", "FL", "VT"
        // };

        // this.Codes.Sort();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_MVT_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_MVT_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_MVT_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_MVT_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["CodeParc"]               = this.SelectedCodeParc;
        MyQuery.Queries.Params["Typee"]                  = this.SelectedTypee;
        MyQuery.Queries.Params["DateDebut"]              = this.SelectedDateDebut;
        MyQuery.Queries.Params["DateFin"]                = this.SelectedDateFin;
        MyQuery.Queries.Params["DureeSejour"]            = this.SelectedDureeSejour;

        // Reset pagination to first page when applying new filters
        await MyGrid.GoToPageAsync(1);
        await MyGrid.Refresh();
    }

    private async Task ClearAllFilters()
    {
        SelectedCodeParc = null;
        SelectedTypee = null;
        SelectedDateDebut = null;
        SelectedDateFin = null;
        SelectedDureeSejour = null;
        await Helper_Refresh();
    }

    private async void OnButtonExport()
    {
        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // Create a workbook with 1 worksheet
            IWorkbook workbook = application.Workbooks.Create(1);
            IWorksheet worksheet = workbook.Worksheets[0];
            worksheet.Name = "MVT_GLOBAL";

            // Get data for export (large limit to get all data)
            string sortcolumn = "code_societe";
            string sortdir = "ascending";

            var items = MyCustomAdaptor.Helper_GetData(
                this.SelectedDateDebut,
                this.SelectedDateFin,
                this.SelectedCodeParc,
                this.SelectedTypee,
                this.SelectedDureeSejour,
                0, 100000,
                sortcolumn, sortdir, 900); // 15 minute timeout for export

            // Create a professional header design with WafaLLD orange branding
            // Main title with orange background
            worksheet["A1"].Text = "EXPORT MOUVEMENTS GLOBAL";
            worksheet["A1"].CellStyle.Font.Bold = true;
            worksheet["A1"].CellStyle.Font.Size = 16;
            worksheet["A1"].CellStyle.Font.Color = ExcelKnownColors.White;
            worksheet["A1"].CellStyle.Color = Color.FromArgb(255, 140, 0); // WafaLLD Orange background
            worksheet["A1"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            worksheet["A1"].CellStyle.VerticalAlignment = ExcelVAlign.VAlignCenter;
            worksheet.Range["A1:R1"].Merge();
            worksheet.Range["A1:R1"].RowHeight = 25;

            // Export info with light orange background
            worksheet["A2"].Text = $"Exporté le: {DateTime.Now:dd/MM/yyyy HH:mm}";
            worksheet["A2"].CellStyle.Font.Size = 10;
            worksheet["A2"].CellStyle.Font.Italic = true;
            worksheet["A2"].CellStyle.Color = Color.FromArgb(255, 228, 196); // Light orange background
            worksheet["A2"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A2:R2"].Merge();

            // Add filter criteria info
            string filterInfo = "Filtres appliqués: ";
            if (SelectedDateDebut.HasValue)
            {
                filterInfo += $"Date Début: {SelectedDateDebut?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (SelectedDateFin.HasValue)
            {
                filterInfo += $"Date Fin: {SelectedDateFin?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (!string.IsNullOrEmpty(SelectedCodeParc))
            {
                filterInfo += $"Code Parc: {SelectedCodeParc} | ";
            }
            if (!string.IsNullOrEmpty(SelectedTypee))
            {
                filterInfo += $"Type: {SelectedTypee} | ";
            }
            if (!string.IsNullOrEmpty(SelectedDureeSejour))
            {
                filterInfo += $"Durée Séjour: {SelectedDureeSejour} | ";
            }
            if (filterInfo.EndsWith(" | "))
            {
                filterInfo = filterInfo.Substring(0, filterInfo.Length - 3);
            }
            else if (filterInfo == "Filtres appliqués: ")
            {
                filterInfo = "Aucun filtre appliqué";
            }

            worksheet["A3"].Text = filterInfo;
            worksheet["A3"].CellStyle.Font.Size = 9;
            worksheet["A3"].CellStyle.Color = Color.FromArgb(255, 248, 240); // Very light orange/cream
            worksheet["A3"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A3:R3"].Merge();

            // Add a separator line with orange accent
            worksheet.Range["A4:R4"].CellStyle.Color = Color.FromArgb(255, 165, 0); // Orange accent line
            worksheet.Range["A4:R4"].RowHeight = 3;

            // Convert to DataTable for Excel export
            DataTable table = new DataTable();
            Type type = typeof(CS_MVT_ITEM);
            PropertyInfo[] properties = type.GetProperties();

            // Create columns
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);

            // Add data rows
            foreach (var item in items)
            {
                DataRow row = table.NewRow();
                int k = 0;
                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }
                table.Rows.Add(row);
            }

            // Export data to Excel worksheet starting from row 6 to leave space for header design
            worksheet.ImportDataTable(table, true, 6, 1);

            // Style the data header row (column names) with orange theme
            IRange headerRange = worksheet.Range[6, 1, 6, properties.Length];
            headerRange.CellStyle.Font.Bold = true;
            headerRange.CellStyle.Font.Color = ExcelKnownColors.White;
            headerRange.CellStyle.Color = Color.FromArgb(255, 165, 0); // Medium orange background
            headerRange.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
            headerRange.RowHeight = 20;

            // Add alternating row colors and borders to all data
            if (items.Count > 0)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    int rowIndex = 7 + i; // Start from row 7 (after header)
                    IRange rowRange = worksheet.Range[rowIndex, 1, rowIndex, properties.Length];

                    // Alternating row colors with orange theme
                    if (i % 2 == 0)
                    {
                        rowRange.CellStyle.Color = Color.FromArgb(255, 250, 240); // Very light orange/cream
                    }
                    else
                    {
                        rowRange.CellStyle.Color = Color.White;
                    }

                    // Add borders
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
                }
            }

            worksheet.UsedRange.AutofitColumns();

            // Save and download
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                string filename = $"ExportMvtGlobal_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "MVTGLOBAL-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                List<CS_MVT_ITEM> empty;
                empty = Enumerable.Empty<CS_MVT_ITEM>().ToList();

                if (dm.Params is null || dm.Params.Count == 0)
                {
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Nothing to do", true);
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                    return dm.RequiresCounts ? new DataResult() { Result = empty, Count = 0 } : (object)empty;
                }

                var codeParc    = dm.Params["CodeParc"] as string;
                var typee       = dm.Params["Typee"] as string;
                var dateDebut   = dm.Params["DateDebut"] as DateTime?;
                var dateFin     = dm.Params["DateFin"] as DateTime?;
                var dureeSejour = dm.Params["DureeSejour"] as string;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"CodeParc = {codeParc}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Typee = {typee}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateDebut = {dateDebut}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateFin = {dateFin}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DureeSejour = {dureeSejour}", true);

                // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
                object dateDebutParam = dateDebut.HasValue ? (object)dateDebut.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateFinParam = dateFin.HasValue ? (object)dateFin.Value.ToString("yyyy-MM-dd") : DBNull.Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateDebutParam = {dateDebutParam}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateFinParam = {dateFinParam}", true);

                // string sortcolumn   = "code_societe";  // OLD: Hardcoded sorting
                // string sortdir      = "ascending";     // OLD: Hardcoded sorting

                // NEW: Dynamic sorting from DataManagerRequest
                string sortcolumn   = "codeparc";  // Default sort column
                string sortdir      = "ascending";  // Default sort direction

                // Extract sorting information from DataManagerRequest
                if (dm.Sorted != null && dm.Sorted.Count > 0)
                {
                    var sortInfo = dm.Sorted[0]; // Get the first sort column
                    sortcolumn = sortInfo.Name;
                    sortdir = sortInfo.Direction;
                }

                CarSystemsContext model;
                model = new CarSystemsContext();

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Phase 1", true);

                var items = Helper_GetData(dateDebut, dateFin, codeParc, typee, dureeSejour, dm.Skip, dm.Take, sortcolumn, sortdir);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Phase 2", true);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetmvtGlobalItemsCount_V2 {0},{1},{2},{3},{4}", codeParc, typee, dateDebutParam, dateFinParam, dureeSejour)
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);
                return null;
            }
        }

        public static List<CS_MVT_ITEM> Helper_GetData(DateTime? dateStart, DateTime? dateEnd, string codeParc, string typee, string dureeSejour, int skip, int take, string sortcolumn, string sortdir, int timeoutSeconds = 30)
        {
            var logKey = "MVTGLOBAL-HELPER_GETDATA";
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateStart = {dateStart}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateEnd = {dateEnd}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"codeParc = {codeParc}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"typee = {typee}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dureeSejour = {dureeSejour}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortcolumn = {sortcolumn}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortdir = {sortdir}", true);

            CarSystemsContext model;
            model = new CarSystemsContext();

            // Set command timeout for long-running operations
            model.Database.SetCommandTimeout(timeoutSeconds);

            // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
            object dateStartParam = dateStart.HasValue ? (object)dateStart.Value.ToString("yyyy-MM-dd") : DBNull.Value;
            object dateEndParam = dateEnd.HasValue ? (object)dateEnd.Value.ToString("yyyy-MM-dd") : DBNull.Value;

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateStartParam = {dateStartParam}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateEndParam = {dateEndParam}", true);

            var items = model.CS_MVT_ITEMS.FromSqlRaw("EXECUTE dbo.GetmvtGlobalItems_V2 {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}",
                codeParc,
                typee,
                dateStartParam,
                dateEndParam,
                dureeSejour,
                skip,
                take,
                sortcolumn,
                sortdir).ToList();

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Retrieved {items.Count} items", true);
            return items;
        }
    }
}
