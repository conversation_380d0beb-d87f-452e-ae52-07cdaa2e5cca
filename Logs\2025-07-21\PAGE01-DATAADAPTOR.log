21/07/2025 : 08:00:53.254 : Entering
21/07/2025 : 08:00:53.257 :     Client           = 
21/07/2025 : 08:00:53.258 :     DateFactureStart = 
21/07/2025 : 08:00:53.259 :     DateFactureEnd   = 
21/07/2025 : 08:00:53.259 :     NO SORTING: Using default sort
21/07/2025 : 08:00:56.368 :     Items retrieved: 100
21/07/2025 : 08:01:04.532 :     Count = 8832132
21/07/2025 : 08:01:04.533 : Leaving
21/07/2025 : 08:01:05.796 : Entering
21/07/2025 : 08:01:05.797 :     Client           = 
21/07/2025 : 08:01:05.797 :     DateFactureStart = 
21/07/2025 : 08:01:05.798 :     DateFactureEnd   = 
21/07/2025 : 08:01:05.800 :     NO SORTING: Using default sort
21/07/2025 : 08:01:06.037 :     Items retrieved: 100
21/07/2025 : 08:01:08.524 :     Count = 8832132
21/07/2025 : 08:01:08.525 : Leaving
21/07/2025 : 08:14:07.174 : Entering
21/07/2025 : 08:14:07.179 :     Client           = 
21/07/2025 : 08:14:07.180 :     DateFactureStart = 
21/07/2025 : 08:14:07.182 :     DateFactureEnd   = 
21/07/2025 : 08:14:10.461 :     Count = 8832132
21/07/2025 : 08:14:10.462 : Leaving
21/07/2025 : 08:14:10.678 : Entering
21/07/2025 : 08:14:10.679 :     Client           = 
21/07/2025 : 08:14:10.680 :     DateFactureStart = 
21/07/2025 : 08:14:10.681 :     DateFactureEnd   = 
21/07/2025 : 08:14:13.327 :     Count = 8832132
21/07/2025 : 08:14:13.328 : Leaving
21/07/2025 : 08:14:15.761 : Entering
21/07/2025 : 08:14:15.762 :     Client           = 
21/07/2025 : 08:14:15.765 :     DateFactureStart = 
21/07/2025 : 08:14:15.766 :     DateFactureEnd   = 
21/07/2025 : 08:14:21.537 :     Count = 8832132
21/07/2025 : 08:14:21.538 : Leaving
21/07/2025 : 08:14:26.811 : Entering
21/07/2025 : 08:14:26.812 :     Client           = 
21/07/2025 : 08:14:26.816 :     DateFactureStart = 
21/07/2025 : 08:14:26.818 :     DateFactureEnd   = 
21/07/2025 : 08:14:29.917 :     Count = 8832132
21/07/2025 : 08:14:29.917 : Leaving
21/07/2025 : 08:28:38.217 : Entering
21/07/2025 : 08:28:38.218 :     Client           = 
21/07/2025 : 08:28:38.218 :     DateFactureStart = 
21/07/2025 : 08:28:38.219 :     DateFactureEnd   = 
21/07/2025 : 08:28:38.219 :     NO SORTING: Using default sort
21/07/2025 : 08:28:38.345 :     Items retrieved: 100
21/07/2025 : 08:28:41.947 :     Count = 8832132
21/07/2025 : 08:28:41.947 : Leaving
21/07/2025 : 08:28:42.270 : Entering
21/07/2025 : 08:28:42.270 :     Client           = 
21/07/2025 : 08:28:42.271 :     DateFactureStart = 
21/07/2025 : 08:28:42.271 :     DateFactureEnd   = 
21/07/2025 : 08:28:42.272 :     NO SORTING: Using default sort
21/07/2025 : 08:28:42.350 :     Items retrieved: 100
21/07/2025 : 08:28:44.446 :     Count = 8832132
21/07/2025 : 08:28:44.446 : Leaving
21/07/2025 : 09:03:57.161 : Entering
21/07/2025 : 09:03:57.162 :     Client           = 
21/07/2025 : 09:03:57.162 :     DateFactureStart = 
21/07/2025 : 09:03:57.162 :     DateFactureEnd   = 
21/07/2025 : 09:03:59.431 :     Count = 8832132
21/07/2025 : 09:03:59.431 : Leaving
21/07/2025 : 09:04:00.486 : Entering
21/07/2025 : 09:04:00.486 :     Client           = 
21/07/2025 : 09:04:00.486 :     DateFactureStart = 
21/07/2025 : 09:04:00.487 :     DateFactureEnd   = 
21/07/2025 : 09:04:02.683 :     Count = 8832132
21/07/2025 : 09:04:02.684 : Leaving
21/07/2025 : 10:08:08.161 : Entering
21/07/2025 : 10:08:08.254 :     Client           = 
21/07/2025 : 10:08:08.306 :     DateFactureStart = 
21/07/2025 : 10:08:08.306 :     DateFactureEnd   = 
21/07/2025 : 10:08:13.797 :     Count = 8832132
21/07/2025 : 10:08:13.798 : Leaving
21/07/2025 : 10:08:18.546 : Entering
21/07/2025 : 10:08:18.546 :     Client           = 0026
21/07/2025 : 10:08:18.548 :     DateFactureStart = 
21/07/2025 : 10:08:18.549 :     DateFactureEnd   = 
21/07/2025 : 10:08:25.904 :     Count = 55904
21/07/2025 : 10:08:25.904 : Leaving
21/07/2025 : 10:08:50.004 : Entering
21/07/2025 : 10:08:50.004 :     Client           = 
21/07/2025 : 10:08:50.005 :     DateFactureStart = 
21/07/2025 : 10:08:50.005 :     DateFactureEnd   = 
21/07/2025 : 10:08:57.361 :     Count = 8832132
21/07/2025 : 10:08:57.364 : Leaving
21/07/2025 : 10:08:57.529 : Entering
21/07/2025 : 10:08:57.529 :     Client           = 0026
21/07/2025 : 10:08:57.530 :     DateFactureStart = 
21/07/2025 : 10:08:57.531 :     DateFactureEnd   = 
21/07/2025 : 10:09:00.275 :     Count = 55904
21/07/2025 : 10:09:00.278 : Leaving
21/07/2025 : 10:09:00.337 : Entering
21/07/2025 : 10:09:00.338 :     Client           = 0026
21/07/2025 : 10:09:00.339 :     DateFactureStart = 
21/07/2025 : 10:09:00.339 :     DateFactureEnd   = 
21/07/2025 : 10:09:03.228 :     Count = 55904
21/07/2025 : 10:09:03.230 : Leaving
21/07/2025 : 10:09:13.904 : Entering
21/07/2025 : 10:09:13.905 :     Client           = 0026
21/07/2025 : 10:09:13.908 :     DateFactureStart = 
21/07/2025 : 10:09:13.910 :     DateFactureEnd   = 
21/07/2025 : 10:09:16.694 :     Count = 55904
21/07/2025 : 10:09:16.695 : Leaving
21/07/2025 : 10:27:50.400 : Entering
21/07/2025 : 10:27:50.401 :     Client           = 
21/07/2025 : 10:27:50.401 :     DateFactureStart = 
21/07/2025 : 10:27:50.402 :     DateFactureEnd   = 
21/07/2025 : 10:27:58.059 :     Count = 8832132
21/07/2025 : 10:27:58.059 : Leaving
21/07/2025 : 10:27:58.151 : Entering
21/07/2025 : 10:27:58.151 :     Client           = 
21/07/2025 : 10:27:58.152 :     DateFactureStart = 
21/07/2025 : 10:27:58.152 :     DateFactureEnd   = 
21/07/2025 : 10:28:02.890 :     Count = 8832132
21/07/2025 : 10:28:02.891 : Leaving
21/07/2025 : 10:34:48.192 : Entering
21/07/2025 : 10:34:48.194 :     Client           = 
21/07/2025 : 10:34:48.195 :     DateFactureStart = 
21/07/2025 : 10:34:48.195 :     DateFactureEnd   = 
21/07/2025 : 10:34:58.314 :     Count = 8832132
21/07/2025 : 10:34:58.315 : Leaving
21/07/2025 : 10:34:58.840 : Entering
21/07/2025 : 10:34:58.841 :     Client           = 
21/07/2025 : 10:34:58.841 :     DateFactureStart = 
21/07/2025 : 10:34:58.841 :     DateFactureEnd   = 
21/07/2025 : 10:35:01.419 :     Count = 8832132
21/07/2025 : 10:35:01.419 : Leaving
21/07/2025 : 10:35:45.261 : Entering
21/07/2025 : 10:35:45.261 :     Client           = 
21/07/2025 : 10:35:45.262 :     DateFactureStart = 
21/07/2025 : 10:35:45.262 :     DateFactureEnd   = 
21/07/2025 : 10:35:49.894 :     Count = 8832132
21/07/2025 : 10:35:49.895 : Leaving
21/07/2025 : 10:36:50.037 : Entering
21/07/2025 : 10:36:50.037 :     Client           = 
21/07/2025 : 10:36:50.038 :     DateFactureStart = 
21/07/2025 : 10:36:50.038 :     DateFactureEnd   = 
21/07/2025 : 10:36:52.708 :     Count = 8832132
21/07/2025 : 10:36:52.709 : Leaving
21/07/2025 : 10:55:31.139 : Entering
21/07/2025 : 10:55:31.143 :     Entering
21/07/2025 : 10:55:31.143 :         Client           = 
21/07/2025 : 10:55:31.144 :         Client           = 
21/07/2025 : 10:55:31.145 :         DateFactureStart = 
21/07/2025 : 10:55:31.145 :         DateFactureEnd   = 
21/07/2025 : 10:55:31.145 :         DateFactureStart = 
21/07/2025 : 10:55:31.145 :         DateFactureEnd   = 
21/07/2025 : 10:55:40.750 :         Count = 8832132
21/07/2025 : 10:55:40.751 :     Leaving
21/07/2025 : 10:55:40.958 :     Count = 8832132
21/07/2025 : 10:55:40.958 : Leaving
21/07/2025 : 10:55:41.205 : Entering
21/07/2025 : 10:55:41.206 :     Entering
21/07/2025 : 10:55:41.206 :         Client           = 
21/07/2025 : 10:55:41.206 :         DateFactureStart = 
21/07/2025 : 10:55:41.206 :         DateFactureEnd   = 
21/07/2025 : 10:55:41.207 :         Client           = 
21/07/2025 : 10:55:41.207 :         DateFactureStart = 
21/07/2025 : 10:55:41.207 :         DateFactureEnd   = 
21/07/2025 : 10:55:48.039 :         Count = 8832132
21/07/2025 : 10:55:48.039 :     Leaving
21/07/2025 : 10:55:48.110 :     Count = 8832132
21/07/2025 : 10:55:48.110 : Leaving
21/07/2025 : 11:41:23.572 : Entering
21/07/2025 : 11:41:23.770 :     Client           = 
21/07/2025 : 11:41:23.771 :     DateFactureStart = 
21/07/2025 : 11:41:23.771 :     DateFactureEnd   = 
21/07/2025 : 11:41:25.911 :     Entering
21/07/2025 : 11:41:25.911 :         Client           = 
21/07/2025 : 11:41:25.912 :         DateFactureStart = 
21/07/2025 : 11:41:25.913 :         DateFactureEnd   = 
21/07/2025 : 11:41:35.074 :         Count = 8832132
21/07/2025 : 11:41:35.075 :     Leaving
21/07/2025 : 11:41:35.325 :     Count = 8832132
21/07/2025 : 11:41:35.325 : Leaving
21/07/2025 : 11:41:35.531 : Entering
21/07/2025 : 11:41:35.531 :     Entering
21/07/2025 : 11:41:35.531 :         Client           = 
21/07/2025 : 11:41:35.532 :         DateFactureStart = 
21/07/2025 : 11:41:35.532 :         DateFactureEnd   = 
21/07/2025 : 11:41:35.532 :         Client           = 
21/07/2025 : 11:41:35.532 :         DateFactureStart = 
21/07/2025 : 11:41:35.533 :         DateFactureEnd   = 
21/07/2025 : 11:41:40.991 :         Count = 8832132
21/07/2025 : 11:41:40.991 :     Leaving
21/07/2025 : 11:41:41.096 :     Count = 8832132
21/07/2025 : 11:41:41.097 : Leaving
21/07/2025 : 11:41:46.770 : Entering
21/07/2025 : 11:41:46.771 :     Client           = 0026
21/07/2025 : 11:41:46.772 :     DateFactureStart = 
21/07/2025 : 11:41:46.772 :     DateFactureEnd   = 
21/07/2025 : 11:41:51.025 :     Count = 55904
21/07/2025 : 11:41:51.026 : Leaving
21/07/2025 : 11:52:56.800 : Entering
21/07/2025 : 11:52:56.803 :     Client           = 
21/07/2025 : 11:52:56.804 :     DateFactureStart = 
21/07/2025 : 11:52:56.804 :     DateFactureEnd   = 
21/07/2025 : 11:53:07.788 :     Count = 8832132
21/07/2025 : 11:53:07.788 : Leaving
21/07/2025 : 11:53:08.376 : Entering
21/07/2025 : 11:53:08.376 :     Client           = 
21/07/2025 : 11:53:08.377 :     DateFactureStart = 
21/07/2025 : 11:53:08.378 :     DateFactureEnd   = 
21/07/2025 : 11:53:09.725 :     Entering
21/07/2025 : 11:53:09.725 :         Client           = 
21/07/2025 : 11:53:09.725 :         DateFactureStart = 
21/07/2025 : 11:53:09.725 :         DateFactureEnd   = 
21/07/2025 : 11:53:17.233 :         Count = 8832132
21/07/2025 : 11:53:17.234 :     Leaving
21/07/2025 : 11:53:18.032 :     Count = 8832132
21/07/2025 : 11:53:18.033 : Leaving
21/07/2025 : 11:53:18.153 : Entering
21/07/2025 : 11:53:18.154 :     Client           = 
21/07/2025 : 11:53:18.154 :     DateFactureStart = 
21/07/2025 : 11:53:18.155 :     DateFactureEnd   = 
21/07/2025 : 11:53:21.837 :     Count = 8832132
21/07/2025 : 11:53:21.837 : Leaving
21/07/2025 : 12:06:54.341 : Entering
21/07/2025 : 12:06:54.345 :     Client           = 
21/07/2025 : 12:06:54.346 :     DateFactureStart = 
21/07/2025 : 12:06:54.347 :     DateFactureEnd   = 
21/07/2025 : 12:07:00.305 :     Entering
21/07/2025 : 12:07:00.308 :         Client           = 
21/07/2025 : 12:07:00.308 :         DateFactureStart = 
21/07/2025 : 12:07:00.308 :         DateFactureEnd   = 
21/07/2025 : 12:07:03.112 :         Count = 8832132
21/07/2025 : 12:07:03.112 :     Leaving
21/07/2025 : 12:07:03.614 :     Entering
21/07/2025 : 12:07:03.614 :         Client           = 
21/07/2025 : 12:07:03.615 :         DateFactureStart = 
21/07/2025 : 12:07:03.615 :         DateFactureEnd   = 
21/07/2025 : 12:07:05.585 :         Count = 8832132
21/07/2025 : 12:07:05.586 :     Leaving
21/07/2025 : 12:07:06.808 :     Entering
21/07/2025 : 12:07:06.809 :         Client           = 
21/07/2025 : 12:07:06.809 :         DateFactureStart = 
21/07/2025 : 12:07:06.809 :         DateFactureEnd   = 
21/07/2025 : 12:07:08.185 :         Count = 8832132
21/07/2025 : 12:07:08.186 :     Leaving
21/07/2025 : 12:07:10.820 :     Count = 8832132
21/07/2025 : 12:07:10.820 : Leaving
21/07/2025 : 12:16:47.788 : Entering
21/07/2025 : 12:16:47.793 :     Client           = 
21/07/2025 : 12:16:47.794 :     DateFactureStart = 
21/07/2025 : 12:16:47.795 :     DateFactureEnd   = 
21/07/2025 : 12:16:53.301 :     Entering
21/07/2025 : 12:16:53.302 :         Client           = 
21/07/2025 : 12:16:53.303 :         DateFactureStart = 
21/07/2025 : 12:16:53.303 :         DateFactureEnd   = 
21/07/2025 : 12:16:59.077 :         Count = 8832132
21/07/2025 : 12:16:59.077 :     Leaving
21/07/2025 : 12:16:59.077 :     Count = 8832132
21/07/2025 : 12:16:59.079 : Leaving
21/07/2025 : 12:16:59.895 : Entering
21/07/2025 : 12:16:59.896 :     Entering
21/07/2025 : 12:16:59.896 :         Client           = 
21/07/2025 : 12:16:59.897 :         DateFactureStart = 
21/07/2025 : 12:16:59.897 :         DateFactureEnd   = 
21/07/2025 : 12:16:59.897 :         Client           = 
21/07/2025 : 12:16:59.898 :         DateFactureStart = 
21/07/2025 : 12:16:59.898 :         DateFactureEnd   = 
21/07/2025 : 12:17:05.730 :         Count = 8832132
21/07/2025 : 12:17:05.731 :     Leaving
21/07/2025 : 12:17:06.140 :     Count = 8832132
21/07/2025 : 12:17:06.141 : Leaving
21/07/2025 : 12:20:25.095 : Entering
21/07/2025 : 12:20:25.095 :     Client           = 
21/07/2025 : 12:20:25.096 :     DateFactureStart = 
21/07/2025 : 12:20:25.097 :     DateFactureEnd   = 
21/07/2025 : 12:20:29.804 :     Count = 8832132
21/07/2025 : 12:20:29.806 : Leaving
21/07/2025 : 12:20:29.905 : Entering
21/07/2025 : 12:20:29.905 :     Client           = 
21/07/2025 : 12:20:29.906 :     DateFactureStart = 
21/07/2025 : 12:20:29.907 :     DateFactureEnd   = 
21/07/2025 : 12:20:34.032 :     Count = 8832132
21/07/2025 : 12:20:34.034 : Leaving
21/07/2025 : 12:20:57.255 : Entering
21/07/2025 : 12:20:57.255 :     Client           = 
21/07/2025 : 12:20:57.260 :     DateFactureStart = 21/06/2020 00:00:00
21/07/2025 : 12:20:57.260 :     DateFactureEnd   = 21/07/2020 00:00:00
21/07/2025 : 12:21:27.857 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
21/07/2025 : 12:22:19.392 :     Entering
21/07/2025 : 12:22:19.393 :         Client           = 
21/07/2025 : 12:22:19.395 :         DateFactureStart = 15/06/2020 00:00:00
21/07/2025 : 12:22:19.395 :         DateFactureEnd   = 21/07/2020 00:00:00
21/07/2025 : 12:22:47.693 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 12:23:20.931 :         Entering
21/07/2025 : 12:23:20.933 :             Client           = 
21/07/2025 : 12:23:20.937 :             DateFactureStart = 09/06/2020 00:00:00
21/07/2025 : 12:23:20.939 :             DateFactureEnd   = 21/07/2020 00:00:00
21/07/2025 : 12:23:44.250 :             The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 12:24:04.694 :             Entering
21/07/2025 : 12:24:04.695 :                 Client           = 
21/07/2025 : 12:24:04.701 :                 DateFactureStart = 31/08/2019 00:00:00
21/07/2025 : 12:24:04.702 :                 DateFactureEnd   = 21/07/2020 00:00:00
21/07/2025 : 12:24:22.649 :                 The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 13:22:20.790 : Entering
21/07/2025 : 13:22:20.792 :     Client           = 
21/07/2025 : 13:22:20.792 :     DateFactureStart = 
21/07/2025 : 13:22:20.793 :     DateFactureEnd   = 
21/07/2025 : 13:22:30.739 :     Count = 8832132
21/07/2025 : 13:22:30.741 : Leaving
21/07/2025 : 13:22:31.487 : Entering
21/07/2025 : 13:22:31.488 :     Client           = 
21/07/2025 : 13:22:31.488 :     DateFactureStart = 
21/07/2025 : 13:22:31.488 :     DateFactureEnd   = 
21/07/2025 : 13:22:34.627 :     Count = 8832132
21/07/2025 : 13:22:34.627 : Leaving
21/07/2025 : 13:24:43.910 : Entering
21/07/2025 : 13:24:43.911 :     Client           = 
21/07/2025 : 13:24:43.911 :     DateFactureStart = 
21/07/2025 : 13:24:43.911 :     DateFactureEnd   = 
21/07/2025 : 13:24:47.748 :     Count = 8832132
21/07/2025 : 13:24:47.749 : Leaving
21/07/2025 : 13:24:48.005 : Entering
21/07/2025 : 13:24:48.006 :     Client           = 
21/07/2025 : 13:24:48.006 :     DateFactureStart = 
21/07/2025 : 13:24:48.007 :     DateFactureEnd   = 
21/07/2025 : 13:24:52.664 :     Count = 8832132
21/07/2025 : 13:24:52.665 : Leaving
21/07/2025 : 13:24:56.053 : Entering
21/07/2025 : 13:24:56.054 :     Client           = 
21/07/2025 : 13:24:56.055 :     DateFactureStart = 
21/07/2025 : 13:24:56.057 :     DateFactureEnd   = 
21/07/2025 : 13:25:01.265 :     Count = 8832132
21/07/2025 : 13:25:01.266 : Leaving
21/07/2025 : 13:29:39.476 : Entering
21/07/2025 : 13:29:39.478 :     Client           = 
21/07/2025 : 13:29:39.482 :     DateFactureStart = 23/01/2002 00:00:00
21/07/2025 : 13:29:39.483 :     DateFactureEnd   = 23/01/2002 00:00:00
21/07/2025 : 13:30:10.796 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
21/07/2025 : 13:30:10.955 :     Entering
21/07/2025 : 13:30:10.955 :         Client           = 
21/07/2025 : 13:30:10.956 :         DateFactureStart = 24/01/2002 00:00:00
21/07/2025 : 13:30:10.957 :         DateFactureEnd   = 23/01/2002 00:00:00
21/07/2025 : 13:30:41.324 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
21/07/2025 : 13:30:54.324 :         Entering
21/07/2025 : 13:30:54.325 :             Client           = 
21/07/2025 : 13:30:54.327 :             DateFactureStart = 23/01/2002 00:00:00
21/07/2025 : 13:30:54.328 :             DateFactureEnd   = 24/01/2002 00:00:00
21/07/2025 : 13:31:24.583 :             Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
21/07/2025 : 13:33:25.874 :             Entering
21/07/2025 : 13:33:25.875 :                 Client           = 
21/07/2025 : 13:33:25.876 :                 DateFactureStart = 22/01/2002 00:00:00
21/07/2025 : 13:33:25.878 :                 DateFactureEnd   = 25/01/2002 00:00:00
21/07/2025 : 13:33:49.555 :                 The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 13:35:07.014 : Entering
21/07/2025 : 13:35:07.018 :     Client           = 
21/07/2025 : 13:35:07.019 :     DateFactureStart = 
21/07/2025 : 13:35:07.020 :     DateFactureEnd   = 
21/07/2025 : 13:35:17.325 :     Count = 8832132
21/07/2025 : 13:35:17.327 : Leaving
21/07/2025 : 13:35:17.993 : Entering
21/07/2025 : 13:35:17.994 :     Client           = 
21/07/2025 : 13:35:17.994 :     DateFactureStart = 
21/07/2025 : 13:35:17.994 :     DateFactureEnd   = 
21/07/2025 : 13:35:22.944 :     Count = 8832132
21/07/2025 : 13:35:22.945 : Leaving
21/07/2025 : 13:35:27.945 : Entering
21/07/2025 : 13:35:27.945 :     Client           = 0026
21/07/2025 : 13:35:27.946 :     DateFactureStart = 
21/07/2025 : 13:35:27.946 :     DateFactureEnd   = 
21/07/2025 : 13:35:31.026 :     Count = 55904
21/07/2025 : 13:35:31.028 : Leaving
21/07/2025 : 13:35:39.154 : Entering
21/07/2025 : 13:35:39.155 :     Client           = 0026
21/07/2025 : 13:35:39.155 :     DateFactureStart = 
21/07/2025 : 13:35:39.156 :     DateFactureEnd   = 
21/07/2025 : 13:35:41.990 :     Count = 55904
21/07/2025 : 13:35:41.991 : Leaving
21/07/2025 : 13:36:18.430 : Entering
21/07/2025 : 13:36:18.432 :     Client           = 0026
21/07/2025 : 13:36:18.439 :     DateFactureStart = 
21/07/2025 : 13:36:18.440 :     DateFactureEnd   = 
21/07/2025 : 13:36:21.312 :     Count = 55904
21/07/2025 : 13:36:21.313 : Leaving
21/07/2025 : 13:36:27.371 : Entering
21/07/2025 : 13:36:27.371 :     Client           = 0026
21/07/2025 : 13:36:27.373 :     DateFactureStart = 
21/07/2025 : 13:36:27.374 :     DateFactureEnd   = 
21/07/2025 : 13:36:30.120 :     Count = 55904
21/07/2025 : 13:36:30.121 : Leaving
21/07/2025 : 13:36:32.756 : Entering
21/07/2025 : 13:36:32.758 :     Client           = 0026
21/07/2025 : 13:36:32.762 :     DateFactureStart = 
21/07/2025 : 13:36:32.763 :     DateFactureEnd   = 
21/07/2025 : 13:36:34.113 :     Count = 0
21/07/2025 : 13:36:34.114 : Leaving
21/07/2025 : 13:36:37.108 : Entering
21/07/2025 : 13:36:37.110 :     Client           = 0026
21/07/2025 : 13:36:37.115 :     DateFactureStart = 
21/07/2025 : 13:36:37.117 :     DateFactureEnd   = 
21/07/2025 : 13:36:37.696 :     Count = 0
21/07/2025 : 13:36:37.697 : Leaving
21/07/2025 : 13:46:15.331 : Entering
21/07/2025 : 13:46:15.331 :     Client           = 
21/07/2025 : 13:46:15.334 :     DateFactureStart = 
21/07/2025 : 13:46:15.334 :     DateFactureEnd   = 
21/07/2025 : 13:46:20.854 :     Entering
21/07/2025 : 13:46:20.855 :         Client           = 
21/07/2025 : 13:46:20.857 :         DateFactureStart = 
21/07/2025 : 13:46:20.857 :         DateFactureEnd   = 
21/07/2025 : 13:46:22.408 :         Count = 8832132
21/07/2025 : 13:46:22.408 :     Leaving
21/07/2025 : 13:46:25.581 :     Count = 8832132
21/07/2025 : 13:46:25.582 : Leaving
21/07/2025 : 13:46:25.679 : Entering
21/07/2025 : 13:46:25.679 :     Client           = 
21/07/2025 : 13:46:25.680 :     DateFactureStart = 
21/07/2025 : 13:46:25.681 :     DateFactureEnd   = 
21/07/2025 : 13:46:28.962 :     Count = 8832132
21/07/2025 : 13:46:28.963 : Leaving
21/07/2025 : 13:47:22.492 : Entering
21/07/2025 : 13:47:22.493 :     Client           = 
21/07/2025 : 13:47:22.497 :     DateFactureStart = 
21/07/2025 : 13:47:22.499 :     DateFactureEnd   = 
21/07/2025 : 13:47:25.691 :     Count = 8832132
21/07/2025 : 13:47:25.692 : Leaving
21/07/2025 : 13:50:25.156 : Entering
21/07/2025 : 13:50:25.159 :     Client           = 
21/07/2025 : 13:50:25.161 :     DateFactureStart = 
21/07/2025 : 13:50:25.161 :     DateFactureEnd   = 
21/07/2025 : 13:50:35.547 :     Count = 8832132
21/07/2025 : 13:50:35.548 : Leaving
21/07/2025 : 13:50:36.155 : Entering
21/07/2025 : 13:50:36.155 :     Client           = 
21/07/2025 : 13:50:36.156 :     DateFactureStart = 
21/07/2025 : 13:50:36.156 :     DateFactureEnd   = 
21/07/2025 : 13:50:40.371 :     Count = 8832132
21/07/2025 : 13:50:40.372 : Leaving
21/07/2025 : 13:50:45.344 : Entering
21/07/2025 : 13:50:45.345 :     Client           = 
21/07/2025 : 13:50:45.345 :     DateFactureStart = 
21/07/2025 : 13:50:45.345 :     DateFactureEnd   = 
21/07/2025 : 13:50:45.925 :     Count = 0
21/07/2025 : 13:50:45.925 : Leaving
21/07/2025 : 13:50:48.952 : Entering
21/07/2025 : 13:50:48.953 :     Client           = 
21/07/2025 : 13:50:48.954 :     DateFactureStart = 
21/07/2025 : 13:50:48.956 :     DateFactureEnd   = 
21/07/2025 : 13:50:49.573 :     Count = 0
21/07/2025 : 13:50:49.574 : Leaving
21/07/2025 : 13:50:54.835 : Entering
21/07/2025 : 13:50:54.837 :     Client           = 
21/07/2025 : 13:50:54.841 :     DateFactureStart = 
21/07/2025 : 13:50:54.843 :     DateFactureEnd   = 
21/07/2025 : 13:50:55.409 :     Count = 0
21/07/2025 : 13:50:55.410 : Leaving
21/07/2025 : 13:50:55.463 : Entering
21/07/2025 : 13:50:55.464 :     Client           = 
21/07/2025 : 13:50:55.466 :     DateFactureStart = 
21/07/2025 : 13:50:55.467 :     DateFactureEnd   = 
21/07/2025 : 13:50:56.089 :     Count = 0
21/07/2025 : 13:50:56.090 : Leaving
21/07/2025 : 13:51:02.493 : Entering
21/07/2025 : 13:51:02.495 :     Client           = 
21/07/2025 : 13:51:02.507 :     DateFactureStart = 
21/07/2025 : 13:51:02.508 :     DateFactureEnd   = 
21/07/2025 : 13:51:06.936 :     Count = 6
21/07/2025 : 13:51:06.937 : Leaving
21/07/2025 : 13:51:06.966 : Entering
21/07/2025 : 13:51:06.967 :     Client           = 
21/07/2025 : 13:51:06.969 :     DateFactureStart = 
21/07/2025 : 13:51:06.970 :     DateFactureEnd   = 
21/07/2025 : 13:51:10.262 :     Count = 6
21/07/2025 : 13:51:10.263 : Leaving
21/07/2025 : 13:51:29.278 : Entering
21/07/2025 : 13:51:29.279 :     Client           = 
21/07/2025 : 13:51:29.285 :     DateFactureStart = 
21/07/2025 : 13:51:29.293 :     DateFactureEnd   = 19/07/2019 00:00:00
21/07/2025 : 13:51:31.076 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 13:52:02.954 :     Entering
21/07/2025 : 13:52:02.955 :         Client           = 
21/07/2025 : 13:52:02.964 :         DateFactureStart = 12/07/2001 00:00:00
21/07/2025 : 13:52:02.965 :         DateFactureEnd   = 19/07/2019 00:00:00
21/07/2025 : 13:52:04.221 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 13:52:09.475 :         Entering
21/07/2025 : 13:52:09.476 :             Client           = 
21/07/2025 : 13:52:09.481 :             DateFactureStart = 12/07/2001 00:00:00
21/07/2025 : 13:52:09.486 :             DateFactureEnd   = 19/07/2019 00:00:00
21/07/2025 : 13:52:10.208 :             The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 13:54:36.065 :             Entering
21/07/2025 : 13:54:36.066 :                 Client           = 0026
21/07/2025 : 13:54:36.067 :                 DateFactureStart = 12/07/2001 00:00:00
21/07/2025 : 13:54:36.068 :                 DateFactureEnd   = 19/07/2019 00:00:00
21/07/2025 : 13:54:36.800 :                 Count = 0
21/07/2025 : 13:54:36.800 :             Leaving
21/07/2025 : 13:54:44.996 :             Entering
21/07/2025 : 13:54:44.998 :                 Client           = 0026
21/07/2025 : 13:54:45.001 :                 DateFactureStart = 
21/07/2025 : 13:54:45.003 :                 DateFactureEnd   = 
21/07/2025 : 13:54:45.309 :                 Count = 0
21/07/2025 : 13:54:45.310 :             Leaving
21/07/2025 : 13:54:50.450 :             Entering
21/07/2025 : 13:54:50.451 :                 Client           = 0028
21/07/2025 : 13:54:50.454 :                 DateFactureStart = 
21/07/2025 : 13:54:50.456 :                 DateFactureEnd   = 
21/07/2025 : 13:54:50.854 :                 Count = 0
21/07/2025 : 13:54:50.855 :             Leaving
21/07/2025 : 13:55:00.809 :             Entering
21/07/2025 : 13:55:00.809 :                 Client           = 0028
21/07/2025 : 13:55:00.811 :                 DateFactureStart = 
21/07/2025 : 13:55:00.811 :                 DateFactureEnd   = 
21/07/2025 : 13:55:02.913 :                 Count = 21326
21/07/2025 : 13:55:02.914 :             Leaving
21/07/2025 : 13:55:33.463 :             Entering
21/07/2025 : 13:55:33.465 :                 Client           = 0028
21/07/2025 : 13:55:33.470 :                 DateFactureStart = 21/07/2023 00:00:00
21/07/2025 : 13:55:33.474 :                 DateFactureEnd   = 09/07/2025 00:00:00
21/07/2025 : 13:55:34.192 :                 The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
21/07/2025 : 13:59:26.823 : Entering
21/07/2025 : 13:59:26.826 :     Client           = 
21/07/2025 : 13:59:26.827 :     DateFactureStart = 
21/07/2025 : 13:59:26.828 :     DateFactureEnd   = 
21/07/2025 : 13:59:26.828 :     SQL Params - Client: '', DateStart: '', DateEnd: '', Etat: '', TypeFacture: ''
21/07/2025 : 13:59:36.236 :     Count = 8832132
21/07/2025 : 13:59:36.239 : Leaving
21/07/2025 : 14:00:30.198 : Entering
21/07/2025 : 14:00:30.199 :     Client           = 
21/07/2025 : 14:00:30.199 :     DateFactureStart = 
21/07/2025 : 14:00:30.199 :     DateFactureEnd   = 
21/07/2025 : 14:00:30.199 :     SQL Params - Client: '', DateStart: '', DateEnd: '', Etat: '', TypeFacture: ''
21/07/2025 : 14:00:33.227 :     Count = 8832132
21/07/2025 : 14:00:33.228 : Leaving
21/07/2025 : 14:05:28.693 : Entering
21/07/2025 : 14:05:28.696 :     Client           = 
21/07/2025 : 14:05:28.697 :     DateFactureStart = 
21/07/2025 : 14:05:28.697 :     DateFactureEnd   = 
21/07/2025 : 14:05:28.697 :     SQL Params - Client: '', DateStart: '', DateEnd: '', Etat: '', TypeFacture: ''
21/07/2025 : 14:05:36.526 :     Count = 8832132
21/07/2025 : 14:05:36.528 : Leaving
