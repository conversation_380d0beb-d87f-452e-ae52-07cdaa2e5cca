﻿using System.Text.RegularExpressions;

namespace CarSystemsWebApp
{
    public static class Utils
    {
        public static string    LogFolder = null;
        public static int       LogRetention = 10;

        private static object m_LockObject = null;
        private static DateTime? m_CurrentDate = null;
        private static Dictionary<string, int> Indentation;
        private static Dictionary<string, StreamWriter> StreamWriterCache = new Dictionary<string, StreamWriter>();

        public static void Helper_Trace(string tag, string vMessage, bool outputToConsole, bool bIdentation = true, int offset = 0)
        {
            if (m_LockObject == null)
                m_LockObject = new object();

            lock (m_LockObject)
            {
                try
                {
                    if (string.IsNullOrEmpty(tag))
                        tag = "MISSINGTAG";

                    // Clean up and refresh date if a new day starts
                    if (m_CurrentDate != DateTime.UtcNow.Date)
                    {
                        Helper_CleanupLogs();
                        m_CurrentDate = DateTime.UtcNow.Date;
                    }

                    // Apply indentation if needed
                    if (offset != 0)
                    {
                        string indentation = new String(' ', offset);
                        vMessage = indentation + vMessage;
                    }

                    // Format the message
                    string s = Helper_Trace_FormatMessage(tag, vMessage, bIdentation);

                    // Optionally output to the console
                    if (outputToConsole)
                        Console.WriteLine(s);

                    if (Utils.LogFolder != null)
                    {
                        // Remove spaces from tag and ensure it's clean
                        tag = tag.Replace(" ", "").Trim();
                        if (tag.StartsWith("+"))
                            tag = tag.Substring(1);

                        // Define the log folder and file structure
                        var logFolder = Utils.LogFolder;
                        string folder = Path.Combine(logFolder, $"{m_CurrentDate.Value.Year}-{m_CurrentDate.Value.Month:00}-{m_CurrentDate.Value.Day:00}");
                        if (!Directory.Exists(folder))
                            Directory.CreateDirectory(folder);

                        string filename = Path.Combine(folder, $"{tag}.log");

                        // Get or create a StreamWriter for this tag
                        StreamWriter sw = Helper_GetStreamWriterForTag(filename);

                        // Write to the log file
                        sw.WriteLine(s);
                    }
                }
                catch (Exception ex)
                {
                    // Handle or log exceptions as necessary
                }
            }
        }

        private static string Helper_Trace_FormatMessage(string tag, string vMessage, bool bIdentation)
        {
            DateTime vDateTime;
            vDateTime = DateTime.UtcNow;

            if (bIdentation)
            {
                if (Indentation == null)
                    Indentation = new Dictionary<string, int>();

                if (Indentation.ContainsKey(tag) == false)
                    Indentation.Add(tag, 0);


                if (vMessage.Contains("Leaving"))
                {
                    Utils.Indentation[tag] = Utils.Indentation[tag] - 1;
                    if (Utils.Indentation[tag] < 0)
                        Utils.Indentation[tag] = 0;
                }
            }

            if (bIdentation)
            {
                string indentation;
                indentation = Utils.Indentation[tag] == 0 ? "" : new String(' ', Utils.Indentation[tag] * 4);
                vMessage = indentation + vMessage;
            }

            string s;
            s = string.Format("{0}/{1}/{2} : {3}:{4}:{5}.{6} : {7}",
                vDateTime.Day.ToString("00"),
                vDateTime.Month.ToString("00"),
                vDateTime.Year,
                vDateTime.Hour.ToString("00"),
                vDateTime.Minute.ToString("00"),
                vDateTime.Second.ToString("00"),
                vDateTime.Millisecond.ToString("000"),
                vMessage);

            if (bIdentation)
            {
                if (vMessage.Contains("Entering"))
                {
                    Utils.Indentation[tag] = Utils.Indentation[tag] + 1;
                }
            }

            return s;
        }

        private static void Helper_CleanupLogs()
        {
            if (Utils.LogFolder == null)
                return;

            //var logFolder = ConfigurationManager.AppSettings["LOGFOLDER"];
            var logFolder = Utils.LogFolder;

            if (logFolder.EndsWith("\\") == false)
                logFolder = logFolder + "\\";

            string[] folders;
            folders = Directory.GetDirectories(logFolder);

            Regex r;
            r = new Regex(@"(\d\d\d\d)\-(\d\d)\-(\d\d)");

            foreach (string s in folders)
            {
                Match m;
                m = r.Match(s);
                if (m.Success == false)
                    continue;

                int year = Convert.ToInt32(m.Groups[1].Value);
                int month = Convert.ToInt32(m.Groups[2].Value);
                int day = Convert.ToInt32(m.Groups[3].Value);

                DateTime dt;
                dt = new DateTime(year, month, day);

                TimeSpan ts;
                ts = DateTime.UtcNow.Date - dt;

                if (ts.TotalDays >= LogRetention)
                    Directory.Delete(s, true);
            }
        }

        private static StreamWriter Helper_GetStreamWriterForTag(string filename)
        {
            if (!StreamWriterCache.ContainsKey(filename))
            {
                //var streamWriter = new StreamWriter(filename, true) { AutoFlush = true };
                //StreamWriterCache[filename] = streamWriter;

                var fileStream = new FileStream(filename, FileMode.Append, FileAccess.Write, FileShare.ReadWrite);
                var streamWriter = new StreamWriter(fileStream) { AutoFlush = true };
                StreamWriterCache[filename] = streamWriter;
            }
            return StreamWriterCache[filename];
        }
    }
}
