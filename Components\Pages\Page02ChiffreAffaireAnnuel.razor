@page "/ChiffreAffaireAnnuel"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using CarSystemsWebApp.Services
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection
@using Syncfusion.Drawing

@inject IJSRuntime _jsRuntime
@inject SfDialogService _dialogService
@inject PageTitleService PageTitleService

<!-- ============== -->
<!-- PROFESSIONAL FILTER BAR -->
<!-- ============== -->

<div class="professional-filter-container">
    <div class="filter-header">
        <div class="filter-title">Filtres</div>
        <button class="btn-clear-all" @onclick="ClearAllFilters">Effacer tout</button>
    </div>

    <div class="row filter-row align-items-center">
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedClient" ShowClearButton="true" Placeholder="Rechercher un client..." CssClass="compact-input-text"></SfTextBox>
        </div>
          <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedParc" ShowClearButton="true" Placeholder="Saisissez le parc..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date de début..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateFactureStart"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date de fin..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateFactureEnd"
                         Width="240px"></SfDatePicker>
        </div>
      
        <div class="col-auto">
            <SfButton Type="button" @onclick="Helper_Refresh" CssClass="btn-inline">
                Appliquer
            </SfButton>
        </div>
        <div class="col-auto">
            <SfButton OnClick="OnButtonExport" CssClass="btn-inline-export">
                Export
            </SfButton>
        </div>
    </div>
</div>

<!-- ======= -->
<!-- RESULTS -->
<!-- ======= -->



@* style="height: calc(100vh - 205px) !important;" *@

<div>
    <SfGrid ID="MyGrid"
          TValue="CS_FAC_AI_JOINED_TEMP2_ITEM"
            @ref="@MyGrid"
            Query="@MyQuery"
            AllowPaging="true"
            AllowFiltering="false"
            AllowSorting="true"
            AllowResizing="true" EnablePersistence="true"
            ShowColumnChooser="true" Toolbar=@ToolbarItems
            EnableInfiniteScrolling="false"
            Height="100%">
        <GridEvents TValue="CS_FAC_AI_JOINED_TEMP2_ITEM"
                    RowSelected="OnRowSelected"
                    OnDataBound="DataBoundHandler"
                    OnActionBegin="OnActionBegin"
                    OnActionComplete="OnActionComplete" />
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="22" />
        <GridColumns>


      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.typefac)" HeaderText="typefac" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.numfac)" HeaderText="numfac" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.facavoir)" HeaderText="facavoir" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.datefac)" HeaderText="datefac" Format="dd/MM/yyy" Type=ColumnType.DateOnly  />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.codetiers)" HeaderText="codetiers" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.facturation)" HeaderText="facturation" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.montant)" HeaderText="montant" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.libelle)" HeaderText="libelle" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.codeprest)" HeaderText="codeprest" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.codeparc)" HeaderText="codeparc" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.integre)" HeaderText="integre" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.etat)" HeaderText="etat" />
      <GridColumn Field="@nameof(CS_FAC_AI_JOINED_TEMP2_ITEM.numldct)" HeaderText="numldct"  />
      

           
        </GridColumns>
    </SfGrid>
</div>

<script type="text/javascript">

    function saveAsFile(filename,bytesBase64){
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++){
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_FAC_AI_JOINED_TEMP2_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;

    private string   SelectedClient;
    private DateTime? SelectedDateFactureStart;
    private DateTime? SelectedDateFactureEnd;
    private string      SelectedParc;
    
   


    protected override void OnInitialized()
    {
        // Set page title
        PageTitleService.SetTitle("Chiffre d'Affaire Annuel");

        this.MyQuery = new Query()
            .AddParams("Client", null)
            .AddParams("DateFactureStart", null)
            .AddParams("DateFactureEnd", null)
            .AddParams("Parc", null)
          ;



    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_FAC_AI_JOINED_TEMP2_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_FAC_AI_JOINED_TEMP2_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_FAC_AI_JOINED_TEMP2_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_FAC_AI_JOINED_TEMP2_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["Client"] = this.SelectedClient;
        MyQuery.Queries.Params["DateFactureStart"] = this.SelectedDateFactureStart;
        MyQuery.Queries.Params["DateFactureEnd"] = this.SelectedDateFactureEnd;
        MyQuery.Queries.Params["Parc"] = this.SelectedParc;

        // Reset pagination to first page when applying new filters
        await MyGrid.GoToPageAsync(1);
        await MyGrid.Refresh();
    }

    private async Task ClearAllFilters()
    {
        SelectedClient = null;
        SelectedDateFactureStart = null;
        SelectedDateFactureEnd = null;
        SelectedParc = null;
        await Helper_Refresh();
    }

    private async void OnButtonExport()
    {
        // if (this.DataSourceGridShifts == null && this.DataSourceGridResults == null)
        // {
        //     await _dialogService.AlertAsync("Aucune donnée à exporter");
        //     return;
        // }

        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // ===================================
            // Create a workbook with 2 worksheets
            // ===================================

            IWorkbook workbook = application.Workbooks.Create(2);

            IWorksheet worksheet;
            DataTable table;

            // =====================
            // Worksheet #0 : shifts
            // =====================

            worksheet = workbook.Worksheets[0];
            worksheet.Name = "ChiffreAffaireAnnuel";

            // Get data for export (large limit to get all data)
            string sortcolumn = "code_societe";
            string sortdir = "ascending";

            List<CS_FAC_AI_JOINED_TEMP2_ITEM> items;
            try
            {
                items = MyCustomAdaptor.Helper_GetData(
                    this.SelectedDateFactureStart,
                    this.SelectedDateFactureEnd,
                    this.SelectedClient,
                    this.SelectedParc,
                    0, 100000,
                    sortcolumn, sortdir, 900); // 15 minute timeout for export
            }
            catch (Microsoft.Data.SqlClient.SqlException ex) when (ex.Number == -2)
            {
                // Timeout exception
                await _dialogService.AlertAsync("Timeout d'exportation: La requête prend trop de temps. Veuillez affiner vos filtres (dates, client, parc) pour réduire le volume de données.", "Erreur d'exportation");
                return;
            }
            catch (Exception ex)
            {
                // Other exceptions
                await _dialogService.AlertAsync($"Erreur lors de l'exportation: {ex.Message}", "Erreur d'exportation");
                return;
            }

            // Create a professional header design with WafaLLD orange branding
            // Main title with orange background
            worksheet["A1"].Text = "EXPORT CHIFFRE D'AFFAIRE ANNUEL";
            worksheet["A1"].CellStyle.Font.Bold = true;
            worksheet["A1"].CellStyle.Font.Size = 16;
            worksheet["A1"].CellStyle.Font.Color = ExcelKnownColors.White;
            worksheet["A1"].CellStyle.Color = Color.FromArgb(255, 140, 0); // WafaLLD Orange background
            worksheet["A1"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            worksheet["A1"].CellStyle.VerticalAlignment = ExcelVAlign.VAlignCenter;
            worksheet.Range["A1:R1"].Merge();
            worksheet.Range["A1:R1"].RowHeight = 25;

            // Export info with light orange background
            worksheet["A2"].Text = $"Exporté le: {DateTime.Now:dd/MM/yyyy HH:mm}";
            worksheet["A2"].CellStyle.Font.Size = 10;
            worksheet["A2"].CellStyle.Font.Italic = true;
            worksheet["A2"].CellStyle.Color = Color.FromArgb(255, 228, 196); // Light orange background
            worksheet["A2"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A2:R2"].Merge();

            // Add filter criteria info
            string filterInfo = "Filtres appliqués: ";
            if (SelectedDateFactureStart.HasValue || SelectedDateFactureEnd.HasValue)
            {
                filterInfo += $"Période: {SelectedDateFactureStart?.ToString("dd/MM/yyyy") ?? "..."} - {SelectedDateFactureEnd?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (!string.IsNullOrEmpty(SelectedClient))
            {
                filterInfo += $"Client: {SelectedClient} | ";
            }
            if (!string.IsNullOrEmpty(SelectedParc))
            {
                filterInfo += $"Parc: {SelectedParc} | ";
            }
            if (filterInfo.EndsWith(" | "))
            {
                filterInfo = filterInfo.Substring(0, filterInfo.Length - 3);
            }
            else if (filterInfo == "Filtres appliqués: ")
            {
                filterInfo = "Aucun filtre appliqué";
            }

            worksheet["A3"].Text = filterInfo;
            worksheet["A3"].CellStyle.Font.Size = 9;
            worksheet["A3"].CellStyle.Color = Color.FromArgb(255, 248, 240); // Very light orange/cream
            worksheet["A3"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A3:R3"].Merge();

            // Add a separator line with orange accent
            worksheet.Range["A4:R4"].CellStyle.Color = Color.FromArgb(255, 165, 0); // Orange accent line
            worksheet.Range["A4:R4"].RowHeight = 3;

            // Convert to DataTable for Excel export
            table = new DataTable();
            Type type = typeof(CS_FAC_AI_JOINED_TEMP2_ITEM);
            PropertyInfo[] properties = type.GetProperties();

            // Create columns
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);

            // Add data rows
            foreach (var item in items)
            {
                DataRow row = table.NewRow();
                int k = 0;
                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }
                table.Rows.Add(row);
            }

            // Export data to Excel worksheet starting from row 6 to leave space for header design
            worksheet.ImportDataTable(table, true, 6, 1);

            // Style the data header row (column names) with orange theme
            IRange headerRange = worksheet.Range[6, 1, 6, properties.Length];
            headerRange.CellStyle.Font.Bold = true;
            headerRange.CellStyle.Font.Color = ExcelKnownColors.White;
            headerRange.CellStyle.Color = Color.FromArgb(255, 165, 0); // Medium orange background
            headerRange.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
            headerRange.RowHeight = 20;

            // Add alternating row colors and borders to all data
            if (items.Count > 0)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    int rowIndex = 7 + i; // Start from row 7 (after header)
                    IRange rowRange = worksheet.Range[rowIndex, 1, rowIndex, properties.Length];

                    // Alternating row colors with orange theme
                    if (i % 2 == 0)
                    {
                        rowRange.CellStyle.Color = Color.FromArgb(255, 250, 240); // Very light orange/cream
                    }
                    else
                    {
                        rowRange.CellStyle.Color = Color.White;
                    }

                    // Add borders
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
                }
            }

            worksheet.UsedRange.AutofitColumns();

            // ===================================================
            // Save the document as a stream and return the stream
            // ===================================================

            using (MemoryStream stream = new MemoryStream())
            {
                // Save the created Excel document to MemoryStream
                workbook.SaveAs(stream);

                // Download the excel file
                string filename = $"ExportChiffreAffaireAnnuel_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "CHIFFREAFFAIREANNUEL-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                List<CS_FAC_AI_JOINED_TEMP2_ITEM> empty;
                empty = Enumerable.Empty<CS_FAC_AI_JOINED_TEMP2_ITEM>().ToList();

                if (dm.Params is null || dm.Params.Count == 0)
                {
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Nothing to do", true);
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                    return dm.RequiresCounts ? new DataResult() { Result = empty, Count = 0 } : (object)empty;
                }

                var client              = dm.Params["Client"] as string;
                var dateFactureStart    = dm.Params["DateFactureStart"] as DateTime?;
                var dateFactureEnd      = dm.Params["DateFactureEnd"] as DateTime?;
                var parc                = dm.Params["Parc"] as string;
                

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateFactureStart = {dateFactureStart}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateFactureEnd = {dateFactureEnd}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Client = {client}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Parc = {parc}", true);

                // string sortcolumn   = "codesociete";  // OLD: Hardcoded sorting
                // string sortdir      = "ascending";     // OLD: Hardcoded sorting

                // NEW: Dynamic sorting from DataManagerRequest
                string sortcolumn   = "codeparc";  // Default sort column
                string sortdir      = "ascending";  // Default sort direction

                // Extract sorting information from DataManagerRequest
                if (dm.Sorted != null && dm.Sorted.Count > 0)
                {
                    var sortInfo = dm.Sorted[0]; // Get the first sort column
                    sortcolumn = sortInfo.Name;
                    sortdir = sortInfo.Direction;
                }

                CarSystemsContext model;
                model = new CarSystemsContext();

              

                // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
                object dateStartParam = dateFactureStart.HasValue ? (object)dateFactureStart.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateEndParam = dateFactureEnd.HasValue ? (object)dateFactureEnd.Value.ToString("yyyy-MM-dd") : DBNull.Value;

                var items = Helper_GetData(dateFactureStart, dateFactureEnd, client, parc, dm.Skip, dm.Take, sortcolumn, sortdir);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetChiffreAffaireAnnuelItemsCount_V2 {0},{1},{2},{3}",
                        client,
                        dateStartParam,
                        dateEndParam,
                        parc
                       )
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);

                return null;
            }
        }

        public static List<CS_FAC_AI_JOINED_TEMP2_ITEM> Helper_GetData(DateTime? dateFactureStart, DateTime? dateFactureEnd, string client, string parc, int skip, int take, string sortcolumn, string sortdir, int timeoutSeconds = 30)
        {
            var logKey = "CHIFFRE-AFFAIRE-HELPER_GETDATA";
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateFactureStart = {dateFactureStart}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateFactureEnd = {dateFactureEnd}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"client = {client}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"parc = {parc}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortcolumn = {sortcolumn}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortdir = {sortdir}", true);

            CarSystemsContext model;
            model = new CarSystemsContext();

            // Set command timeout for long-running operations
            model.Database.SetCommandTimeout(timeoutSeconds);

            // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
            object dateStartParam = dateFactureStart.HasValue ? (object)dateFactureStart.Value.ToString("yyyy-MM-dd") : DBNull.Value;
            object dateEndParam = dateFactureEnd.HasValue ? (object)dateFactureEnd.Value.ToString("yyyy-MM-dd") : DBNull.Value;

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateStartParam = {dateStartParam}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateEndParam = {dateEndParam}", true);

            var items = model.CS_FAC_AI_JOINED_TEMP2_ITEMS.FromSqlRaw("EXECUTE dbo.GetChiffreAffaireAnnuelItems_V2 {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}",
              client,
              dateStartParam,
              dateEndParam,
              parc,
              skip,
              take,
              sortcolumn,
              sortdir).ToList();

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Retrieved {items.Count} items", true);
            return items;
        }
    }
}
