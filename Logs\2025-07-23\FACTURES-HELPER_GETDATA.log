23/07/2025 : 10:39:21.299 : Formatted dateMinFacture = 
23/07/2025 : 10:39:21.301 : Formatted dateMaxFacture = 
23/07/2025 : 10:39:21.302 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 10:39:25.988 : Successfully retrieved 14 items
23/07/2025 : 10:39:34.301 : Formatted dateMinFacture = 
23/07/2025 : 10:39:34.301 : Formatted dateMaxFacture = 
23/07/2025 : 10:39:34.302 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datefac, sortdir=descending
23/07/2025 : 10:41:19.781 : Formatted dateMinFacture = 
23/07/2025 : 10:41:19.789 : Formatted dateMaxFacture = 
23/07/2025 : 10:41:19.790 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=datefac, sortdir=descending
23/07/2025 : 10:41:34.292 : Successfully retrieved 14 items
23/07/2025 : 10:41:40.854 : Formatted dateMinFacture = 
23/07/2025 : 10:41:40.854 : Formatted dateMaxFacture = 
23/07/2025 : 10:41:40.855 : Executing SP with params: client=0026, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=datefac, sortdir=descending
23/07/2025 : 10:41:41.555 : Successfully retrieved 14 items
23/07/2025 : 10:41:51.069 : Formatted dateMinFacture = 
23/07/2025 : 10:41:51.070 : Formatted dateMaxFacture = 
23/07/2025 : 10:41:51.072 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=datefac, sortdir=descending
23/07/2025 : 10:42:06.693 : Successfully retrieved 14 items
23/07/2025 : 10:42:20.947 : Formatted dateMinFacture = 
23/07/2025 : 10:42:20.950 : Formatted dateMaxFacture = 
23/07/2025 : 10:42:20.955 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 10:42:21.518 : Successfully retrieved 14 items
23/07/2025 : 10:42:27.438 : Formatted dateMinFacture = 
23/07/2025 : 10:42:27.439 : Formatted dateMaxFacture = 
23/07/2025 : 10:42:27.439 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=datefac, sortdir=ascending
23/07/2025 : 10:42:39.158 : Successfully retrieved 14 items
23/07/2025 : 10:43:08.744 : Formatted dateMinFacture = 
23/07/2025 : 10:43:08.744 : Formatted dateMaxFacture = 
23/07/2025 : 10:43:08.745 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=datecrea, sortdir=ascending
23/07/2025 : 10:43:10.182 : Successfully retrieved 14 items
23/07/2025 : 10:43:22.913 : Formatted dateMinFacture = 
23/07/2025 : 10:43:22.914 : Formatted dateMaxFacture = 
23/07/2025 : 10:43:22.919 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=datecrea, sortdir=descending
23/07/2025 : 10:43:23.678 : Successfully retrieved 14 items
23/07/2025 : 10:43:25.609 : Formatted dateMinFacture = 
23/07/2025 : 10:43:25.609 : Formatted dateMaxFacture = 
23/07/2025 : 10:43:25.610 : Executing SP with params: client=, dateMin=, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 10:43:26.176 : Successfully retrieved 14 items
23/07/2025 : 10:43:43.478 : Formatted dateMinFacture = 2023-05-25
23/07/2025 : 10:43:43.479 : Formatted dateMaxFacture = 
23/07/2025 : 10:43:43.479 : Executing SP with params: client=, dateMin=2023-05-25, dateMax=, etat=, typeFacture=, skip=14, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 10:44:04.649 : Successfully retrieved 14 items
23/07/2025 : 13:00:19.085 : dateMinFacture = 
23/07/2025 : 13:00:19.087 : dateMaxFacture = 
23/07/2025 : 13:00:19.087 : sortcolumn = numfac
23/07/2025 : 13:00:19.087 : sortdir = ascending
23/07/2025 : 13:00:24.841 : Retrieved 14 items
23/07/2025 : 13:00:35.823 : dateMinFacture = 
23/07/2025 : 13:00:35.823 : dateMaxFacture = 
23/07/2025 : 13:00:35.823 : sortcolumn = numfac
23/07/2025 : 13:00:35.824 : sortdir = ascending
23/07/2025 : 13:00:39.497 : Retrieved 14 items
23/07/2025 : 13:00:53.490 : dateMinFacture = 
23/07/2025 : 13:00:53.491 : dateMaxFacture = 
23/07/2025 : 13:00:53.492 : sortcolumn = numfac
23/07/2025 : 13:00:53.493 : sortdir = ascending
23/07/2025 : 13:00:55.574 : Retrieved 14 items
23/07/2025 : 13:01:04.494 : dateMinFacture = 06/02/2025 00:00:00
23/07/2025 : 13:01:04.495 : dateMaxFacture = 
23/07/2025 : 13:01:04.495 : sortcolumn = numfac
23/07/2025 : 13:01:04.495 : sortdir = ascending
23/07/2025 : 13:01:05.814 : Retrieved 0 items
23/07/2025 : 13:01:58.999 : dateMinFacture = 
23/07/2025 : 13:01:59.000 : dateMaxFacture = 
23/07/2025 : 13:01:59.000 : sortcolumn = numfac
23/07/2025 : 13:01:59.000 : sortdir = ascending
23/07/2025 : 13:02:02.074 : Retrieved 14 items
23/07/2025 : 13:02:08.810 : dateMinFacture = 
23/07/2025 : 13:02:08.810 : dateMaxFacture = 
23/07/2025 : 13:02:08.811 : sortcolumn = numfac
23/07/2025 : 13:02:08.811 : sortdir = ascending
23/07/2025 : 13:02:11.149 : Retrieved 14 items
23/07/2025 : 13:02:21.125 : dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:02:21.126 : dateMaxFacture = 
23/07/2025 : 13:02:21.127 : sortcolumn = numfac
23/07/2025 : 13:02:21.128 : sortdir = ascending
23/07/2025 : 13:03:15.219 : dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:03:15.220 : dateMaxFacture = 
23/07/2025 : 13:03:15.221 : sortcolumn = numfac
23/07/2025 : 13:03:15.222 : sortdir = ascending
23/07/2025 : 13:03:42.490 : Retrieved 0 items
23/07/2025 : 13:03:43.643 : dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:03:43.645 : dateMaxFacture = 
23/07/2025 : 13:03:43.646 : sortcolumn = numfac
23/07/2025 : 13:03:43.647 : sortdir = ascending
23/07/2025 : 13:08:40.263 : dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:08:40.263 : dateMaxFacture = 
23/07/2025 : 13:08:40.264 : sortcolumn = numfac
23/07/2025 : 13:08:40.264 : sortdir = ascending
23/07/2025 : 13:09:03.209 : Retrieved 0 items
23/07/2025 : 19:41:09.702 : dateMinFacture = 
23/07/2025 : 19:41:09.704 : dateMaxFacture = 
23/07/2025 : 19:41:09.704 : sortcolumn = numfac
23/07/2025 : 19:41:09.704 : sortdir = ascending
23/07/2025 : 19:41:09.710 : Formatted dateMinParam = 
23/07/2025 : 19:41:09.710 : Formatted dateMaxParam = 
23/07/2025 : 19:41:14.735 : Retrieved 14 items
23/07/2025 : 19:41:19.732 : dateMinFacture = 
23/07/2025 : 19:41:19.733 : dateMaxFacture = 
23/07/2025 : 19:41:19.733 : sortcolumn = numfac
23/07/2025 : 19:41:19.734 : sortdir = ascending
23/07/2025 : 19:41:19.734 : Formatted dateMinParam = 
23/07/2025 : 19:41:19.735 : Formatted dateMaxParam = 
23/07/2025 : 19:41:23.764 : Retrieved 14 items
23/07/2025 : 19:43:48.081 : dateMinFacture = 
23/07/2025 : 19:43:48.082 : dateMaxFacture = 
23/07/2025 : 19:43:48.082 : sortcolumn = numfac
23/07/2025 : 19:43:48.083 : sortdir = ascending
23/07/2025 : 19:43:48.084 : Formatted dateMinParam = 
23/07/2025 : 19:43:48.085 : Formatted dateMaxParam = 
23/07/2025 : 19:43:50.371 : Retrieved 14 items
23/07/2025 : 19:43:52.643 : dateMinFacture = 
23/07/2025 : 19:43:52.643 : dateMaxFacture = 
23/07/2025 : 19:43:52.644 : sortcolumn = numfac
23/07/2025 : 19:43:52.644 : sortdir = ascending
23/07/2025 : 19:43:52.644 : Formatted dateMinParam = 
23/07/2025 : 19:43:52.645 : Formatted dateMaxParam = 
23/07/2025 : 19:43:53.127 : Retrieved 14 items
23/07/2025 : 19:44:07.353 : dateMinFacture = 
23/07/2025 : 19:44:07.354 : dateMaxFacture = 
23/07/2025 : 19:44:07.358 : sortcolumn = datefac
23/07/2025 : 19:44:07.359 : sortdir = ascending
23/07/2025 : 19:44:07.361 : Formatted dateMinParam = 
23/07/2025 : 19:44:07.362 : Formatted dateMaxParam = 
23/07/2025 : 19:44:38.867 : dateMinFacture = 
23/07/2025 : 19:44:38.867 : dateMaxFacture = 
23/07/2025 : 19:44:38.868 : sortcolumn = datefac
23/07/2025 : 19:44:38.868 : sortdir = descending
23/07/2025 : 19:44:38.869 : Formatted dateMinParam = 
23/07/2025 : 19:44:38.869 : Formatted dateMaxParam = 
23/07/2025 : 19:44:51.238 : Retrieved 14 items
23/07/2025 : 19:45:06.140 : dateMinFacture = 12/07/2025 00:00:00
23/07/2025 : 19:45:06.140 : dateMaxFacture = 
23/07/2025 : 19:45:06.140 : sortcolumn = datefac
23/07/2025 : 19:45:06.141 : sortdir = descending
23/07/2025 : 19:45:06.141 : Formatted dateMinParam = 2025-07-12 00:00:00
23/07/2025 : 19:45:06.141 : Formatted dateMaxParam = 
23/07/2025 : 19:45:14.031 : Retrieved 0 items
23/07/2025 : 20:10:23.312 : dateMinFacture = 12/07/2025 00:00:00
23/07/2025 : 20:10:23.312 : dateMaxFacture = 
23/07/2025 : 20:10:23.312 : sortcolumn = datefac
23/07/2025 : 20:10:23.313 : sortdir = descending
23/07/2025 : 20:10:23.314 : Formatted dateMinParam = 2025-07-12 00:00:00
23/07/2025 : 20:10:23.314 : Formatted dateMaxParam = 
23/07/2025 : 23:40:22.599 : dateMinFacture = 
23/07/2025 : 23:40:22.606 : dateMaxFacture = 
23/07/2025 : 23:40:22.607 : sortcolumn = numfac
23/07/2025 : 23:40:22.607 : sortdir = ascending
23/07/2025 : 23:40:22.613 : Formatted dateMinParam = 
23/07/2025 : 23:40:22.613 : Formatted dateMaxParam = 
23/07/2025 : 23:40:27.566 : dateMinFacture = 
23/07/2025 : 23:40:27.566 : dateMaxFacture = 
23/07/2025 : 23:40:27.566 : sortcolumn = datefac
23/07/2025 : 23:40:27.567 : sortdir = descending
23/07/2025 : 23:40:27.571 : Formatted dateMinParam = 
23/07/2025 : 23:40:27.572 : Formatted dateMaxParam = 
23/07/2025 : 23:41:12.954 : dateMinFacture = 
23/07/2025 : 23:41:12.954 : dateMaxFacture = 
23/07/2025 : 23:41:12.955 : sortcolumn = datefac
23/07/2025 : 23:41:12.955 : sortdir = descending
23/07/2025 : 23:41:12.955 : Formatted dateMinParam = 
23/07/2025 : 23:41:12.956 : Formatted dateMaxParam = 
23/07/2025 : 23:41:20.729 : dateMinFacture = 
23/07/2025 : 23:41:20.729 : dateMaxFacture = 
23/07/2025 : 23:41:20.729 : sortcolumn = datefac
23/07/2025 : 23:41:20.729 : sortdir = descending
23/07/2025 : 23:41:20.730 : Formatted dateMinParam = 
23/07/2025 : 23:41:20.730 : Formatted dateMaxParam = 
23/07/2025 : 23:48:55.620 : dateMinFacture = 
23/07/2025 : 23:48:55.621 : dateMaxFacture = 
23/07/2025 : 23:48:55.621 : sortcolumn = numfac
23/07/2025 : 23:48:55.622 : sortdir = ascending
23/07/2025 : 23:48:55.625 : Formatted dateMinParam = 
23/07/2025 : 23:48:55.626 : Formatted dateMaxParam = 
23/07/2025 : 23:48:59.397 : dateMinFacture = 
23/07/2025 : 23:48:59.397 : dateMaxFacture = 
23/07/2025 : 23:48:59.397 : sortcolumn = datefac
23/07/2025 : 23:48:59.397 : sortdir = descending
23/07/2025 : 23:48:59.398 : Formatted dateMinParam = 
23/07/2025 : 23:48:59.399 : Formatted dateMaxParam = 
23/07/2025 : 23:57:40.470 : dateMinFacture = 
23/07/2025 : 23:57:40.471 : dateMaxFacture = 
23/07/2025 : 23:57:40.471 : sortcolumn = numfac
23/07/2025 : 23:57:40.471 : sortdir = ascending
23/07/2025 : 23:57:40.474 : Formatted dateMinParam = 
23/07/2025 : 23:57:40.474 : Formatted dateMaxParam = 
23/07/2025 : 23:57:43.950 : Retrieved 14 items
23/07/2025 : 23:57:48.030 : dateMinFacture = 
23/07/2025 : 23:57:48.030 : dateMaxFacture = 
23/07/2025 : 23:57:48.031 : sortcolumn = datefac
23/07/2025 : 23:57:48.031 : sortdir = descending
23/07/2025 : 23:57:48.031 : Formatted dateMinParam = 
23/07/2025 : 23:57:48.032 : Formatted dateMaxParam = 
23/07/2025 : 23:58:05.366 : Retrieved 14 items
23/07/2025 : 23:58:17.002 : dateMinFacture = 
23/07/2025 : 23:58:17.002 : dateMaxFacture = 
23/07/2025 : 23:58:17.002 : sortcolumn = datefac
23/07/2025 : 23:58:17.002 : sortdir = descending
23/07/2025 : 23:58:17.002 : Formatted dateMinParam = 
23/07/2025 : 23:58:17.002 : Formatted dateMaxParam = 
23/07/2025 : 23:58:22.946 : Retrieved 14 items
23/07/2025 : 23:58:40.635 : dateMinFacture = 
23/07/2025 : 23:58:40.635 : dateMaxFacture = 10/07/2020 00:00:00
23/07/2025 : 23:58:40.636 : sortcolumn = datefac
23/07/2025 : 23:58:40.636 : sortdir = descending
23/07/2025 : 23:58:40.636 : Formatted dateMinParam = 
23/07/2025 : 23:58:40.637 : Formatted dateMaxParam = 2020-07-10
23/07/2025 : 23:58:48.843 : Retrieved 14 items
