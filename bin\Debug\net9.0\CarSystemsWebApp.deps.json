{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"CarSystemsWebApp/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": "9.0.3", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.3", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.3", "Microsoft.EntityFrameworkCore.Tools": "9.0.3", "Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Calendars": "28.1.33", "Syncfusion.Blazor.Charts": "28.1.33", "Syncfusion.Blazor.DataForm": "28.1.33", "Syncfusion.Blazor.Diagram": "28.1.33", "Syncfusion.Blazor.DropDowns": "28.1.33", "Syncfusion.Blazor.Grid": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33", "Syncfusion.Blazor.Layouts": "28.1.33", "Syncfusion.Blazor.Lists": "28.1.33", "Syncfusion.Blazor.Navigations": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.ProgressBar": "28.1.33", "Syncfusion.Blazor.RichTextEditor": "28.1.33", "Syncfusion.Blazor.Schedule": "28.1.33", "Syncfusion.Blazor.Themes": "28.1.33", "Syncfusion.Blazor.TreeGrid": "28.1.33", "Syncfusion.Pdf.Net.Core": "28.1.33", "Syncfusion.XlsIO.Net.Core": "28.1.33", "Syncfusion.XlsIORenderer.Net.Core": "28.1.33"}, "runtime": {"CarSystemsWebApp.dll": {}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "BitMiracle.LibTiff.NET/2.4.649": {"runtime": {"lib/netstandard2.0/BitMiracle.LibTiff.NET.dll": {"assemblyVersion": "2.4.649.0", "fileVersion": "2.4.649.0"}}}, "HarfBuzzSharp/7.3.0.2": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.2", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.2"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.0.2"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Authorization/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}}, "Microsoft.AspNetCore.Components/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Analyzers": "9.0.0"}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.0": {}, "Microsoft.AspNetCore.Components.Forms/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.0"}}, "Microsoft.AspNetCore.Components.Web/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Forms": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3", "Microsoft.JSInterop": "9.0.0"}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.3": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.3", "Microsoft.Extensions.Identity.Stores": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Metadata/9.0.0": {}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.6": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.3", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.3", "Microsoft.Extensions.Caching.Memory": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.3": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.3": {}, "Microsoft.EntityFrameworkCore.Design/9.0.3": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.3", "Microsoft.Extensions.Caching.Memory": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyModel": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.3", "Microsoft.Extensions.Caching.Memory": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.3": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.3", "Microsoft.Extensions.Caching.Memory": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "System.Formats.Asn1": "9.0.3", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.3"}}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Caching.Memory/9.0.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyModel/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.3", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Identity.Core/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.Extensions.Identity.Stores/9.0.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.3", "Microsoft.Extensions.Identity.Core": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.Extensions.Logging/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Options/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Primitives/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.JSInterop/9.0.0": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.8.0"}}}, "SkiaSharp.HarfBuzz/2.88.8": {"dependencies": {"HarfBuzzSharp": "7.3.0.2", "SkiaSharp": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.8.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Syncfusion.Blazor.Buttons/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Buttons.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Calendars/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33", "Syncfusion.Blazor.Lists": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Calendars.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Charts/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.DataVizCommon": "28.1.33", "Syncfusion.ExcelExport.Net.Core": "28.1.33", "Syncfusion.PdfExport.Net.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Charts.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Core/28.1.33": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.0", "Syncfusion.Licensing": "28.1.33", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Core.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Data/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Data.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.DataForm/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Calendars": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.DropDowns": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.DataForm.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.DataVizCommon/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.DataVizCommon.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Diagram/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.Navigations": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Diagram.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.DropDowns/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33", "Syncfusion.Blazor.Notifications": "28.1.33", "Syncfusion.Blazor.Spinner": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.DropDowns.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Grid/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Calendars": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.DropDowns": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33", "Syncfusion.Blazor.Navigations": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.Spinner": "28.1.33", "Syncfusion.ExcelExport.Net.Core": "28.1.33", "Syncfusion.PdfExport.Net.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Grids.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Inputs/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.Spinner": "28.1.33", "Syncfusion.Blazor.SplitButtons": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Inputs.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Layouts/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Layouts.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Lists/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Lists.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Navigations/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.DropDowns": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33", "Syncfusion.Blazor.Lists": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.Spinner": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Navigations.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Notifications/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Notifications.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Popups/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Popups.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.ProgressBar/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.ProgressBar.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.RichTextEditor/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33", "Syncfusion.Blazor.Navigations": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.SplitButtons": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.RichTextEditor.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Schedule/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Calendars": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.DropDowns": "28.1.33", "Syncfusion.Blazor.Inputs": "28.1.33", "Syncfusion.Blazor.Navigations": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.Spinner": "28.1.33", "Syncfusion.ExcelExport.Net.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Schedule.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Spinner/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Spinner.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.SplitButtons/28.1.33": {"dependencies": {"Syncfusion.Blazor.Buttons": "28.1.33", "Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.Spinner": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.SplitButtons.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.Themes/28.1.33": {"runtime": {"lib/net9.0/Syncfusion.Blazor.Themes.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Blazor.TreeGrid/28.1.33": {"dependencies": {"Syncfusion.Blazor.Core": "28.1.33", "Syncfusion.Blazor.Data": "28.1.33", "Syncfusion.Blazor.Grid": "28.1.33", "Syncfusion.Blazor.Navigations": "28.1.33", "Syncfusion.Blazor.Popups": "28.1.33", "Syncfusion.Blazor.Spinner": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.TreeGrid.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Compression.Net.Core/28.1.33": {"runtime": {"lib/net9.0/Syncfusion.Compression.Portable.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.ExcelExport.Net.Core/28.1.33": {"runtime": {"lib/net9.0/Syncfusion.ExcelExport.Net.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Licensing/28.1.33": {"runtime": {"lib/net9.0/Syncfusion.Licensing.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.MetafileRenderer.NET/28.1.33": {"dependencies": {"SkiaSharp": "2.88.8", "Syncfusion.Licensing": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.MetafileRenderer.NET.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Pdf.Imaging.Net.Core/28.1.33": {"dependencies": {"BitMiracle.LibTiff.NET": "2.4.649", "SkiaSharp": "2.88.8", "Syncfusion.Compression.Net.Core": "28.1.33", "Syncfusion.Pdf.Net.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.Pdf.Imaging.Portable.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.Pdf.Net.Core/28.1.33": {"dependencies": {"Syncfusion.Compression.Net.Core": "28.1.33", "Syncfusion.Licensing": "28.1.33", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/net9.0/Syncfusion.Pdf.Portable.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.PdfExport.Net.Core/28.1.33": {"runtime": {"lib/net9.0/Syncfusion.PdfExport.Net.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.SkiaSharpHelper.Net.Core/28.1.33": {"dependencies": {"SkiaSharp.HarfBuzz": "2.88.8", "Syncfusion.Compression.Net.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.SkiaSharpHelper.Portable.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.XlsIO.Net.Core/28.1.33": {"dependencies": {"Syncfusion.Compression.Net.Core": "28.1.33", "Syncfusion.Licensing": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.XlsIO.Portable.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "Syncfusion.XlsIORenderer.Net.Core/28.1.33": {"dependencies": {"SkiaSharp": "2.88.8", "Syncfusion.Compression.Net.Core": "28.1.33", "Syncfusion.MetafileRenderer.NET": "28.1.33", "Syncfusion.Pdf.Imaging.Net.Core": "28.1.33", "Syncfusion.Pdf.Net.Core": "28.1.33", "Syncfusion.SkiaSharpHelper.Net.Core": "28.1.33", "Syncfusion.XlsIO.Net.Core": "28.1.33"}, "runtime": {"lib/net9.0/Syncfusion.XlsIORenderer.Portable.dll": {"assemblyVersion": "28.1.33.0", "fileVersion": "28.1.33.0"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/9.0.3": {}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "System.IO.Pipelines/7.0.0": {}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "9.0.3"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "9.0.3"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/9.0.3": {}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}}}, "libraries": {"CarSystemsWebApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "BitMiracle.LibTiff.NET/2.4.649": {"type": "package", "serviceable": true, "sha512": "sha512-XCEdfCphKo0UAvSg1h/OzOFWtjkIGnPMAziYkNqV7NVrqb/tZJTBxJhxIEX7gpvQwg8GIRYt133SUncfkKfjLA==", "path": "bitmiracle.libtiff.net/2.4.649", "hashPath": "bitmiracle.libtiff.net.2.4.649.nupkg.sha512"}, "HarfBuzzSharp/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-0tCd6HyCmNsX/DniCp2b00fo0xPbdNwKOs9BxxyT8oOOuMlWjcSFwzONKyeckCKVBFEsbSmsAHPDTqxoSDwZMg==", "path": "harfbuzzsharp/7.3.0.2", "hashPath": "harfbuzzsharp.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nycYH/WLJ6ogm+I+QSFCdPJsdxSb5GANWYbQyp1vsd/KjXN56RVUJWPhbgP2GKb/Y7mrsHM7EProqVXlO/EMsA==", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.macos.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-DpF9JBzwws2dupOLnjME65hxQWWbN/GD40AoTkwB4S05WANvxo3n81AnQJKxWDCnrWfWhLPB36OF27TvEqzb/A==", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.win32.7.3.0.2.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qDJlBC5pUQ/3o6/C6Vuo9CGKtV5TAe5AdKeHvDR2bgmw8vwPxsAy3KG5eU0i1C+iAUNbmq+iDTbiKt16f9pRiA==", "path": "microsoft.aspnetcore.authorization/9.0.0", "hashPath": "microsoft.aspnetcore.authorization.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xKzY0LRqWrwuPVzKIF9k1kC21NrLmIE2qPhhKlInEAdYqNe8qcMoPWZy7fo1uScHkz5g73nTqDDra3+aAV7mTQ==", "path": "microsoft.aspnetcore.components/9.0.0", "hashPath": "microsoft.aspnetcore.components.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-maOE1qlJ9hf1Fb7PhFLw9bgP9mWckuDOcn1uKNt9/msdJG2YHl3cPRHojYa6CxliGHIXL8Da4qPgeUc4CaOoeg==", "path": "microsoft.aspnetcore.components.analyzers/9.0.0", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-orHGxDkbAa9syuaLVtZWOhNC8IddnCsDqpFaKjBj4zxe+B8cd6kcNf/t4Lv5hWBQ7mODiRCzEfKBnpU+GCHvbw==", "path": "microsoft.aspnetcore.components.forms/9.0.0", "hashPath": "microsoft.aspnetcore.components.forms.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZfJwwV05T+268cnJsO6yfi9oXYLe3ATRAEk0VZgBMptA5HVsduIsnFLjhNOYT7+I8NolxDEx1CEW8yKe5xTb6Q==", "path": "microsoft.aspnetcore.components.web/9.0.0", "hashPath": "microsoft.aspnetcore.components.web.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tKXLFMMefKRyDbbVJM+5Gtd1CgPS6vwc9yTLnaStT9+DmkI6iFrEOdKMjZt5q3ijqQVjWew0/EcB9EbEKeP0DA==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.3", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-gkhJ1uMfYnOH7hylbLx4VxeHHIpNWKEMXAZH3BJwUd+pkTGbm0etmL2dzHJLBrlblvTjZy4e9sg8Tpvkfetj7A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.3", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-1kN6Z2pclLO+Q0ljKaQMh54y5qJkNekAwtov6upT62sv51RZlVYIOREEnWWJWVVprt/PSz6LHVCMW45nb6C+zQ==", "path": "microsoft.aspnetcore.diagnostics.entityframeworkcore/9.0.3", "hashPath": "microsoft.aspnetcore.diagnostics.entityframeworkcore.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-qC4KdZVBI+aKRIcTVkepcfs0VDlcXT6yOQshyG4lDcBhWcingOGU87c19pQBqOB8cG9/cGpphet4xCgMBXQZrQ==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.3", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-X81C891nMuWgzNHyZ0C3s+blSDxRHzQHDFYQoOKtFvFuxGq3BbkLbc5CfiCqIzA/sWIfz6u8sGBgwntQwBJWBw==", "path": "microsoft.aspnetcore.metadata/9.0.0", "hashPath": "microsoft.aspnetcore.metadata.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-+pz7gIPh5ydsBcQvivt4R98PwJXer86fyQBBToIBLxZ5kuhW4N13Ijz87s9WpuPtF1vh4JesYCgpDPAOgkMhdg==", "path": "microsoft.data.sqlclient/5.1.6", "hashPath": "microsoft.data.sqlclient.5.1.6.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ji6fWE8bFWTvqaMue49RSCcAeqlec13hAbitTBZyz/RmYsQDhqM9VkWUoXS1x1mjcsyWjc5EQr2XyuAkwp0eNQ==", "path": "microsoft.entityframeworkcore/9.0.3", "hashPath": "microsoft.entityframeworkcore.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HQH7HgwTl6IyasW7t6F2+ihuHLDmf9+8XaS41v8VHN5Z7x5ZMQIiHCbwxme4P5ICAjBG6VsWcjMCoh4fHbEwJg==", "path": "microsoft.entityframeworkcore.abstractions/9.0.3", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-M+iOxejD3HqPV8/KE148wIehHA3cmMF+FgnpiN8CFB7DLokVSKQPPrwtHjcrNYJ/BDEssviEPNok/jtQWxj7xA==", "path": "microsoft.entityframeworkcore.analyzers/9.0.3", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-n5ZrAZ4RFELxYjJxN74lzmFlRrtSpYEYZfZcIJIWPGjSigwJsbya2CnOdjSVDPyfx3rKl9rzbd72D2DNHBJWeA==", "path": "microsoft.entityframeworkcore.design/9.0.3", "hashPath": "microsoft.entityframeworkcore.design.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-xsS+5TM7M5f3tCSRaSbzouGCoIgD2zokQxBGXvf9z3DusRztWvT1NNT9XJaY2JoK1qEEDcHah8is6azYmpZhIg==", "path": "microsoft.entityframeworkcore.relational/9.0.3", "hashPath": "microsoft.entityframeworkcore.relational.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-poKK0h5fldiBkpk/L/xq9Scd4FpnnZPj0SAKArnykqSMVJErd9iKtmCG5CjCpSPHrSdEyk8Uc4WUIWJ55i4JFg==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.3", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-c2kkyHhkop9WAbBWgm64apzPbzMeu0iKsRW3cn5kxSge0SQNxSlkAOtnO4L9dc8YwCAtFFyAZrxVFekxyvxKwQ==", "path": "microsoft.entityframeworkcore.tools/9.0.3", "hashPath": "microsoft.entityframeworkcore.tools.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-t8b0R6wtqC4o0hJ+oQkLPydw2MMLEoLEpQXCWbzXAm9NBMOngkDZNcvwF6DxbYdL5SlfZJXbYmiOxKZmwHNgNg==", "path": "microsoft.extensions.caching.abstractions/9.0.3", "hashPath": "microsoft.extensions.caching.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TXggBGDDd6r+J7FV09plXpzGmWcknVyoDsHlY2qcCbcAhmb0eH7Q9IkfIZl54/zEedVTa9jPgiPFTxH9WuCGMQ==", "path": "microsoft.extensions.caching.memory/9.0.3", "hashPath": "microsoft.extensions.caching.memory.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-q5qlbm6GRUrle2ZZxy9aqS/wWoc+mRD3JeP6rcpiJTh5XcemYkplAcJKq8lU11ZfPom5lfbZZfnQvDqcUhqD5Q==", "path": "microsoft.extensions.configuration.abstractions/9.0.3", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "path": "microsoft.extensions.dependencyinjection/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-194P+NOekDXrPHmM2R8678T4bRfZ2isQXDDAsXKE5qI0QLUnXbwB0upljAkyxk+Kkt1DV1tV+9tHOtHEEh3ksw==", "path": "microsoft.extensions.dependencymodel/9.0.3", "hashPath": "microsoft.extensions.dependencymodel.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-3cmtvVU2ejnXPI9drZNruEygs3utAtF+My9vcFomIN5nCDwVCQv2IJjZntJW0iyn2WM0epIsUGWdy+OfNNET8Q==", "path": "microsoft.extensions.identity.core/9.0.3", "hashPath": "microsoft.extensions.identity.core.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-IlOCMVEXGZ2uKpbtKaAMdq6LEeIc3VXlH0SO2gxGckgI7LFSVGWpzmB0ebrQEeiVyzD4yDKS8UH9Pk0Zgpugfw==", "path": "microsoft.extensions.identity.stores/9.0.3", "hashPath": "microsoft.extensions.identity.stores.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "path": "microsoft.extensions.logging/9.0.3", "hashPath": "microsoft.extensions.logging.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "path": "microsoft.extensions.logging.abstractions/9.0.3", "hashPath": "microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "path": "microsoft.extensions.options/9.0.3", "hashPath": "microsoft.extensions.options.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "path": "microsoft.extensions.primitives/9.0.3", "hashPath": "microsoft.extensions.primitives.9.0.3.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.JSInterop/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-efQKxKUPe8OuH0hRiYsvBJkhhPzIYFNcr9+3wanQ7Bch/wr1JWNd90GYiPLtkSHepE1zMEoaLkAxi5N5/eyC4Q==", "path": "microsoft.jsinterop/9.0.0", "hashPath": "microsoft.jsinterop.9.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.HarfBuzz/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-ajSyJ2D17R0kZ4FwKwFrJTsYs3D3Y9iRBLhNecROR7dOxC6VTFaMPXJuwQB8KYpAqgmb2JAJFEgZ3i8MaaIw1g==", "path": "skiasharp.harfbuzz/2.88.8", "hashPath": "skiasharp.harfbuzz.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "Syncfusion.Blazor.Buttons/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-CL2dqD+eelQVrKYP6Uz+9WmCYiBUaQifokRfp6YiXv0ipr8yNvlo4OvXsrT1iJHI3GBWCulymwoN0FtyHRtmvQ==", "path": "syncfusion.blazor.buttons/28.1.33", "hashPath": "syncfusion.blazor.buttons.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Calendars/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-r3hGeAH19y1LzYyNLUNYtKIVFelO4EAxJJvoAZutITmqr4kA4wiTyMbv42xyE3cUoCYbzlZNO/Pvl3o8luWBxA==", "path": "syncfusion.blazor.calendars/28.1.33", "hashPath": "syncfusion.blazor.calendars.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Charts/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-ilyr4RjLM9g+T4bjaCgbmB45aylwKoTVlFhgr+7xUUTLfS1TmT+IJ4+7/TJSgdOkVnDLngpm66xhIIZLUyWxeg==", "path": "syncfusion.blazor.charts/28.1.33", "hashPath": "syncfusion.blazor.charts.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-54bcyv8S0YJYJG4u0fipfuAdkrAHY/fZ/x7N5FXXP+RK39gnEO5KG4NotgJDPjkXdGbkfXwZffdlEnEWwNkAJw==", "path": "syncfusion.blazor.core/28.1.33", "hashPath": "syncfusion.blazor.core.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Data/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-k3044nOsh76ZKoZXJXKYpTk+4Rl2pxDIs74SwEkYU6cMK1WdlWkIPBiYZk781bKW2WPD6bF7OtnNI+ydSlrtww==", "path": "syncfusion.blazor.data/28.1.33", "hashPath": "syncfusion.blazor.data.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.DataForm/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-pjdsmlLdRQi4MKBjrFGAz6V3FFGELWNQlbea97LUAyK4IgtkQKX8164xzdbtzqYzuhjd9v5bptvlXmJcVhaE+g==", "path": "syncfusion.blazor.dataform/28.1.33", "hashPath": "syncfusion.blazor.dataform.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.DataVizCommon/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-rkU7kNja2LLmSa0vcYw7nQdngsxa3ZdijWGGk1gqUOeOKU+Cbbitlm6qo+ssmo+WMrAV+WfwM62NNcP5Nts5yA==", "path": "syncfusion.blazor.datavizcommon/28.1.33", "hashPath": "syncfusion.blazor.datavizcommon.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Diagram/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-sUaoBAjGBIGhskKO/KdjjhEkbAGRIi74rWxsmANWIjRvy5+Ity/89RsTRMZDLkoPp0Nn4SPekOJBrA6sibJYBQ==", "path": "syncfusion.blazor.diagram/28.1.33", "hashPath": "syncfusion.blazor.diagram.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.DropDowns/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-rmpcX0YRhrSgWpj2I7GfAbJ5eI5Nh6phjreNscK791UT++RobPe5drGc7Tx7Dyp1cYgWQ7riVM9ke0QFJSrZnA==", "path": "syncfusion.blazor.dropdowns/28.1.33", "hashPath": "syncfusion.blazor.dropdowns.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Grid/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-a2/u9yUhzmkQTBJ+dQt+7aOZ76XjlYLDYUivTR3SYNgbTxQcDSBuGS7eFIw/ZW+36BceJBF/x5boFcEA0Hs1XA==", "path": "syncfusion.blazor.grid/28.1.33", "hashPath": "syncfusion.blazor.grid.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Inputs/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-lRC9p4uGuT5/y8ptfuJZZXIZJ8FobgqrNADA9yfD1OPPcQmy2aRqoz6B0vDAnejYj7Bwafy8lAcoJ5rqMLkQsw==", "path": "syncfusion.blazor.inputs/28.1.33", "hashPath": "syncfusion.blazor.inputs.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Layouts/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-G3xLuDgNL/iRD4lfZfrcZ5fdPwbxUmgor+QDMMVT3fm47UdnpzD/YRmIh0TMWfY+jI1a436NAJ6qTRxb/NptbA==", "path": "syncfusion.blazor.layouts/28.1.33", "hashPath": "syncfusion.blazor.layouts.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Lists/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-ILwfE1XPYBGwg0Vf0yUMkK0ylnRLUfUueGS+feCgePbn8s4pRlzrRYINnIC0+3k1pvlItoBxvI34/zUVys0OaQ==", "path": "syncfusion.blazor.lists/28.1.33", "hashPath": "syncfusion.blazor.lists.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Navigations/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-kqeg/uDX5in0Hn1meELcQWMXAOQU/RnCrOoQCWLD15gP5iGeaHO+7YoTpaOjmaWh/QsL0dlV3sVUpBqW0+wTMA==", "path": "syncfusion.blazor.navigations/28.1.33", "hashPath": "syncfusion.blazor.navigations.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Notifications/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-YUT+yCoJxi3cnX5F35oVcLLMsaelhck9rVUrWDhgwpIigHFJDlrwIghMaItQ+ApxMPIoWpklz8/bKbOyf4OXFg==", "path": "syncfusion.blazor.notifications/28.1.33", "hashPath": "syncfusion.blazor.notifications.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Popups/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-rW4C5yFyPe10z/f0QvfcK2LpMhqAChqFpLOyCF/FJvgtasZOgQnZa/DBjnNZFd1IrMrZdRwtuyDEJorJqNRwUw==", "path": "syncfusion.blazor.popups/28.1.33", "hashPath": "syncfusion.blazor.popups.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.ProgressBar/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-aNakFjmMh6r6k8hu6lgaOeUh3JyZOA4XiSsekKW3jTxFW3l51BS7Jnsap4cNCbo7ZNdv5jCKclHM+m2Cm7bJAQ==", "path": "syncfusion.blazor.progressbar/28.1.33", "hashPath": "syncfusion.blazor.progressbar.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.RichTextEditor/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-Ihme9FcGU93Ktu9gkLmf2xNYoqS82Zj7qo6MpQ7LR1mh1fK+ydSjVOTTZxsKBwdjViN4TvCyLJarDsmRdp2mZg==", "path": "syncfusion.blazor.richtexteditor/28.1.33", "hashPath": "syncfusion.blazor.richtexteditor.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Schedule/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-aeHT3m5rdk0V53Nh/X6ybvOXz3pEKwMT3l4ICXszuuwYU8e1PVC4fnZ7Whh7JcmDHhPI+MTd9OXZj4rB7TagJg==", "path": "syncfusion.blazor.schedule/28.1.33", "hashPath": "syncfusion.blazor.schedule.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Spinner/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-6BVU5jowqC4gned37gYZRF7rA6fzQXfLXozuSXn/CkzAKsa5fbhbS2T1lpmlxwa7qquNZsYPb3XCeweu6Yd7bA==", "path": "syncfusion.blazor.spinner/28.1.33", "hashPath": "syncfusion.blazor.spinner.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.SplitButtons/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-Jc1kOvwI3SUP2ZkUjAwSE6+6EmCDP3N0gWNzZOGi1PuSDMh62iOQHjkwrq9M7XxNns5tiI9mnfelZqBpFeJwmA==", "path": "syncfusion.blazor.splitbuttons/28.1.33", "hashPath": "syncfusion.blazor.splitbuttons.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.Themes/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-nVQ3fBXhsz7niBgC9yV/YNRrn3J9r3Jxy4xRWTCZjCtImLzDOaSC3M4ncj56CcVsMFTJp/BQKOG3URznh4xCww==", "path": "syncfusion.blazor.themes/28.1.33", "hashPath": "syncfusion.blazor.themes.28.1.33.nupkg.sha512"}, "Syncfusion.Blazor.TreeGrid/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-uZTHiUGJsKBFTOouT5rRE0orgDbBA1MkZIpb6E6D2/E/Kdzv57Wbltmh2fCuwC1V13074JGVD64dOqphzHZSJg==", "path": "syncfusion.blazor.treegrid/28.1.33", "hashPath": "syncfusion.blazor.treegrid.28.1.33.nupkg.sha512"}, "Syncfusion.Compression.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-jUTCqN/ciR9Xezz2TEVVpQZOOtGBJkI4zW9eavPQQa9b/zBjgd//qhgcbtdDHFKTNfImIGUDtz2Ay+eNr0lrzQ==", "path": "syncfusion.compression.net.core/28.1.33", "hashPath": "syncfusion.compression.net.core.28.1.33.nupkg.sha512"}, "Syncfusion.ExcelExport.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-Ev6QVeyOVllKxJQQ5HUPmpwP9By4mUv5q3KtQFOjFrw4dmV8j/X9bSyAxmaUP6lrYkJ7txOpuZlJ9pZoHPIDbg==", "path": "syncfusion.excelexport.net.core/28.1.33", "hashPath": "syncfusion.excelexport.net.core.28.1.33.nupkg.sha512"}, "Syncfusion.Licensing/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-y7qeTcetesPWWmeLJQpo7SpFaQpDiMIyP8AOYxKoOchYDbar2/YAWKitc8CgFwwJjQmP7hgytA3MNA3vHnlmBg==", "path": "syncfusion.licensing/28.1.33", "hashPath": "syncfusion.licensing.28.1.33.nupkg.sha512"}, "Syncfusion.MetafileRenderer.NET/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-U1t1V13ZOaXHrXcfpCvVsi/oZ+ZF4kO2gvtgcdQOfjfJj0l4xIZU8Hc8pihVOsBVzkjDXMet07U1F8ROMfF91A==", "path": "syncfusion.metafilerenderer.net/28.1.33", "hashPath": "syncfusion.metafilerenderer.net.28.1.33.nupkg.sha512"}, "Syncfusion.Pdf.Imaging.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-WsIyEpDAhxQYfHeboilLh3BvrHqyTGLqlmf+EBcVRkYtCVW9+y14BZh7+4X+fvGBQhtVRapxY1/715cdlus1Sg==", "path": "syncfusion.pdf.imaging.net.core/28.1.33", "hashPath": "syncfusion.pdf.imaging.net.core.28.1.33.nupkg.sha512"}, "Syncfusion.Pdf.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-ZesoYRh5EC6ZqzYkLagL+nstASVRDT/tG0ZW9ii+K9iZAJ0ef1YxXnKzpT5L324ZzC4S4I6fsGJaqbhZkNkRsg==", "path": "syncfusion.pdf.net.core/28.1.33", "hashPath": "syncfusion.pdf.net.core.28.1.33.nupkg.sha512"}, "Syncfusion.PdfExport.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-SFQ5/PRPuCZKBcVyA83TFtor8nmBA7xeBKyHK/lz/N5+xpscafYOis5Hjh1YrTG5gHcf14naq9vZiSNKKYzgqA==", "path": "syncfusion.pdfexport.net.core/28.1.33", "hashPath": "syncfusion.pdfexport.net.core.28.1.33.nupkg.sha512"}, "Syncfusion.SkiaSharpHelper.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-AhE3QRIampihfpJkiyXo7IcWBqk7b3TSZHCHWH6aN4MSZwtxNZcLgb4Yh1Xa1x/v9WSWxZ9As+uuI6u+MdA3TQ==", "path": "syncfusion.skiasharphelper.net.core/28.1.33", "hashPath": "syncfusion.skiasharphelper.net.core.28.1.33.nupkg.sha512"}, "Syncfusion.XlsIO.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-6J3zxq683kbxyu+YDe9w5qA9ftHx3I2m0UzLF+Df58Ui+CIR9vW4sZZpKU4CZnbYPe6V0ULeup6cPWgptnCrWQ==", "path": "syncfusion.xlsio.net.core/28.1.33", "hashPath": "syncfusion.xlsio.net.core.28.1.33.nupkg.sha512"}, "Syncfusion.XlsIORenderer.Net.Core/28.1.33": {"type": "package", "serviceable": true, "sha512": "sha512-1lgLj+h2V9gaWdn3HdiGSmn1LxSw3c+Df24CGnY/eEBmiaQFK6MyqmnIUFaCPVfkAbGFXWnFTubKYarCQ6y4vA==", "path": "syncfusion.xlsiorenderer.net.core/28.1.33", "hashPath": "syncfusion.xlsiorenderer.net.core.28.1.33.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-UzVZ0mGzqvpe5fbG6RKA0pEOuLsELtc5OzSqmCS/lE5sqAyLKm2IiozCHbJRoA5+EO9Wq/bGFuroYQZsbAu/bA==", "path": "system.formats.asn1/9.0.3", "hashPath": "system.formats.asn1.9.0.3.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-r2JRkLjsYrq5Dpo7+y3Wa73OfirZPdVhxiTJWwZ+oJM7FOAe0LkM3GlH+pgkNRdd1G1kwUbmRCdmh4uoaWwu1g==", "path": "system.text.json/9.0.3", "hashPath": "system.text.json.9.0.3.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}}}