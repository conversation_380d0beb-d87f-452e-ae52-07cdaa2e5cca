22/07/2025 : 10:37:19.643 : Starting Helper_GetData
22/07/2025 : 10:37:19.650 : SQL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 10:37:25.103 : ERROR in Helper_GetData: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:37:25.145 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 511
22/07/2025 : 10:37:25.461 : Starting Helper_GetData
22/07/2025 : 10:37:25.461 : SQL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 10:37:26.290 : ERROR in Helper_GetData: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:37:26.291 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 511
22/07/2025 : 10:39:52.794 : Starting Helper_GetData
22/07/2025 : 10:39:52.795 : SQL Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 10:39:53.336 : ERROR in Helper_GetData: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:39:53.337 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 511
22/07/2025 : 10:43:00.240 : Starting Helper_GetData
22/07/2025 : 10:43:00.244 : SQL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 10:43:04.643 : ERROR in Helper_GetData: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:43:04.676 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 512
22/07/2025 : 10:43:05.010 : Starting Helper_GetData
22/07/2025 : 10:43:05.011 : SQL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 10:43:05.787 : ERROR in Helper_GetData: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:43:05.787 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 512
22/07/2025 : 11:56:35.764 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
22/07/2025 : 11:56:38.443 : ERROR: The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:56:38.472 : STACK TRACE:    at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.BuildIndexMap(IReadOnlyList`1 columnNames, DbDataReader dataReader)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 483
22/07/2025 : 11:57:09.204 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
22/07/2025 : 11:57:09.586 : ERROR: The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:57:09.588 : STACK TRACE:    at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.BuildIndexMap(IReadOnlyList`1 columnNames, DbDataReader dataReader)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 483
