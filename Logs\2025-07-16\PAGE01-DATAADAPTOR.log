16/07/2025 : 00:02:24.139 : Entering
16/07/2025 : 00:02:24.147 :     Client           = 
16/07/2025 : 00:02:24.148 :     DateFactureStart = 
16/07/2025 : 00:02:24.149 :     DateFactureEnd   = 
16/07/2025 : 00:02:24.149 :     NO SORTING: Using default sort
16/07/2025 : 00:02:28.162 :     Items retrieved: 14
16/07/2025 : 00:02:31.106 :     Count = 8832132
16/07/2025 : 00:02:31.108 : Leaving
16/07/2025 : 00:02:31.602 : Entering
16/07/2025 : 00:02:31.603 :     Client           = 
16/07/2025 : 00:02:31.604 :     DateFactureStart = 
16/07/2025 : 00:02:31.604 :     DateFactureEnd   = 
16/07/2025 : 00:02:31.605 :     NO SORTING: Using default sort
16/07/2025 : 00:02:31.810 :     Items retrieved: 14
16/07/2025 : 00:02:35.178 :     Count = 8832132
16/07/2025 : 00:02:35.179 : Leaving
16/07/2025 : 00:09:41.140 : Entering
16/07/2025 : 00:09:41.141 :     Client           = 
16/07/2025 : 00:09:41.141 :     DateFactureStart = 
16/07/2025 : 00:09:41.142 :     DateFactureEnd   = 
16/07/2025 : 00:09:41.142 :     SORTING: Column = numfac, Direction = ascending
16/07/2025 : 00:09:42.911 :     Items retrieved: 14
16/07/2025 : 00:09:45.159 :     Count = 8832132
16/07/2025 : 00:09:45.160 : Leaving
16/07/2025 : 00:09:45.198 : Entering
16/07/2025 : 00:09:45.199 :     Client           = 
16/07/2025 : 00:09:45.199 :     DateFactureStart = 
16/07/2025 : 00:09:45.200 :     DateFactureEnd   = 
16/07/2025 : 00:09:45.200 :     SORTING: Column = numfac, Direction = descending
16/07/2025 : 00:09:46.191 :     Items retrieved: 14
16/07/2025 : 00:09:48.129 :     Count = 8832132
16/07/2025 : 00:09:48.130 : Leaving
16/07/2025 : 00:09:49.605 : Entering
16/07/2025 : 00:09:49.606 :     Client           = 
16/07/2025 : 00:09:49.606 :     DateFactureStart = 
16/07/2025 : 00:09:49.607 :     DateFactureEnd   = 
16/07/2025 : 00:09:49.608 :     NO SORTING: Using default sort
16/07/2025 : 00:09:49.678 :     Items retrieved: 14
16/07/2025 : 00:09:51.714 :     Count = 8832132
16/07/2025 : 00:09:51.715 : Leaving
16/07/2025 : 00:10:04.096 : Entering
16/07/2025 : 00:10:04.097 :     Client           = 0028
16/07/2025 : 00:10:04.098 :     DateFactureStart = 
16/07/2025 : 00:10:04.100 :     DateFactureEnd   = 
16/07/2025 : 00:10:04.100 :     NO SORTING: Using default sort
16/07/2025 : 00:10:09.745 :     Items retrieved: 14
16/07/2025 : 00:10:11.682 :     Count = 21368
16/07/2025 : 00:10:11.683 : Leaving
16/07/2025 : 00:10:18.345 : Entering
16/07/2025 : 00:10:18.346 :     Client           = 0028
16/07/2025 : 00:10:18.346 :     DateFactureStart = 
16/07/2025 : 00:10:18.347 :     DateFactureEnd   = 
16/07/2025 : 00:10:18.348 :     NO SORTING: Using default sort
16/07/2025 : 00:10:22.129 :     Items retrieved: 14
16/07/2025 : 00:10:24.175 :     Count = 21368
16/07/2025 : 00:10:24.176 : Leaving
16/07/2025 : 00:11:38.653 : Entering
16/07/2025 : 00:11:38.654 :     Client           = 
16/07/2025 : 00:11:38.654 :     DateFactureStart = 
16/07/2025 : 00:11:38.654 :     DateFactureEnd   = 
16/07/2025 : 00:11:38.654 :     NO SORTING: Using default sort
16/07/2025 : 00:11:38.718 :     Items retrieved: 14
16/07/2025 : 00:11:40.567 :     Count = 8832132
16/07/2025 : 00:11:40.568 : Leaving
16/07/2025 : 00:11:40.735 : Entering
16/07/2025 : 00:11:40.736 :     Client           = 
16/07/2025 : 00:11:40.736 :     DateFactureStart = 
16/07/2025 : 00:11:40.737 :     DateFactureEnd   = 
16/07/2025 : 00:11:40.737 :     NO SORTING: Using default sort
16/07/2025 : 00:11:40.807 :     Items retrieved: 14
16/07/2025 : 00:11:42.717 :     Count = 8832132
16/07/2025 : 00:11:42.718 : Leaving
16/07/2025 : 00:12:06.362 : Entering
16/07/2025 : 00:12:06.363 :     Client           = 
16/07/2025 : 00:12:06.364 :     DateFactureStart = 
16/07/2025 : 00:12:06.365 :     DateFactureEnd   = 
16/07/2025 : 00:12:06.366 :     SORTING: Column = codetiers, Direction = ascending
16/07/2025 : 00:12:06.440 :     Items retrieved: 14
16/07/2025 : 00:12:08.317 :     Count = 8832132
16/07/2025 : 00:12:08.318 : Leaving
16/07/2025 : 00:12:08.333 : Entering
16/07/2025 : 00:12:08.334 :     Client           = 
16/07/2025 : 00:12:08.334 :     DateFactureStart = 
16/07/2025 : 00:12:08.334 :     DateFactureEnd   = 
16/07/2025 : 00:12:08.335 :     SORTING: Column = codetiers, Direction = descending
16/07/2025 : 00:12:08.407 :     Items retrieved: 14
16/07/2025 : 00:12:10.194 :     Count = 8832132
16/07/2025 : 00:12:10.195 : Leaving
16/07/2025 : 08:12:13.730 : Entering
16/07/2025 : 08:12:13.731 :     Client           = 
16/07/2025 : 08:12:13.732 :     DateFactureStart = 
16/07/2025 : 08:12:13.732 :     DateFactureEnd   = 
16/07/2025 : 08:12:13.733 :     NO SORTING: Using default sort
16/07/2025 : 08:12:14.005 :     Items retrieved: 14
16/07/2025 : 08:12:16.617 :     Count = 8832132
16/07/2025 : 08:12:16.618 : Leaving
16/07/2025 : 08:12:22.765 : Entering
16/07/2025 : 08:12:22.766 :     Client           = 
16/07/2025 : 08:12:22.767 :     DateFactureStart = 
16/07/2025 : 08:12:22.768 :     DateFactureEnd   = 
16/07/2025 : 08:12:22.768 :     SORTING: Column = montant, Direction = ascending
16/07/2025 : 08:12:52.992 :     Items retrieved: 0
16/07/2025 : 08:12:55.632 :     Count = 8832132
16/07/2025 : 08:12:55.632 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:12:55.632 : Leaving
16/07/2025 : 08:12:55.633 : Entering
16/07/2025 : 08:12:55.634 :     Client           = 
16/07/2025 : 08:12:55.634 :     DateFactureStart = 
16/07/2025 : 08:12:55.634 :     DateFactureEnd   = 
16/07/2025 : 08:12:55.635 :     SORTING: Column = montant, Direction = ascending
16/07/2025 : 08:13:25.814 :     Items retrieved: 0
16/07/2025 : 08:13:27.683 :     Count = 8832132
16/07/2025 : 08:13:27.684 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:13:27.684 : Leaving
16/07/2025 : 08:15:11.083 : Entering
16/07/2025 : 08:15:11.084 :     Client           = 
16/07/2025 : 08:15:11.084 :     DateFactureStart = 
16/07/2025 : 08:15:11.085 :     DateFactureEnd   = 
16/07/2025 : 08:15:11.085 :     NO SORTING: Using default sort
16/07/2025 : 08:15:12.530 :     Items retrieved: 14
16/07/2025 : 08:15:16.421 :     Count = 8832132
16/07/2025 : 08:15:16.424 : Leaving
16/07/2025 : 08:15:16.680 : Entering
16/07/2025 : 08:15:16.681 :     Client           = 
16/07/2025 : 08:15:16.681 :     DateFactureStart = 
16/07/2025 : 08:15:16.681 :     DateFactureEnd   = 
16/07/2025 : 08:15:16.682 :     SORTING: Column = montant, Direction = ascending
16/07/2025 : 08:15:40.943 :     Entering
16/07/2025 : 08:15:40.943 :         Client           = 
16/07/2025 : 08:15:40.943 :         DateFactureStart = 
16/07/2025 : 08:15:40.944 :         DateFactureEnd   = 
16/07/2025 : 08:15:40.944 :         NO SORTING: Using default sort
16/07/2025 : 08:15:41.327 :         Items retrieved: 14
16/07/2025 : 08:15:44.678 :         Count = 8832132
16/07/2025 : 08:15:44.679 :     Leaving
16/07/2025 : 08:15:44.769 :     Entering
16/07/2025 : 08:15:44.770 :         Client           = 
16/07/2025 : 08:15:44.770 :         DateFactureStart = 
16/07/2025 : 08:15:44.770 :         DateFactureEnd   = 
16/07/2025 : 08:15:44.771 :         NO SORTING: Using default sort
16/07/2025 : 08:15:44.841 :         Items retrieved: 14
16/07/2025 : 08:15:46.977 :         Items retrieved: 0
16/07/2025 : 08:15:47.679 :         Count = 8832132
16/07/2025 : 08:15:47.680 :     Leaving
16/07/2025 : 08:15:49.401 :     Count = 8832132
16/07/2025 : 08:15:49.403 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:15:49.404 : Leaving
16/07/2025 : 08:16:00.555 : Entering
16/07/2025 : 08:16:00.555 :     Client           = 
16/07/2025 : 08:16:00.555 :     DateFactureStart = 
16/07/2025 : 08:16:00.555 :     DateFactureEnd   = 
16/07/2025 : 08:16:00.555 :     NO SORTING: Using default sort
16/07/2025 : 08:16:00.625 :     Items retrieved: 14
16/07/2025 : 08:16:03.037 :     Count = 8832132
16/07/2025 : 08:16:03.038 : Leaving
16/07/2025 : 08:16:03.109 : Entering
16/07/2025 : 08:16:03.110 :     Client           = 
16/07/2025 : 08:16:03.110 :     DateFactureStart = 
16/07/2025 : 08:16:03.110 :     DateFactureEnd   = 
16/07/2025 : 08:16:03.110 :     NO SORTING: Using default sort
16/07/2025 : 08:16:03.197 :     Items retrieved: 14
16/07/2025 : 08:16:06.124 :     Count = 8832132
16/07/2025 : 08:16:06.125 : Leaving
16/07/2025 : 08:20:13.720 : Entering
16/07/2025 : 08:20:13.720 :     Client           = 
16/07/2025 : 08:20:13.721 :     DateFactureStart = 
16/07/2025 : 08:20:13.721 :     DateFactureEnd   = 
16/07/2025 : 08:20:13.721 :     SORTING: Column = codeutilismodi, Direction = ascending
16/07/2025 : 08:20:15.671 :     Items retrieved: 14
16/07/2025 : 08:20:18.869 :     Count = 8832132
16/07/2025 : 08:20:18.869 : Leaving
16/07/2025 : 08:20:18.887 : Entering
16/07/2025 : 08:20:18.888 :     Client           = 
16/07/2025 : 08:20:18.888 :     DateFactureStart = 
16/07/2025 : 08:20:18.889 :     DateFactureEnd   = 
16/07/2025 : 08:20:18.889 :     SORTING: Column = codeutilismodi, Direction = descending
16/07/2025 : 08:20:20.175 :     Items retrieved: 14
16/07/2025 : 08:20:22.368 :     Count = 8832132
16/07/2025 : 08:20:22.369 : Leaving
16/07/2025 : 08:23:53.947 : Entering
16/07/2025 : 08:23:53.948 :     Client           = 
16/07/2025 : 08:23:53.948 :     DateFactureStart = 
16/07/2025 : 08:23:53.949 :     DateFactureEnd   = 
16/07/2025 : 08:23:53.949 :     NO SORTING: Using default sort
16/07/2025 : 08:23:54.022 :     Items retrieved: 14
16/07/2025 : 08:23:56.580 :     Count = 8832132
16/07/2025 : 08:23:56.581 : Leaving
16/07/2025 : 08:23:56.683 : Entering
16/07/2025 : 08:23:56.684 :     Client           = 
16/07/2025 : 08:23:56.684 :     DateFactureStart = 
16/07/2025 : 08:23:56.684 :     DateFactureEnd   = 
16/07/2025 : 08:23:56.684 :     SORTING: Column = codeutilismodi, Direction = descending
16/07/2025 : 08:23:57.863 :     Items retrieved: 14
16/07/2025 : 08:23:59.914 :     Count = 8832132
16/07/2025 : 08:23:59.915 : Leaving
16/07/2025 : 08:24:12.170 : Entering
16/07/2025 : 08:24:12.175 :     Client           = 
16/07/2025 : 08:24:12.176 :     DateFactureStart = 
16/07/2025 : 08:24:12.176 :     DateFactureEnd   = 
16/07/2025 : 08:24:12.176 :     NO SORTING: Using default sort
16/07/2025 : 08:24:14.269 :     Items retrieved: 14
16/07/2025 : 08:24:16.905 :     Count = 8832132
16/07/2025 : 08:24:16.906 : Leaving
16/07/2025 : 08:24:17.251 : Entering
16/07/2025 : 08:24:17.251 :     Client           = 
16/07/2025 : 08:24:17.252 :     DateFactureStart = 
16/07/2025 : 08:24:17.252 :     DateFactureEnd   = 
16/07/2025 : 08:24:17.252 :     NO SORTING: Using default sort
16/07/2025 : 08:24:17.387 :     Items retrieved: 14
16/07/2025 : 08:24:20.316 :     Count = 8832132
16/07/2025 : 08:24:20.316 : Leaving
16/07/2025 : 08:25:16.103 : Entering
16/07/2025 : 08:25:16.103 :     Client           = 
16/07/2025 : 08:25:16.106 :     DateFactureStart = 16/07/2025 00:00:00
16/07/2025 : 08:25:16.106 :     DateFactureEnd   = 10/07/2020 00:00:00
16/07/2025 : 08:25:16.106 :     NO SORTING: Using default sort
16/07/2025 : 08:25:46.212 :     Items retrieved: 0
16/07/2025 : 08:25:48.169 :     Count = 0
16/07/2025 : 08:25:48.169 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:25:48.171 : Leaving
16/07/2025 : 08:26:23.879 : Entering
16/07/2025 : 08:26:23.880 :     Client           = 
16/07/2025 : 08:26:23.881 :     DateFactureStart = 12/07/2019 00:00:00
16/07/2025 : 08:26:23.881 :     DateFactureEnd   = 10/07/2020 00:00:00
16/07/2025 : 08:26:23.882 :     NO SORTING: Using default sort
16/07/2025 : 08:26:24.156 :     Items retrieved: 14
16/07/2025 : 08:26:26.622 :     Count = 533785
16/07/2025 : 08:26:26.623 : Leaving
16/07/2025 : 08:29:24.236 : Entering
16/07/2025 : 08:29:24.236 :     Client           = 
16/07/2025 : 08:29:24.238 :     DateFactureStart = 12/07/2019 00:00:00
16/07/2025 : 08:29:24.239 :     DateFactureEnd   = 10/07/2020 00:00:00
16/07/2025 : 08:29:24.240 :     NO SORTING: Using default sort
16/07/2025 : 08:29:54.353 :     Items retrieved: 0
16/07/2025 : 08:29:58.994 :     Count = 533785
16/07/2025 : 08:29:58.995 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:29:58.996 : Leaving
16/07/2025 : 08:29:59.009 : Entering
16/07/2025 : 08:29:59.009 :     Client           = 
16/07/2025 : 08:29:59.010 :     DateFactureStart = 12/07/2019 00:00:00
16/07/2025 : 08:29:59.010 :     DateFactureEnd   = 10/07/2020 00:00:00
16/07/2025 : 08:29:59.011 :     NO SORTING: Using default sort
16/07/2025 : 08:29:59.574 :     Items retrieved: 14
16/07/2025 : 08:30:03.485 :     Count = 533785
16/07/2025 : 08:30:03.486 : Leaving
16/07/2025 : 08:30:03.499 : Entering
16/07/2025 : 08:30:03.499 :     Client           = 
16/07/2025 : 08:30:03.500 :     DateFactureStart = 12/07/2019 00:00:00
16/07/2025 : 08:30:03.500 :     DateFactureEnd   = 10/07/2020 00:00:00
16/07/2025 : 08:30:03.502 :     NO SORTING: Using default sort
16/07/2025 : 08:30:33.613 :     Items retrieved: 0
16/07/2025 : 08:30:36.741 :     Count = 533785
16/07/2025 : 08:30:36.741 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:30:36.743 : Leaving
16/07/2025 : 08:30:53.856 : Entering
16/07/2025 : 08:30:53.857 :     Client           = 
16/07/2025 : 08:30:53.860 :     DateFactureStart = 12/07/2019 00:00:00
16/07/2025 : 08:30:53.861 :     DateFactureEnd   = 10/07/2020 00:00:00
16/07/2025 : 08:30:53.862 :     NO SORTING: Using default sort
16/07/2025 : 08:31:23.963 :     Items retrieved: 0
16/07/2025 : 08:31:26.095 :     Count = 533785
16/07/2025 : 08:31:26.096 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:31:26.102 : Leaving
16/07/2025 : 08:31:26.114 : Entering
16/07/2025 : 08:31:26.115 :     Client           = 
16/07/2025 : 08:31:26.116 :     DateFactureStart = 12/07/2019 00:00:00
16/07/2025 : 08:31:26.117 :     DateFactureEnd   = 10/07/2020 00:00:00
16/07/2025 : 08:31:26.119 :     NO SORTING: Using default sort
16/07/2025 : 08:31:56.207 :     Items retrieved: 0
16/07/2025 : 08:31:58.508 :     Count = 533785
16/07/2025 : 08:31:58.509 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 08:31:58.509 : Leaving
16/07/2025 : 09:00:09.053 : Entering
16/07/2025 : 09:00:09.053 :     Client           = 
16/07/2025 : 09:00:09.054 :     DateFactureStart = 
16/07/2025 : 09:00:09.054 :     DateFactureEnd   = 
16/07/2025 : 09:00:09.055 :     NO SORTING: Using default sort
16/07/2025 : 09:00:09.110 :     Items retrieved: 14
16/07/2025 : 09:00:11.368 :     Count = 8832132
16/07/2025 : 09:00:11.368 : Leaving
16/07/2025 : 09:00:11.467 : Entering
16/07/2025 : 09:00:11.467 :     Client           = 
16/07/2025 : 09:00:11.468 :     DateFactureStart = 
16/07/2025 : 09:00:11.468 :     DateFactureEnd   = 
16/07/2025 : 09:00:11.469 :     NO SORTING: Using default sort
16/07/2025 : 09:00:31.867 :     Items retrieved: 14
16/07/2025 : 09:00:34.895 :     Count = 8832132
16/07/2025 : 09:00:34.896 : Leaving
16/07/2025 : 09:00:35.316 : Entering
16/07/2025 : 09:00:35.317 :     Client           = 
16/07/2025 : 09:00:35.318 :     DateFactureStart = 
16/07/2025 : 09:00:35.318 :     DateFactureEnd   = 
16/07/2025 : 09:00:35.318 :     NO SORTING: Using default sort
16/07/2025 : 09:00:35.374 :     Items retrieved: 14
16/07/2025 : 09:00:37.339 :     Count = 8832132
16/07/2025 : 09:13:58.251 :     Entering
16/07/2025 : 09:13:58.257 :         Entering
16/07/2025 : 09:13:58.258 :             Client           = 
16/07/2025 : 09:13:58.258 :             Entering
16/07/2025 : 09:13:58.259 :                 Client           = 
16/07/2025 : 09:13:58.259 :                 DateFactureStart = 
16/07/2025 : 09:13:58.260 :                 DateFactureEnd   = 
16/07/2025 : 09:13:58.260 :                 NO SORTING: Using default sort
16/07/2025 : 09:13:58.260 :             Leaving
16/07/2025 : 09:13:58.261 :             Client           = 
16/07/2025 : 09:13:58.261 :             DateFactureStart = 
16/07/2025 : 09:13:58.262 :             DateFactureEnd   = 
16/07/2025 : 09:13:58.262 :             NO SORTING: Using default sort
16/07/2025 : 09:13:58.263 :             DateFactureStart = 
16/07/2025 : 09:13:58.263 :             DateFactureEnd   = 
16/07/2025 : 09:13:58.264 :             NO SORTING: Using default sort
16/07/2025 : 09:13:58.264 :             Entering
16/07/2025 : 09:13:58.265 :                 Client           = 
16/07/2025 : 09:13:58.265 :                 DateFactureStart = 
16/07/2025 : 09:13:58.266 :                 DateFactureEnd   = 
16/07/2025 : 09:13:58.268 :                 NO SORTING: Using default sort
16/07/2025 : 09:13:58.667 :                 Items retrieved: 14
16/07/2025 : 09:13:58.741 :                 Items retrieved: 14
16/07/2025 : 09:13:58.805 :                 Items retrieved: 14
16/07/2025 : 09:13:58.806 :                 Items retrieved: 14
16/07/2025 : 09:14:07.976 :                 Count = 8832132
16/07/2025 : 09:14:07.976 :             Leaving
16/07/2025 : 09:14:07.990 :             Count = 8832132
16/07/2025 : 09:14:07.990 :         Leaving
16/07/2025 : 09:14:08.035 :         Entering
16/07/2025 : 09:14:08.035 :             Client           = 0026
16/07/2025 : 09:14:08.036 :             DateFactureStart = 
16/07/2025 : 09:14:08.036 :             DateFactureEnd   = 
16/07/2025 : 09:14:08.036 :             NO SORTING: Using default sort
16/07/2025 : 09:14:08.180 :             Count = 8832132
16/07/2025 : 09:14:15.563 :             Count = 8832132
16/07/2025 : 09:14:15.569 :         Leaving
16/07/2025 : 09:14:15.572 :     Leaving
16/07/2025 : 09:14:15.575 :     Items retrieved: 14
16/07/2025 : 09:14:15.668 :     Entering
16/07/2025 : 09:14:15.669 :         Client           = 
16/07/2025 : 09:14:15.669 :         DateFactureStart = 
16/07/2025 : 09:14:15.670 :         DateFactureEnd   = 
16/07/2025 : 09:14:15.670 :         NO SORTING: Using default sort
16/07/2025 : 09:14:15.737 :         Items retrieved: 14
16/07/2025 : 09:14:18.651 :         Count = 55904
16/07/2025 : 09:14:18.651 :     Leaving
16/07/2025 : 09:14:18.653 :     Entering
16/07/2025 : 09:14:18.653 :         Client           = 0026
16/07/2025 : 09:14:18.654 :         DateFactureStart = 
16/07/2025 : 09:14:18.654 :         DateFactureEnd   = 
16/07/2025 : 09:14:18.655 :         NO SORTING: Using default sort
16/07/2025 : 09:14:18.955 :         Count = 8832132
16/07/2025 : 09:14:18.956 :     Leaving
16/07/2025 : 09:14:19.695 :     Items retrieved: 14
16/07/2025 : 09:14:21.401 :     Count = 55904
16/07/2025 : 09:14:21.402 : Leaving
16/07/2025 : 09:14:21.408 : Entering
16/07/2025 : 09:14:21.409 :     Client           = 0026
16/07/2025 : 09:14:21.412 :     DateFactureStart = 
16/07/2025 : 09:14:21.413 :     DateFactureEnd   = 
16/07/2025 : 09:14:21.413 :     NO SORTING: Using default sort
16/07/2025 : 09:14:22.166 :     Items retrieved: 14
16/07/2025 : 09:14:23.770 :     Count = 55904
16/07/2025 : 09:14:23.770 : Leaving
16/07/2025 : 09:14:23.772 : Entering
16/07/2025 : 09:14:23.772 :     Client           = 0026
16/07/2025 : 09:14:23.773 :     DateFactureStart = 
16/07/2025 : 09:14:23.773 :     DateFactureEnd   = 
16/07/2025 : 09:14:23.774 :     NO SORTING: Using default sort
16/07/2025 : 09:14:24.583 :     Items retrieved: 14
16/07/2025 : 09:14:26.407 :     Count = 55904
16/07/2025 : 09:14:26.408 : Leaving
16/07/2025 : 09:14:26.412 : Entering
16/07/2025 : 09:14:26.412 :     Client           = 0026
16/07/2025 : 09:14:26.413 :     DateFactureStart = 
16/07/2025 : 09:14:26.413 :     DateFactureEnd   = 
16/07/2025 : 09:14:26.414 :     NO SORTING: Using default sort
16/07/2025 : 09:14:27.325 :     Items retrieved: 14
16/07/2025 : 09:14:49.719 : Entering
16/07/2025 : 09:14:49.721 :     Client           = 
16/07/2025 : 09:14:49.722 :     DateFactureStart = 
16/07/2025 : 09:14:49.722 :     DateFactureEnd   = 
16/07/2025 : 09:14:49.722 :     NO SORTING: Using default sort
16/07/2025 : 09:14:51.355 :     Items retrieved: 14
16/07/2025 : 09:14:53.200 :     Count = 8832132
16/07/2025 : 09:14:53.200 : Leaving
16/07/2025 : 09:14:53.449 : Entering
16/07/2025 : 09:14:53.450 :     Client           = 
16/07/2025 : 09:14:53.450 :     DateFactureStart = 
16/07/2025 : 09:14:53.450 :     DateFactureEnd   = 
16/07/2025 : 09:14:53.450 :     NO SORTING: Using default sort
16/07/2025 : 09:14:53.556 :     Items retrieved: 14
16/07/2025 : 09:14:59.199 :     Count = 8832132
16/07/2025 : 09:14:59.200 : Leaving
16/07/2025 : 09:25:31.863 : Entering
16/07/2025 : 09:25:31.865 :     Client           = 
16/07/2025 : 09:25:31.865 :     DateFactureStart = 
16/07/2025 : 09:25:31.865 :     DateFactureEnd   = 
16/07/2025 : 09:25:31.865 :     NO SORTING: Using default sort
16/07/2025 : 09:25:32.212 :     Items retrieved: 14
16/07/2025 : 09:25:35.485 :     Count = 8832132
16/07/2025 : 09:25:35.486 : Leaving
16/07/2025 : 09:25:35.622 : Entering
16/07/2025 : 09:25:35.623 :     Client           = 
16/07/2025 : 09:25:35.623 :     DateFactureStart = 
16/07/2025 : 09:25:35.623 :     DateFactureEnd   = 
16/07/2025 : 09:25:35.623 :     NO SORTING: Using default sort
16/07/2025 : 09:25:55.781 :     Items retrieved: 14
16/07/2025 : 09:25:57.910 :     Count = 8832132
16/07/2025 : 09:25:57.911 : Leaving
16/07/2025 : 10:16:39.020 : Entering
16/07/2025 : 10:16:39.025 :     Client           = 
16/07/2025 : 10:16:39.027 :     DateFactureStart = 
16/07/2025 : 10:16:39.027 :     DateFactureEnd   = 
16/07/2025 : 10:16:39.027 :     SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:17:09.809 :     Items retrieved: 0
16/07/2025 : 10:17:12.003 :     Count = 8832132
16/07/2025 : 10:17:12.004 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:17:12.004 : Leaving
16/07/2025 : 10:17:12.012 : Entering
16/07/2025 : 10:17:12.012 :     Client           = 
16/07/2025 : 10:17:12.012 :     DateFactureStart = 
16/07/2025 : 10:17:12.012 :     DateFactureEnd   = 
16/07/2025 : 10:17:12.012 :     SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:17:42.209 :     Items retrieved: 0
16/07/2025 : 10:17:44.110 :     Count = 8832132
16/07/2025 : 10:17:44.110 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:17:44.111 : Leaving
16/07/2025 : 10:17:44.208 : Entering
16/07/2025 : 10:17:44.208 :     Client           = 
16/07/2025 : 10:17:44.209 :     DateFactureStart = 
16/07/2025 : 10:17:44.209 :     DateFactureEnd   = 
16/07/2025 : 10:17:44.209 :     SORTING: Column = datefac2, Direction = ascending
16/07/2025 : 10:18:14.543 :     Items retrieved: 0
16/07/2025 : 10:18:16.654 :     Count = 8832132
16/07/2025 : 10:18:16.655 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:18:16.655 : Leaving
16/07/2025 : 10:18:16.657 : Entering
16/07/2025 : 10:18:16.660 :     Client           = 
16/07/2025 : 10:18:16.661 :     DateFactureStart = 
16/07/2025 : 10:18:16.662 :     DateFactureEnd   = 
16/07/2025 : 10:18:16.663 :     SORTING: Column = datefac2, Direction = ascending
16/07/2025 : 10:18:46.886 :     Items retrieved: 0
16/07/2025 : 10:18:49.007 :     Count = 8832132
16/07/2025 : 10:18:49.008 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:18:49.014 : Leaving
16/07/2025 : 10:18:49.033 : Entering
16/07/2025 : 10:18:49.033 :     Client           = 
16/07/2025 : 10:18:49.034 :     DateFactureStart = 
16/07/2025 : 10:18:49.034 :     DateFactureEnd   = 
16/07/2025 : 10:18:49.035 :     SORTING: Column = etat, Direction = ascending
16/07/2025 : 10:18:57.219 :     Entering
16/07/2025 : 10:18:57.220 :         Client           = 
16/07/2025 : 10:18:57.220 :         DateFactureStart = 
16/07/2025 : 10:18:57.220 :         DateFactureEnd   = 
16/07/2025 : 10:18:57.220 :         NO SORTING: Using default sort
16/07/2025 : 10:18:57.486 :         Items retrieved: 14
16/07/2025 : 10:18:59.727 :         Count = 8832132
16/07/2025 : 10:18:59.728 :     Leaving
16/07/2025 : 10:18:59.840 :     Entering
16/07/2025 : 10:18:59.840 :         Client           = 
16/07/2025 : 10:18:59.841 :         DateFactureStart = 
16/07/2025 : 10:18:59.841 :         DateFactureEnd   = 
16/07/2025 : 10:18:59.841 :         NO SORTING: Using default sort
16/07/2025 : 10:19:17.898 :         Items retrieved: 14
16/07/2025 : 10:19:19.163 :         Items retrieved: 0
16/07/2025 : 10:19:20.514 :         Count = 8832132
16/07/2025 : 10:19:20.515 :     Leaving
16/07/2025 : 10:19:21.532 :     Count = 8832132
16/07/2025 : 10:19:21.533 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:19:21.533 : Leaving
16/07/2025 : 10:19:21.534 : Entering
16/07/2025 : 10:19:21.534 :     Client           = 
16/07/2025 : 10:19:21.534 :     DateFactureStart = 
16/07/2025 : 10:19:21.534 :     DateFactureEnd   = 
16/07/2025 : 10:19:21.535 :     SORTING: Column = etat, Direction = ascending
16/07/2025 : 10:19:24.634 :     Entering
16/07/2025 : 10:19:24.636 :         Client           = 
16/07/2025 : 10:19:24.636 :         DateFactureStart = 
16/07/2025 : 10:19:24.636 :         DateFactureEnd   = 
16/07/2025 : 10:19:24.637 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:19:51.654 :         Items retrieved: 0
16/07/2025 : 10:19:55.666 :         Count = 8832132
16/07/2025 : 10:19:55.667 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:19:55.668 :     Leaving
16/07/2025 : 10:19:55.681 :     Entering
16/07/2025 : 10:19:55.681 :         Client           = 
16/07/2025 : 10:19:55.681 :         DateFactureStart = 
16/07/2025 : 10:19:55.682 :         DateFactureEnd   = 
16/07/2025 : 10:19:55.682 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:19:55.697 :         Items retrieved: 0
16/07/2025 : 10:20:02.538 :         Count = 8832132
16/07/2025 : 10:20:02.539 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:20:02.544 :     Leaving
16/07/2025 : 10:20:02.547 :     Entering
16/07/2025 : 10:20:02.547 :         Client           = 
16/07/2025 : 10:20:02.548 :         DateFactureStart = 
16/07/2025 : 10:20:02.549 :         DateFactureEnd   = 
16/07/2025 : 10:20:02.550 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:20:25.982 :         Items retrieved: 0
16/07/2025 : 10:20:29.412 :         Count = 8832132
16/07/2025 : 10:20:29.412 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:20:29.412 :     Leaving
16/07/2025 : 10:20:29.413 :     Entering
16/07/2025 : 10:20:29.413 :         Client           = 
16/07/2025 : 10:20:29.414 :         DateFactureStart = 
16/07/2025 : 10:20:29.415 :         DateFactureEnd   = 
16/07/2025 : 10:20:29.415 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:20:33.278 :         Items retrieved: 0
16/07/2025 : 10:20:37.926 :         Count = 8832132
16/07/2025 : 10:20:37.926 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:20:37.927 :     Leaving
16/07/2025 : 10:20:37.935 :     Entering
16/07/2025 : 10:20:37.935 :         Client           = 
16/07/2025 : 10:20:37.936 :         DateFactureStart = 
16/07/2025 : 10:20:37.936 :         DateFactureEnd   = 
16/07/2025 : 10:20:37.936 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:20:59.659 :         Items retrieved: 0
16/07/2025 : 10:21:04.175 :         Count = 8832132
16/07/2025 : 10:21:04.176 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:21:04.178 :     Leaving
16/07/2025 : 10:21:04.184 :     Entering
16/07/2025 : 10:21:04.184 :         Client           = 
16/07/2025 : 10:21:04.184 :         DateFactureStart = 
16/07/2025 : 10:21:04.184 :         DateFactureEnd   = 
16/07/2025 : 10:21:04.185 :         SORTING: Column = datecrea, Direction = ascending
16/07/2025 : 10:21:08.135 :         Items retrieved: 0
16/07/2025 : 10:21:11.878 :         Count = 8832132
16/07/2025 : 10:21:11.879 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:21:11.880 :     Leaving
16/07/2025 : 10:21:11.885 :     Entering
16/07/2025 : 10:21:11.885 :         Client           = 
16/07/2025 : 10:21:11.886 :         DateFactureStart = 
16/07/2025 : 10:21:11.887 :         DateFactureEnd   = 
16/07/2025 : 10:21:11.889 :         SORTING: Column = montant, Di16/07/2025 : 10:21:13.16/07/2025 : 10:21:34.315 :         Items retrieved: 0
16/07/2016/07/2025 : 10:28:25.500 : Entering
16/07/2025 : 10:28:25.500 :     Client           = 
16/07/2025 : 10:28:25.501 :     DateFactureStart = 
16/07/2025 : 10:28:25.502 :     DateFactureEnd   = 
16/07/2025 : 10:28:26.791 :     A TOP can not be used in the same query or sub-query as a OFFSET.
16/07/2025 : 10:28:26.837 :     Entering
16/07/2025 : 10:28:26.838 :         Client           = 
16/07/2025 : 10:28:26.839 :         DateFactureStart = 
16/07/2025 : 10:28:26.840 :         DateFactureEnd   = 
16/07/2025 : 10:28:27.025 :         A TOP can not be used in the same query or sub-query as a OFFSET.
16/07/2025 : 10:29:18.372 :         Entering
16/07/2025 : 10:29:18.372 :             Client           = 
16/07/2025 : 10:29:18.373 :             DateFactureStart = 
16/07/2025 : 10:29:18.373 :             DateFactureEnd   = 
16/07/2025 : 10:29:20.494 :             Count = 8832132
16/07/2025 : 10:29:20.494 :         Leaving
16/07/2025 : 10:29:20.582 :         Entering
16/07/2025 : 10:29:20.583 :             Client           = 
16/07/2025 : 10:29:20.584 :             DateFactureStart = 
16/07/2025 : 10:29:20.584 :             DateFactureEnd   = 
16/07/2025 : 10:29:22.450 :             Count = 8832132
16/07/2025 : 10:29:22.450 :         Leaving
16/07/2025 : 10:32:46.306 :         Entering
16/07/2025 : 10:32:46.307 :             Client           = 
16/07/2025 : 10:32:46.314 :             DateFactureStart = 
16/07/2025 : 10:32:46.315 :             DateFactureEnd   = 
16/07/2025 : 10:32:48.541 :             Count = 8832132
16/07/2025 : 10:32:48.541 :         Leaving
16/07/2025 : 10:32:48.612 :         Entering
16/07/2025 : 10:32:48.612 :             Client           = 
16/07/2025 : 10:32:48.614 :             DateFactureStart = 
16/07/2025 : 10:32:48.615 :             DateFactureEnd   = 
16/07/2025 : 10:32:50.529 :             Count = 8832132
16/07/2025 : 10:32:50.530 :         Leaving
16/07/2025 : 10:32:50.541 :         Entering
16/07/2025 : 10:32:50.541 :             Client           = 
16/07/2025 : 10:32:50.542 :             DateFactureStart = 
16/07/2025 : 10:32:50.542 :             DateFactureEnd   = 
16/07/2025 : 10:32:52.590 :             Count = 8832132
16/07/2025 : 10:32:52.590 :         Leaving
16/07/2025 : 10:32:54.268 :         Entering
16/07/2025 : 10:32:54.269 :             Client           = 
16/07/2025 : 10:32:54.276 :             DateFactureStart = 
16/07/2025 : 10:32:54.278 :             DateFactureEnd   = 
16/07/2025 : 10:32:56.369 :             Count = 8832132
16/07/2025 : 10:32:56.370 :         Leaving
16/07/2025 : 10:32:56.401 :         Entering
16/07/2025 : 10:32:56.402 :             Client           = 
16/07/2025 : 10:32:56.406 :             DateFactureStart = 
16/07/2025 : 10:32:56.407 :             DateFactureEnd   = 
16/07/2025 : 10:32:58.205 :             Count = 8832132
16/07/2025 : 10:32:58.206 :         Leaving
16/07/2025 : 10:33:03.053 :         Entering
16/07/2025 : 10:33:03.053 :             Client           = 
16/07/2025 : 10:33:03.056 :             DateFactureStart = 
16/07/2025 : 10:33:03.058 :             DateFactureEnd   = 
16/07/2025 : 10:33:05.111 :             Count = 8832132
16/07/2025 : 10:33:05.112 :         Leaving
16/07/2025 : 10:33:08.071 :         Entering
16/07/2025 : 10:33:08.072 :             Client           = 
16/07/2025 : 10:33:08.079 :             DateFactureStart = 
16/07/2025 : 10:33:08.080 :             DateFactureEnd   = 
16/07/2025 : 10:33:09.941 :             Count = 8832132
16/07/2025 : 10:33:09.942 :         Leaving
16/07/2025 : 10:34:26.510 :         Entering
16/07/2025 : 10:34:26.511 :             Client           = 
16/07/2025 : 10:34:26.517 :             DateFactureStart = 
16/07/2025 : 10:34:26.519 :             DateFactureEnd   = 
NING: No items returned from Helper_GetData
16/07/2025 : 10:23:28.605 :     Leaving
16/07/2025 : 10:23:28.608 :     Entering
16/07/2025 : 10:23:28.610 :         Client           = 
16/07/2025 : 10:23:28.612 :         DateFactureStart = 
16/07/2025 : 10:23:28.614 :         DateFactureEnd   = 
16/07/2025 : 10:23:28.615 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:23:50.536 :         Items retrieved: 0
16/07/2025 : 10:23:55.355 :         Count = 8832132
16/07/2025 : 10:23:55.355 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:23:55.357 :     Leaving
16/07/2025 : 10:23:55.358 :     Entering
16/07/2025 : 10:23:55.358 :         Client           = 
16/07/2025 : 10:23:55.358 :         DateFactureStart = 
16/07/2025 : 10:23:55.358 :         DateFactureEnd   = 
16/07/2025 : 10:23:55.359 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:23:58.994 :         Items retrieved: 0
16/07/2025 : 10:24:04.341 :         Count = 8832132
16/07/2025 : 10:24:04.342 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:24:04.345 :     Leaving
16/07/2025 : 10:24:04.347 :     Entering
16/07/2025 : 10:24:04.348 :         Client           = 
16/07/2025 : 10:24:04.350 :         DateFactureStart = 
16/07/2025 : 10:24:04.357 :         DateFactureEnd   = 
16/07/2025 : 10:24:04.360 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:24:25.606 :         Items retrieved: 0
16/07/2025 : 10:24:29.451 :         Count = 8832132
16/07/2025 : 10:24:29.452 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:24:29.454 :     Leaving
16/07/2025 : 10:24:29.470 :     Entering
16/07/2025 : 10:24:29.471 :         Client           = 
16/07/2025 : 10:24:29.472 :         DateFactureStart = 
16/07/2025 : 10:24:29.474 :         DateFactureEnd   = 
16/07/2025 : 10:24:29.475 :         NO SORTING: Using default sort
16/07/2025 : 10:24:34.881 :         Items retrieved: 0
16/07/2025 : 10:24:37.558 :         Count = 8832132
16/07/2025 : 10:24:37.558 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:24:37.559 :     Leaving
16/07/2025 : 10:24:37.833 :     Entering
16/07/2025 : 10:24:37.833 :         Client           = 
16/07/2025 : 10:24:37.834 :         DateFactureStart = 
16/07/2025 : 10:24:37.835 :         DateFactureEnd   = 
16/07/2025 : 10:24:37.835 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:24:59.611 :         Items retrieved: 0
16/07/2025 : 10:25:05.302 :         Count = 8832132
16/07/2025 : 10:25:05.302 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:25:05.303 :     Leaving
16/07/2025 : 10:25:05.304 :     Entering
16/07/2025 : 10:25:05.304 :         Client           = 
16/07/2025 : 10:25:05.304 :         DateFactureStart = 
16/07/2025 : 10:25:05.305 :         DateFactureEnd   = 
16/07/2025 : 10:25:05.305 :         NO SORTING: Using default sort
16/07/2025 : 10:25:07.996 :         Items retrieved: 0
16/07/2025 : 10:25:10.662 :         Count = 8832132
16/07/2025 : 10:25:10.662 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:25:10.663 :     Leaving
16/07/2025 : 10:25:10.664 :     Entering
16/07/2025 : 10:25:10.665 :         Client           = 
16/07/2025 : 10:25:10.665 :         DateFactureStart = 
16/07/2025 : 10:25:10.666 :         DateFactureEnd   = 
16/07/2025 : 10:25:10.666 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:25:35.391 :         Items retrieved: 0
16/07/2025 : 10:25:39.005 :         Count = 8832132
16/07/2025 : 10:25:39.006 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:25:39.008 :     Leaving
16/07/2025 : 10:25:39.014 :     Entering
16/07/2025 : 10:25:39.014 :         Client           = 
16/07/2025 : 10:25:39.015 :         DateFactureStart = 
16/07/2025 : 10:25:39.017 :         DateFactureEnd   = 
16/07/2025 : 10:25:39.019 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:25:40.952 :         Items retrieved: 0
16/07/2025 : 10:25:46.469 :         Count = 8832132
16/07/2025 : 10:25:46.470 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:25:46.472 :     Leaving
16/07/2025 : 10:25:46.477 :     Entering
16/07/2025 : 10:25:46.478 :         Client           = 
16/07/2025 : 10:25:46.479 :         DateFactureStart = 
16/07/2025 : 10:25:46.480 :         DateFactureEnd   = 
16/07/2025 : 10:25:46.481 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:26:09.196 :         Items retrieved: 0
16/07/2025 : 10:26:12.272 :         Count = 8832132
16/07/2025 : 10:26:12.273 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:26:12.278 :     Leaving
16/07/2025 : 10:26:12.280 :     Entering
16/07/2025 : 10:26:12.282 :         Client           = 
16/07/2025 : 10:26:12.284 :         DateFactureStart = 
16/07/2025 : 10:26:12.285 :         DateFactureEnd   = 
16/07/2025 : 10:26:12.286 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:26:16.756 :         Items retrieved: 0
16/07/2025 : 10:26:20.793 :         Count = 8832132
16/07/2025 : 10:26:20.794 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:26:20.796 :     Leaving
16/07/2025 : 10:26:20.798 :     Entering
16/07/2025 : 10:26:20.798 :         Client           = 
16/07/2025 : 10:26:20.799 :         DateFactureStart = 
16/07/2025 : 10:26:20.800 :         DateFactureEnd   = 
16/07/2025 : 10:26:20.802 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:26:42.661 :         Items retrieved: 0
16/07/2025 : 10:26:47.546 :         Count = 8832132
16/07/2025 : 10:26:47.546 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:26:47.550 :     Leaving
16/07/2025 : 10:26:47.551 :     Entering
16/07/2025 : 10:26:47.552 :         Client           = 
16/07/2025 : 10:26:47.552 :         DateFactureStart = 
16/07/2025 : 10:26:47.553 :         DateFactureEnd   = 
16/07/2025 : 10:26:47.553 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:26:51.282 :         Items retrieved: 0
16/07/2025 : 10:26:56.369 :         Count = 8832132
16/07/2025 : 10:26:56.369 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:26:56.371 :     Leaving
16/07/2025 : 10:26:56.372 :     Entering
16/07/2025 : 10:26:56.372 :         Client           = 
16/07/2025 : 10:26:56.373 :         DateFactureStart = 
16/07/2025 : 10:26:56.376 :         DateFactureEnd   = 
16/07/2025 : 10:26:56.377 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:27:17.981 :         Items retrieved: 0
16/07/2025 : 10:27:21.951 :         Count = 8832132
16/07/2025 : 10:27:21.951 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:27:21.953 :     Leaving
16/07/2025 : 10:27:21.954 :     Entering
16/07/2025 : 10:27:21.954 :         Client           = 
16/07/2025 : 10:27:21.955 :         DateFactureStart = 
16/07/2025 : 10:27:21.956 :         DateFactureEnd   = 
16/07/2025 : 10:27:21.956 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:27:26.646 :         Items retrieved: 0
16/07/2025 : 10:27:31.637 :         Count = 8832132
16/07/2025 : 10:27:31.637 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:27:31.639 :     Leaving
16/07/2025 : 10:27:52.160 :     Items retrieved: 0
16/07/2025 : 10:27:55.137 :     Count = 8832132
16/07/2025 : 10:27:55.137 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:27:55.138 : Leaving
16/07/2025 : 10:27:55.139 : Entering
16/07/2025 : 10:27:55.139 :     Client           = 
16/07/2025 : 10:27:55.140 :     DateFactureStart = 
16/07/2025 : 10:27:55.140 :     DateFactureEnd   = 
16/07/2025 : 10:27:55.141 :     NO SORTING: Using default sort
16/07/2025 : 10:27:55.228 :     Items retrieved: 0
16/07/2025 : 10:27:56.934 :     Entering
16/07/2025 : 10:27:56.935 :         Client           = 
16/07/2025 : 10:27:56.937 :         DateFactureStart = 
16/07/2025 : 10:27:56.938 :         DateFactureEnd   = 
16/07/2025 : 10:27:56.939 :         NO SORTING: Using default sort
16/07/2025 : 10:27:57.001 :         Items retrieved: 0
16/07/2025 : 10:27:57.710 :         Count = 8832132
16/07/2025 : 10:27:57.711 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:27:57.712 :     Leaving
16/07/2025 : 10:27:57.713 :     Entering
16/07/2025 : 10:27:57.713 :         Client           = 
16/07/2025 : 10:27:57.714 :         DateFactureStart = 
16/07/2025 : 10:27:57.714 :         DateFactureEnd   = 
16/07/2025 : 10:27:57.715 :         NO SORTING: Using default sort
16/07/2025 : 10:27:57.903 :         Items retrieved: 0
16/07/2025 : 10:27:59.975 :         Count = 8832132
16/07/2025 : 10:27:59.976 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:27:59.979 :     Leaving
16/07/2025 : 10:27:59.991 :     Entering
16/07/2025 : 10:27:59.991 :         Client           = 
16/07/2025 : 10:27:59.993 :         DateFactureStart = 
16/07/2025 : 10:27:59.996 :         DateFactureEnd   = 
16/07/2025 : 10:28:00.000 :         NO SORTING: Using default sort
16/07/2025 : 10:28:00.223 :         Items retrieved: 0
16/07/2025 : 10:28:01.378 :         Count = 8832132
16/07/2025 : 10:28:01.378 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:01.379 :     Leaving
16/07/2025 : 10:28:01.380 :     Entering
16/07/2025 : 10:28:01.380 :         Client           = 
16/07/2025 : 10:28:01.381 :         DateFactureStart = 
16/07/2025 : 10:28:01.382 :         DateFactureEnd   = 
16/07/2025 : 10:28:01.382 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:28:01.462 :         Items retrieved: 0
16/07/2025 : 10:28:03.345 :         Count = 8832132
16/07/2025 : 10:28:03.345 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:03.346 :     Leaving
16/07/2025 : 10:28:03.394 :     Entering
16/07/2025 : 10:28:03.394 :         Client           = 
16/07/2025 : 10:28:03.396 :         DateFactureStart = 
16/07/2025 : 10:28:03.396 :         DateFactureEnd   = 
16/07/2025 : 10:28:03.396 :         SORTING: Column = montant, Direction = descending
16/07/2025 : 10:28:03.532 :         Items retrieved: 0
16/07/2025 : 10:28:05.664 :         Count = 8832132
16/07/2025 : 10:28:05.665 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:05.668 :     Leaving
16/07/2025 : 10:28:05.676 :     Entering
16/07/2025 : 10:28:05.677 :         Client           = 
16/07/2025 : 10:28:05.679 :         DateFactureStart = 
16/07/2025 : 10:28:05.683 :         DateFactureEnd   = 
16/07/2025 : 10:28:05.689 :         SORTING: Column = montant, Direction = ascending
16/07/2025 : 10:28:05.964 :         Items retrieved: 0
16/07/2025 : 10:28:06.862 :         Count = 8832132
16/07/2025 : 10:28:06.863 :         WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:06.868 :     Leaving
16/07/2025 : 10:28:07.957 :     Count = 8832132
16/07/2025 : 10:28:07.958 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:07.963 : Leaving
16/07/2025 : 10:28:07.966 : Entering
16/07/2025 : 10:28:07.966 :     Client           = 
16/07/2025 : 10:28:07.968 :     DateFactureStart = 
16/07/2025 : 10:28:07.970 :     DateFactureEnd   = 
16/07/2025 : 10:28:07.971 :     SORTING: Column = montant, Direction = descending
16/07/2025 : 10:28:08.080 :     Items retrieved: 0
16/07/2025 : 10:28:10.079 :     Count = 8832132
16/07/2025 : 10:28:10.080 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:10.086 : Leaving
16/07/2025 : 10:28:10.093 : Entering
16/07/2025 : 10:28:10.094 :     Client           = 
16/07/2025 : 10:28:10.095 :     DateFactureStart = 
16/07/2025 : 10:28:10.096 :     DateFactureEnd   = 
16/07/2025 : 10:28:10.097 :     SORTING: Column = montant, Direction = descending
16/07/2025 : 10:28:10.195 :     Items retrieved: 0
16/07/2025 : 10:28:11.981 :     Count = 8832132
16/07/2025 : 10:28:11.982 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:11.985 : Leaving
16/07/2025 : 10:28:12.008 : Entering
16/07/2025 : 10:28:12.009 :     Client           = 
16/07/2025 : 10:28:12.010 :     DateFactureStart = 
16/07/2025 : 10:28:12.012 :     DateFactureEnd   = 
16/07/2025 : 10:28:12.014 :     SORTING: Column = etat, Direction = ascending
16/07/2025 : 10:28:12.097 :     Items retrieved: 0
16/07/2025 : 10:28:14.355 :     Count = 8832132
16/07/2025 : 10:28:14.355 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:14.358 : Leaving
16/07/2025 : 10:28:14.359 : Entering
16/07/2025 : 10:28:14.359 :     Client           = 
16/07/2025 : 10:28:14.360 :     DateFactureStart = 
16/07/2025 : 10:28:14.361 :     DateFactureEnd   = 
16/07/2025 : 10:28:14.362 :     SORTING: Column = etat, Direction = ascending
16/07/2025 : 10:28:14.462 :     Items retrieved: 0
16/07/2025 : 10:28:16.844 :     Count = 8832132
16/07/2025 : 10:28:16.845 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:16.849 : Leaving
16/07/2025 : 10:28:16.852 : Entering
16/07/2025 : 10:28:16.857 :     Client           = 
16/07/2025 : 10:28:16.862 :     DateFactureStart = 
16/07/2025 : 10:28:16.866 :     DateFactureEnd   = 
16/07/2025 : 10:28:16.872 :     SORTING: Column = etat, Direction = descending
16/07/2025 : 10:28:16.972 :     Items retrieved: 0
16/07/2025 : 10:28:18.773 :     Count = 8832132
16/07/2025 : 10:28:18.774 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:18.776 : Leaving
16/07/2025 : 10:28:18.777 : Entering
16/07/2025 : 10:28:18.778 :     Client           = 
16/07/2025 : 10:28:18.778 :     DateFactureStart = 
16/07/2025 : 10:28:18.779 :     DateFactureEnd   = 
16/07/2025 : 10:28:18.780 :     SORTING: Column = etat, Direction = descending
16/07/2025 : 10:28:18.855 :     Items retrieved: 0
16/07/2025 : 10:28:20.818 :     Count = 8832132
16/07/2025 : 10:28:20.819 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:20.820 : Leaving
16/07/2025 : 10:28:20.821 : Entering
16/07/2025 : 10:28:20.822 :     Client           = 
16/07/2025 : 10:28:20.822 :     DateFactureStart = 
16/07/2025 : 10:28:20.823 :     DateFactureEnd   = 
16/07/2025 : 10:28:20.824 :     SORTING: Column = datefac2, Direction = ascending
16/07/2025 : 10:28:20.928 :     Items retrieved: 0
16/07/2025 : 10:28:23.214 :     Count = 8832132
16/07/2025 : 10:28:23.215 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:23.217 : Leaving
16/07/2025 : 10:28:23.218 : Entering
16/07/2025 : 10:28:23.219 :     Client           = 
16/07/2025 : 10:28:23.220 :     DateFactureStart = 
16/07/2025 : 10:28:23.221 :     DateFactureEnd   = 
16/07/2025 : 10:28:23.222 :     SORTING: Column = datefac2, Direction = ascending
16/07/2025 : 10:28:23.326 :     Items retrieved: 0
16/07/2025 : 10:28:25.195 :     Count = 8832132
16/07/2025 : 10:28:25.196 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:25.197 : Leaving
16/07/2025 : 10:28:25.201 : Entering
16/07/2025 : 10:28:25.201 :     Client           = 
16/07/2025 : 10:28:25.202 :     DateFactureStart = 
16/07/2025 : 10:28:25.202 :     DateFactureEnd   = 
16/07/2025 : 10:28:25.203 :     SORTING: Column = datefac2, Direction = descending
16/07/2025 : 10:28:25.273 :     Items retrieved: 0
16/07/2025 : 10:28:27.089 :     Count = 8832132
16/07/2025 : 10:28:27.089 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:27.090 : Leaving
16/07/2025 : 10:28:27.091 : Entering
16/07/2025 : 10:28:27.092 :     Client           = 
16/07/2025 : 10:28:27.092 :     DateFactureStart = 
16/07/2025 : 10:28:27.093 :     DateFactureEnd   = 
16/07/2025 : 10:28:27.094 :     SORTING: Column = datefac2, Direction = descending
16/07/2025 : 10:28:27.166 :     Items retrieved: 0
16/07/2025 : 10:28:29.123 :     Count = 8832132
16/07/2025 : 10:28:29.123 :     WARNING: No items returned from Helper_GetData
16/07/2025 : 10:28:29.124 : Leaving
16/07/2025 : 10:38:59.505 : Entering
16/07/2025 : 10:38:59.507 :     Client           = 
16/07/2025 : 10:38:59.508 :     DateFactureStart = 
16/07/2025 : 10:38:59.508 :     DateFactureEnd   = 
16/07/2025 : 10:39:04.444 :     Count = 8832132
16/07/2025 : 10:39:04.447 : Leaving
16/07/2025 : 10:39:04.787 : Entering
16/07/2025 : 10:39:04.787 :     Client           = 
16/07/2025 : 10:39:04.788 :     DateFactureStart = 
16/07/2025 : 10:39:04.788 :     DateFactureEnd   = 
16/07/2025 : 10:39:08.117 :     Count = 8832132
16/07/2025 : 10:39:08.118 : Leaving
16/07/2025 : 10:45:24.333 : Entering
16/07/2025 : 10:45:24.339 :     Client           = 
16/07/2025 : 10:45:24.340 :     DateFactureStart = 
16/07/2025 : 10:45:24.340 :     DateFactureEnd   = 
16/07/2025 : 10:45:24.340 :     NO SORTING: Using default sort
16/07/2025 : 10:45:27.180 :     Items retrieved: 14
16/07/2025 : 10:45:30.226 :     Count = 8832132
16/07/2025 : 10:45:30.228 : Leaving
16/07/2025 : 10:45:30.672 : Entering
16/07/2025 : 10:45:30.672 :     Client           = 
16/07/2025 : 10:45:30.673 :     DateFactureStart = 
16/07/2025 : 10:45:30.674 :     DateFactureEnd   = 
16/07/2025 : 10:45:30.674 :     NO SORTING: Using default sort
16/07/2025 : 10:45:30.824 :     Items retrieved: 14
16/07/2025 : 10:45:33.284 :     Count = 8832132
16/07/2025 : 10:45:33.285 : Leaving
16/07/2025 : 11:47:48.001 : Entering
16/07/2025 : 11:47:48.003 :     Client           = 
16/07/2025 : 11:47:48.003 :     DateFactureStart = 
16/07/2025 : 11:47:48.003 :     DateFactureEnd   = 
16/07/2025 : 11:47:52.349 :     Count = 8832132
16/07/2025 : 11:47:52.350 : Leaving
16/07/2025 : 11:47:52.656 : Entering
16/07/2025 : 11:47:52.656 :     Client           = 
16/07/2025 : 11:47:52.657 :     DateFactureStart = 
16/07/2025 : 11:47:52.657 :     DateFactureEnd   = 
16/07/2025 : 11:47:54.704 :     Count = 8832132
16/07/2025 : 11:47:54.706 : Leaving
16/07/2025 : 11:48:02.115 : Entering
16/07/2025 : 11:48:02.115 :     Client           = 
16/07/2025 : 11:48:02.115 :     DateFactureStart = 
16/07/2025 : 11:48:02.116 :     DateFactureEnd   = 
16/07/2025 : 11:48:04.943 :     Count = 8832132
16/07/2025 : 11:48:04.943 : Leaving
16/07/2025 : 11:48:04.957 : Entering
16/07/2025 : 11:48:04.957 :     Client           = 
16/07/2025 : 11:48:04.957 :     DateFactureStart = 
16/07/2025 : 11:48:04.958 :     DateFactureEnd   = 
16/07/2025 : 11:48:07.719 :     Count = 8832132
16/07/2025 : 11:48:07.720 : Leaving
