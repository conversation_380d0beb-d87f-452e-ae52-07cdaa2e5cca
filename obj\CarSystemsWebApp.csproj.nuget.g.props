﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.core\28.1.33\buildTransitive\Syncfusion.Blazor.Core.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.core\28.1.33\buildTransitive\Syncfusion.Blazor.Core.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.spinner\28.1.33\buildTransitive\Syncfusion.Blazor.Spinner.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.spinner\28.1.33\buildTransitive\Syncfusion.Blazor.Spinner.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.buttons\28.1.33\buildTransitive\Syncfusion.Blazor.Buttons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.buttons\28.1.33\buildTransitive\Syncfusion.Blazor.Buttons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.popups\28.1.33\buildTransitive\Syncfusion.Blazor.Popups.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.popups\28.1.33\buildTransitive\Syncfusion.Blazor.Popups.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\28.1.33\buildTransitive\Syncfusion.Blazor.SplitButtons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\28.1.33\buildTransitive\Syncfusion.Blazor.SplitButtons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.notifications\28.1.33\buildTransitive\Syncfusion.Blazor.Notifications.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.notifications\28.1.33\buildTransitive\Syncfusion.Blazor.Notifications.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.data\28.1.33\buildTransitive\Syncfusion.Blazor.Data.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.data\28.1.33\buildTransitive\Syncfusion.Blazor.Data.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.lists\28.1.33\buildTransitive\Syncfusion.Blazor.Lists.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.lists\28.1.33\buildTransitive\Syncfusion.Blazor.Lists.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.inputs\28.1.33\buildTransitive\Syncfusion.Blazor.Inputs.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.inputs\28.1.33\buildTransitive\Syncfusion.Blazor.Inputs.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.dropdowns\28.1.33\buildTransitive\Syncfusion.Blazor.DropDowns.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.dropdowns\28.1.33\buildTransitive\Syncfusion.Blazor.DropDowns.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.navigations\28.1.33\buildTransitive\Syncfusion.Blazor.Navigations.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.navigations\28.1.33\buildTransitive\Syncfusion.Blazor.Navigations.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.calendars\28.1.33\buildTransitive\Syncfusion.Blazor.Calendars.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.calendars\28.1.33\buildTransitive\Syncfusion.Blazor.Calendars.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.grid\28.1.33\buildTransitive\Syncfusion.Blazor.Grid.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.grid\28.1.33\buildTransitive\Syncfusion.Blazor.Grid.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.treegrid\28.1.33\buildTransitive\Syncfusion.Blazor.TreeGrid.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.treegrid\28.1.33\buildTransitive\Syncfusion.Blazor.TreeGrid.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.themes\28.1.33\buildTransitive\Syncfusion.Blazor.Themes.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.themes\28.1.33\buildTransitive\Syncfusion.Blazor.Themes.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.schedule\28.1.33\buildTransitive\Syncfusion.Blazor.Schedule.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.schedule\28.1.33\buildTransitive\Syncfusion.Blazor.Schedule.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.richtexteditor\28.1.33\buildTransitive\Syncfusion.Blazor.RichTextEditor.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.richtexteditor\28.1.33\buildTransitive\Syncfusion.Blazor.RichTextEditor.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.progressbar\28.1.33\buildTransitive\Syncfusion.Blazor.ProgressBar.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.progressbar\28.1.33\buildTransitive\Syncfusion.Blazor.ProgressBar.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.layouts\28.1.33\buildTransitive\Syncfusion.Blazor.Layouts.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.layouts\28.1.33\buildTransitive\Syncfusion.Blazor.Layouts.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.diagram\28.1.33\buildTransitive\Syncfusion.Blazor.Diagram.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.diagram\28.1.33\buildTransitive\Syncfusion.Blazor.Diagram.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.charts\28.1.33\buildTransitive\Syncfusion.Blazor.Charts.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.charts\28.1.33\buildTransitive\Syncfusion.Blazor.Charts.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.3\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.3\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore.design\9.0.3\build\net8.0\Microsoft.EntityFrameworkCore.Design.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore.design\9.0.3\build\net8.0\Microsoft.EntityFrameworkCore.Design.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4</PkgMicrosoft_CodeAnalysis_Analyzers>
    <PkgMicrosoft_EntityFrameworkCore_Tools Condition=" '$(PkgMicrosoft_EntityFrameworkCore_Tools)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.tools\9.0.3</PkgMicrosoft_EntityFrameworkCore_Tools>
  </PropertyGroup>
</Project>