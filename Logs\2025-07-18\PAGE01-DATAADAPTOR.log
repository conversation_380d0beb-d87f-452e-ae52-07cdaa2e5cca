18/07/2025 : 08:35:28.958 : Entering
18/07/2025 : 08:35:28.965 :     Client           = 
18/07/2025 : 08:35:28.967 :     DateFactureStart = 
18/07/2025 : 08:35:28.968 :     DateFactureEnd   = 
18/07/2025 : 08:35:41.645 :     Count = 8832132
18/07/2025 : 08:35:41.646 : Leaving
18/07/2025 : 08:36:00.788 : Entering
18/07/2025 : 08:36:00.789 :     Client           = 
18/07/2025 : 08:36:00.789 :     DateFactureStart = 
18/07/2025 : 08:36:00.789 :     DateFactureEnd   = 
18/07/2025 : 08:36:02.888 :     Count = 8832132
18/07/2025 : 08:36:02.889 : Leaving
18/07/2025 : 10:33:20.250 : Entering
18/07/2025 : 10:33:20.250 :     Client           = 
18/07/2025 : 10:33:20.251 :     DateFactureStart = 
18/07/2025 : 10:33:20.251 :     DateFactureEnd   = 
18/07/2025 : 10:33:20.251 :     NO SORTING: Using default sort
18/07/2025 : 10:33:20.359 :     Items retrieved: 0
18/07/2025 : 10:33:22.983 :     Count = 8832132
18/07/2025 : 10:33:22.983 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 10:33:22.985 : Leaving
18/07/2025 : 10:33:22.992 : Entering
18/07/2025 : 10:33:22.993 :     Client           = 
18/07/2025 : 10:33:22.993 :     DateFactureStart = 
18/07/2025 : 10:33:22.994 :     DateFactureEnd   = 
18/07/2025 : 10:33:22.994 :     NO SORTING: Using default sort
18/07/2025 : 10:33:23.062 :     Items retrieved: 0
18/07/2025 : 10:33:25.232 :     Count = 8832132
18/07/2025 : 10:33:25.233 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 10:33:25.234 : Leaving
18/07/2025 : 10:33:25.261 : Entering
18/07/2025 : 10:33:25.261 :     Client           = 
18/07/2025 : 10:33:25.262 :     DateFactureStart = 
18/07/2025 : 10:33:25.263 :     DateFactureEnd   = 
18/07/2025 : 10:33:25.264 :     SORTING: Column = codeutilismodi, Direction = descending
18/07/2025 : 10:33:28.070 :     Items retrieved: 0
18/07/2025 : 10:33:29.923 :     Count = 8832132
18/07/2025 : 10:33:29.924 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 10:33:29.928 : Leaving
18/07/2025 : 10:38:08.912 : Entering
18/07/2025 : 10:38:08.915 :     Client           = 
18/07/2025 : 10:38:08.916 :     DateFactureStart = 
18/07/2025 : 10:38:08.916 :     DateFactureEnd   = 
18/07/2025 : 10:38:14.608 :     Count = 8832132
18/07/2025 : 10:38:14.609 : Leaving
18/07/2025 : 10:38:15.587 : Entering
18/07/2025 : 10:38:15.587 :     Client           = 
18/07/2025 : 10:38:15.588 :     DateFactureStart = 
18/07/2025 : 10:38:15.588 :     DateFactureEnd   = 
18/07/2025 : 10:38:17.425 :     Count = 8832132
18/07/2025 : 10:38:17.426 : Leaving
18/07/2025 : 14:00:10.444 : Entering
18/07/2025 : 14:00:10.451 :     Client           = 
18/07/2025 : 14:00:10.452 :     DateFactureStart = 
18/07/2025 : 14:00:10.453 :     DateFactureEnd   = 
18/07/2025 : 14:00:10.453 :     NO SORTING: Using default sort
18/07/2025 : 14:00:14.510 :     Items retrieved: 14
18/07/2025 : 14:00:19.744 :     Count = 8832132
18/07/2025 : 14:00:19.746 : Leaving
18/07/2025 : 14:00:20.314 : Entering
18/07/2025 : 14:00:20.315 :     Client           = 
18/07/2025 : 14:00:20.315 :     DateFactureStart = 
18/07/2025 : 14:00:20.316 :     DateFactureEnd   = 
18/07/2025 : 14:00:20.316 :     NO SORTING: Using default sort
18/07/2025 : 14:00:20.652 :     Items retrieved: 14
18/07/2025 : 14:00:24.646 :     Count = 8832132
18/07/2025 : 14:00:24.647 : Leaving
18/07/2025 : 14:00:29.222 : Entering
18/07/2025 : 14:00:29.223 :     Client           = 
18/07/2025 : 14:00:29.223 :     DateFactureStart = 
18/07/2025 : 14:00:29.224 :     DateFactureEnd   = 
18/07/2025 : 14:00:29.225 :     SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:01:00.128 :     Items retrieved: 0
18/07/2025 : 14:01:02.903 :     Count = 8832132
18/07/2025 : 14:01:02.904 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 14:01:02.905 : Leaving
18/07/2025 : 14:01:02.908 : Entering
18/07/2025 : 14:01:02.909 :     Client           = 
18/07/2025 : 14:01:02.910 :     DateFactureStart = 
18/07/2025 : 14:01:02.911 :     DateFactureEnd   = 
18/07/2025 : 14:01:02.912 :     SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:01:34.591 :     Items retrieved: 0
18/07/2025 : 14:01:37.866 :     Count = 8832132
18/07/2025 : 14:01:37.867 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 14:01:37.867 : Leaving
18/07/2025 : 14:01:37.884 : Entering
18/07/2025 : 14:01:37.885 :     Client           = 
18/07/2025 : 14:01:37.885 :     DateFactureStart = 
18/07/2025 : 14:01:37.885 :     DateFactureEnd   = 
18/07/2025 : 14:01:37.885 :     SORTING: Column = montant, Direction = descending
18/07/2025 : 14:02:08.874 :     Items retrieved: 0
18/07/2025 : 14:02:11.833 :     Count = 8832132
18/07/2025 : 14:02:11.834 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 14:02:11.834 : Leaving
18/07/2025 : 14:02:11.834 : Entering
18/07/2025 : 14:02:11.835 :     Client           = 
18/07/2025 : 14:02:11.835 :     DateFactureStart = 
18/07/2025 : 14:02:11.835 :     DateFactureEnd   = 
18/07/2025 : 14:02:11.835 :     SORTING: Column = montant, Direction = descending
18/07/2025 : 14:02:42.578 :     Items retrieved: 0
18/07/2025 : 14:02:46.776 :     Count = 8832132
18/07/2025 : 14:02:46.782 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 14:02:46.782 : Leaving
18/07/2025 : 14:02:46.818 : Entering
18/07/2025 : 14:02:46.818 :     Client           = 
18/07/2025 : 14:02:46.819 :     DateFactureStart = 
18/07/2025 : 14:02:46.819 :     DateFactureEnd   = 
18/07/2025 : 14:02:46.819 :     NO SORTING: Using default sort
18/07/2025 : 14:03:16.901 :     Items retrieved: 0
18/07/2025 : 14:03:20.159 :     Count = 8832132
18/07/2025 : 14:03:20.160 :     WARNING: No items returned from Helper_GetData
18/07/2025 : 14:03:20.160 : Leaving
18/07/2025 : 14:03:20.161 : Entering
18/07/2025 : 14:03:20.161 :     Client           = 
18/07/2025 : 14:03:20.161 :     DateFactureStart = 
18/07/2025 : 14:03:20.161 :     DateFactureEnd   = 
18/07/2025 : 14:03:20.162 :     NO SORTING: Using default sort
18/07/2025 : 14:03:33.788 :     Entering
18/07/2025 : 14:03:33.789 :         Client           = 
18/07/2025 : 14:03:33.789 :         DateFactureStart = 
18/07/2025 : 14:03:33.789 :         DateFactureEnd   = 
18/07/2025 : 14:03:33.789 :         NO SORTING: Using default sort
18/07/2025 : 14:03:34.064 :         Items retrieved: 14
18/07/2025 : 14:03:37.011 :         Count = 8832132
18/07/2025 : 14:03:37.012 :     Leaving
18/07/2025 : 14:03:37.126 :     Entering
18/07/2025 : 14:03:37.126 :         Client           = 
18/07/2025 : 14:03:37.126 :         DateFactureStart = 
18/07/2025 : 14:03:37.127 :         DateFactureEnd   = 
18/07/2025 : 14:03:37.127 :         SORTING: Column = montant, Direction = descending
18/07/2025 : 14:03:50.241 :         Items retrieved: 0
18/07/2025 : 14:03:55.916 :         Count = 8832132
18/07/2025 : 14:03:55.917 :         WARNING: No items returned from Helper_GetData
18/07/2025 : 14:03:55.917 :     Leaving
18/07/2025 : 14:03:55.925 :     Entering
18/07/2025 : 14:03:55.925 :         Client           = 
18/07/2025 : 14:03:55.926 :         DateFactureStart = 
18/07/2025 : 14:03:55.926 :         DateFactureEnd   = 
18/07/2025 : 14:03:55.926 :         SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:04:07.944 :         Items retrieved: 0
18/07/2025 : 14:04:13.204 :         Count = 8832132
18/07/2025 : 14:04:13.205 :         WARNING: No items returned from Helper_GetData
18/07/2025 : 14:04:13.206 :     Leaving
18/07/2025 : 14:04:17.962 :     Entering
18/07/2025 : 14:04:17.963 :         Client           = 
18/07/2025 : 14:04:17.963 :         DateFactureStart = 
18/07/2025 : 14:04:17.964 :         DateFactureEnd   = 
18/07/2025 : 14:04:17.964 :         SORTING: Column = montant, Direction = descending
18/07/2025 : 14:04:26.878 :         Items retrieved: 0
18/07/2025 : 14:04:33.786 :         Count = 8832132
18/07/2025 : 14:04:33.787 :         WARNING: No items returned from Helper_GetData
18/07/2025 : 14:04:33.787 :     Leaving
18/07/2025 : 14:04:33.789 :     Entering
18/07/2025 : 14:04:33.789 :         Client           = 
18/07/2025 : 14:04:33.790 :         DateFactureStart = 
18/07/2025 : 14:04:33.791 :         DateFactureEnd   = 
18/07/2025 : 14:04:33.791 :         SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:04:41.965 :         Items retrieved: 14
18/07/2025 : 14:04:47.509 :         Count = 8832132
18/07/2025 : 14:04:47.510 :     Leaving
18/07/2025 : 14:05:20.653 : Entering
18/07/2025 : 14:05:20.655 :     Client           = 
18/07/2025 : 14:05:20.656 :     DateFactureStart = 
18/07/2025 : 14:05:20.656 :     DateFactureEnd   = 
18/07/2025 : 14:05:20.657 :     NO SORTING: Using default sort
18/07/2025 : 14:05:22.788 :     Items retrieved: 100
18/07/2025 : 14:05:26.008 :     Count = 8832132
18/07/2025 : 14:05:26.011 : Leaving
18/07/2025 : 14:05:26.888 : Entering
18/07/2025 : 14:05:26.889 :     Client           = 
18/07/2025 : 14:05:26.890 :     DateFactureStart = 
18/07/2025 : 14:05:26.890 :     DateFactureEnd   = 
18/07/2025 : 14:05:26.891 :     SORTING: Column = montant, Direction = descending
18/07/2025 : 14:05:44.671 :     Items retrieved: 14
18/07/2025 : 14:05:47.925 :     Count = 8832132
18/07/2025 : 14:05:47.926 : Leaving
18/07/2025 : 14:05:48.051 : Entering
18/07/2025 : 14:05:48.052 :     Client           = 
18/07/2025 : 14:05:48.052 :     DateFactureStart = 
18/07/2025 : 14:05:48.052 :     DateFactureEnd   = 
18/07/2025 : 14:05:48.052 :     NO SORTING: Using default sort
18/07/2025 : 14:05:48.129 :     Items retrieved: 100
18/07/2025 : 14:05:50.588 :     Count = 8832132
18/07/2025 : 14:05:50.589 : Leaving
18/07/2025 : 14:05:50.898 : Entering
18/07/2025 : 14:05:50.898 :     Client           = 
18/07/2025 : 14:05:50.898 :     DateFactureStart = 
18/07/2025 : 14:05:50.899 :     DateFactureEnd   = 
18/07/2025 : 14:05:50.899 :     SORTING: Column = montant, Direction = descending
18/07/2025 : 14:06:04.175 :     Entering
18/07/2025 : 14:06:04.176 :         Client           = 
18/07/2025 : 14:06:04.176 :         DateFactureStart = 
18/07/2025 : 14:06:04.176 :         DateFactureEnd   = 
18/07/2025 : 14:06:04.177 :         NO SORTING: Using default sort
18/07/2025 : 14:06:05.509 :         Items retrieved: 100
18/07/2025 : 14:06:10.761 :         Count = 8832132
18/07/2025 : 14:06:10.762 :     Leaving
18/07/2025 : 14:06:10.789 :     Items retrieved: 14
18/07/2025 : 14:06:11.125 :     Entering
18/07/2025 : 14:06:11.126 :         Client           = 
18/07/2025 : 14:06:11.126 :         DateFactureStart = 
18/07/2025 : 14:06:11.126 :         DateFactureEnd   = 
18/07/2025 : 14:06:11.126 :         NO SORTING: Using default sort
18/07/2025 : 14:06:11.415 :         Items retrieved: 100
18/07/2025 : 14:06:15.813 :         Count = 8832132
18/07/2025 : 14:06:15.813 :     Leaving
18/07/2025 : 14:06:16.290 :     Count = 8832132
18/07/2025 : 14:06:16.291 : Leaving
18/07/2025 : 14:06:22.025 : Entering
18/07/2025 : 14:06:22.025 :     Client           = 
18/07/2025 : 14:06:22.026 :     DateFactureStart = 
18/07/2025 : 14:06:22.026 :     DateFactureEnd   = 
18/07/2025 : 14:06:22.027 :     SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:06:36.981 :     Items retrieved: 100
18/07/2025 : 14:06:40.015 :     Count = 8832132
18/07/2025 : 14:06:40.015 : Leaving
18/07/2025 : 14:06:40.097 : Entering
18/07/2025 : 14:06:40.097 :     Client           = 
18/07/2025 : 14:06:40.098 :     DateFactureStart = 
18/07/2025 : 14:06:40.098 :     DateFactureEnd   = 
18/07/2025 : 14:06:40.098 :     SORTING: Column = montant, Direction = descending
18/07/2025 : 14:06:53.028 :     Items retrieved: 100
18/07/2025 : 14:06:55.509 :     Count = 8832132
18/07/2025 : 14:06:55.510 : Leaving
18/07/2025 : 14:07:01.750 : Entering
18/07/2025 : 14:07:01.751 :     Client           = 
18/07/2025 : 14:07:01.752 :     DateFactureStart = 
18/07/2025 : 14:07:01.752 :     DateFactureEnd   = 
18/07/2025 : 14:07:01.753 :     NO SORTING: Using default sort
18/07/2025 : 14:07:01.834 :     Items retrieved: 100
18/07/2025 : 14:07:04.142 :     Count = 8832132
18/07/2025 : 14:07:04.143 : Leaving
18/07/2025 : 14:07:04.194 : Entering
18/07/2025 : 14:07:04.195 :     Client           = 
18/07/2025 : 14:07:04.195 :     DateFactureStart = 
18/07/2025 : 14:07:04.196 :     DateFactureEnd   = 
18/07/2025 : 14:07:04.196 :     SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:07:18.844 :     Items retrieved: 100
18/07/2025 : 14:07:21.951 :     Count = 8832132
18/07/2025 : 14:07:21.952 : Leaving
18/07/2025 : 14:08:07.615 : Entering
18/07/2025 : 14:08:07.615 :     Client           = 
18/07/2025 : 14:08:07.616 :     DateFactureStart = 
18/07/2025 : 14:08:07.616 :     DateFactureEnd   = 
18/07/2025 : 14:08:07.616 :     NO SORTING: Using default sort
18/07/2025 : 14:08:07.683 :     Items retrieved: 100
18/07/2025 : 14:08:10.365 :     Count = 8832132
18/07/2025 : 14:08:10.366 : Leaving
18/07/2025 : 14:08:27.166 : Entering
18/07/2025 : 14:08:27.167 :     Client           = 
18/07/2025 : 14:08:27.168 :     DateFactureStart = 
18/07/2025 : 14:08:27.168 :     DateFactureEnd   = 
18/07/2025 : 14:08:27.169 :     SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:08:52.455 :     Items retrieved: 100
18/07/2025 : 14:08:55.830 :     Count = 8832132
18/07/2025 : 14:08:55.831 : Leaving
18/07/2025 : 14:09:00.533 : Entering
18/07/2025 : 14:09:00.533 :     Client           = 0026
18/07/2025 : 14:09:00.534 :     DateFactureStart = 
18/07/2025 : 14:09:00.534 :     DateFactureEnd   = 
18/07/2025 : 14:09:00.534 :     SORTING: Column = montant, Direction = ascending
18/07/2025 : 14:09:09.871 :     Items retrieved: 100
18/07/2025 : 14:09:11.691 :     Count = 55904
18/07/2025 : 14:09:11.692 : Leaving
18/07/2025 : 15:06:19.181 : Entering
18/07/2025 : 15:06:19.183 :     Client           = 
18/07/2025 : 15:06:19.183 :     DateFactureStart = 
18/07/2025 : 15:06:19.183 :     DateFactureEnd   = 
18/07/2025 : 15:06:19.184 :     NO SORTING: Using default sort
18/07/2025 : 15:06:19.375 :     Items retrieved: 100
18/07/2025 : 15:06:22.042 :     Count = 8832132
18/07/2025 : 15:06:22.043 : Leaving
18/07/2025 : 15:06:22.414 : Entering
18/07/2025 : 15:06:22.414 :     Client           = 
18/07/2025 : 15:06:22.415 :     DateFactureStart = 
18/07/2025 : 15:06:22.415 :     DateFactureEnd   = 
18/07/2025 : 15:06:22.415 :     SORTING: Column = montant, Direction = ascending
