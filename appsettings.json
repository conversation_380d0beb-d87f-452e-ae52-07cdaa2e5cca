{
    "ConnectionStrings": {
        // "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=aspnet-CarSystemsWebApp-76bec111-a2a5-4d25-9607-ca66423d92fa;Trusted_Connection=True;MultipleActiveResultSets=true"
        "DefaultConnection": "Server=vmi401426.contaboserver.net;Database=CARSYSTEMS;Integrated Security=false;User id=sa;Password=**********;Trusted_Connection=False;MultipleActiveResultSets=true;TrustServerCertificate=True"
    },
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
  "appSettings": {
    "LOGFOLDER": "C:\\SVN\\CarSystemsWebApp\\Logs"
  },
    "AllowedHosts": "*",
    "DetailedErrors": true
}
