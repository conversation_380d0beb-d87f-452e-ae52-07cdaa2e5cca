/* ========================================= */
/* PROFESSIONAL FILTER STYLING */
/* ========================================= */

.professional-filter-container {
    background: #f8f9fa;
    margin: 0.5rem 0;
    padding: 1rem 1.5rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.filter-title {
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clear-all-btn {
    background: white !important;
    border: 1px solid #6c757d !important;
    color: #6c757d !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.clear-all-btn:hover {
    background: #f8f9fa !important;
    border-color: #5a6268 !important;
    color: #5a6268 !important;
}

.btn-clear-all {
    background: white !important;
    border: 1px solid #6c757d !important;
    color: #6c757d !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.btn-clear-all:hover {
    background: #f8f9fa !important;
    border-color: #5a6268 !important;
    color: #5a6268 !important;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    flex-wrap: nowrap;
}

/* Professional inline inputs and buttons - Consistent styling for all filter inputs */
.compact-input-text {
    width: 320px !important;
    margin-right: 1.2rem;
    border-radius: 6px !important;
    border: 1px solid #ced4da !important;
    height: 40px !important;
    background: white !important;
}

.compact-input-date {
    width: 240px !important;
    margin-right: 1.2rem;
    border-radius: 6px !important;
    border: 1px solid #ced4da !important;
    height: 40px !important;
    background: white !important;
}

.compact-input-dropdown {
    width: 240px !important;
    margin-right: 1.2rem;
    border-radius: 6px !important;
    border: 1px solid #ced4da !important;
    height: 40px !important;
    background: white !important;
}

.compact-input-text:focus-within,
.compact-input-date:focus-within,
.compact-input-dropdown:focus-within {
    border-color: #F8981D !important;
    box-shadow: 0 0 0 2px rgba(248, 152, 29, 0.2) !important;
}

/* Ensure consistent height, padding and left-center alignment for ALL filter inputs */
.professional-filter-container .e-input-group,
.professional-filter-container .e-input-group.e-control-wrapper,
.professional-filter-container .e-control.e-datepicker,
.professional-filter-container .e-control.e-textbox,
.professional-filter-container .e-control.e-dropdownlist {
    height: 40px !important;
    border-radius: 6px !important;
    border: 1px solid #ced4da !important;
    background: white !important;
}

/* Text alignment and padding for input elements */
.professional-filter-container .e-input,
.professional-filter-container .e-input-group input,
.professional-filter-container .e-textbox input,
.professional-filter-container .e-datepicker input {
    height: 38px !important;
    padding: 8px 12px !important;
    text-align: left !important;
    vertical-align: middle !important;
    line-height: 22px !important;
    font-size: 14px !important;
    border: none !important;
    background: transparent !important;
}

/* Focus states for consistent styling */
.professional-filter-container .e-input-group:focus-within,
.professional-filter-container .e-control:focus-within {
    border-color: #F8981D !important;
    box-shadow: 0 0 0 2px rgba(248, 152, 29, 0.2) !important;
}

.btn-inline {
    background: #F8981D !important;
    border: none !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
    height: 40px !important;
    margin-right: 0.1rem !important;
    cursor: pointer !important;
}

.btn-inline:hover {
    background: #e6851a !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(248, 152, 29, 0.3) !important;
}

.btn-inline-export {
    background: #6c757d !important;
    border: none !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
    height: 40px !important;
    margin-right: 0.5rem !important;
    cursor: pointer !important;
}

.btn-inline-export:hover {
    background: #5a6268 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3) !important;
}

/* ========================================= */
/* GRID CONTAINER STYLING */
/* ========================================= */

.grid-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
    }

    .filter-actions-inline {
        flex-direction: row;
        justify-content: center;
    }
}
