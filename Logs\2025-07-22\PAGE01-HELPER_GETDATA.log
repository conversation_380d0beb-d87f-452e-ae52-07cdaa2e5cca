22/07/2025 : 11:11:53.637 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 11:11:57.323 : Error executing stored procedure: The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:11:57.346 : Stack trace:    at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.BuildIndexMap(IReadOnlyList`1 columnNames, DbDataReader dataReader)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 539
22/07/2025 : 11:11:57.586 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 11:11:58.280 : Error executing stored procedure: The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:11:58.281 : Stack trace:    at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.BuildIndexMap(IReadOnlyList`1 columnNames, DbDataReader dataReader)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 539
22/07/2025 : 11:25:51.904 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 11:25:56.905 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 11:25:56.954 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 611
22/07/2025 : 11:25:57.370 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 11:25:58.281 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 11:25:58.281 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 611
22/07/2025 : 12:05:11.351 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:05:23.582 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:05:23.610 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:05:23.834 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
22/07/2025 : 12:05:25.322 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:05:25.323 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:05:25.497 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:05:26.511 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:05:26.512 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:08:00.224 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:08:03.623 : Successfully retrieved 14 items
22/07/2025 : 12:08:16.131 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:08:16.800 : Successfully retrieved 14 items
22/07/2025 : 12:08:38.414 : Executing stored procedure with params: client=, dateStart=22/07/2025 00:00:00, dateEnd=11/07/2019 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:08:45.016 : Successfully retrieved 0 items
22/07/2025 : 12:08:57.247 : Executing stored procedure with params: client=, dateStart=22/07/2000 00:00:00, dateEnd=11/07/2019 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:09:28.179 : Error executing stored procedure: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 12:09:28.213 : Stack trace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:10:17.161 : Executing stored procedure with params: client=, dateStart=22/07/2000 00:00:00, dateEnd=11/07/2019 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:10:47.660 : Error executing stored procedure: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 12:10:47.661 : Stack trace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:16:31.085 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:16:34.814 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:16:34.854 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:16:35.148 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:16:35.847 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:16:35.847 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:17:48.036 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:17:48.587 : Error executing stored procedure: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:17:48.588 : Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:26:13.191 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:26:16.949 : Successfully retrieved 14 items
22/07/2025 : 12:26:25.180 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:26:26.211 : Successfully retrieved 14 items
22/07/2025 : 12:26:29.745 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=facturation, sortdir=ascending
22/07/2025 : 12:26:30.268 : Successfully retrieved 14 items
22/07/2025 : 12:26:54.436 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 12:26:55.242 : Successfully retrieved 14 items
22/07/2025 : 12:27:10.855 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
22/07/2025 : 12:27:42.327 : Error executing stored procedure: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 12:27:42.385 : Stack trace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 12:27:42.543 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=descending
22/07/2025 : 14:11:58.079 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 14:12:04.413 : Successfully retrieved 14 items
22/07/2025 : 14:12:09.235 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
22/07/2025 : 14:12:09.933 : Successfully retrieved 14 items
22/07/2025 : 14:12:17.304 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
22/07/2025 : 14:12:48.163 : Error executing stored procedure: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 14:12:48.202 : Stack trace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
22/07/2025 : 14:12:48.380 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=descending
22/07/2025 : 14:13:06.664 : Successfully retrieved 14 items
