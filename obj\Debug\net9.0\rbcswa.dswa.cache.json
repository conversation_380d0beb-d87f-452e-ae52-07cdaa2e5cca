{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["X8++Pjsh1crbljnq8Ynrp1qjnuykTJv05LOa59o2X8I=", "yubiEYDPKZYfRNQtmzUfO6TT5rfIlC7wOmkyfIdPebA=", "obHL0iusFbEmr2/fGvr3iE/0kxBGG/fY5FeQV0cQIE8=", "ofc+Gz3NNwq4nwWCnY0wp8z0wbPcW1QTv3I3lZU9TVk=", "zt+LetTzWiqMOloYwUdaAISpzrDPDtugGOfe20VaGW4=", "59GtGPMU7fFrdm5z8FXq5lga2guZzDbWjCy+YumupXY=", "kxqOT0FWrBxTw6yXrC0ZFZc3hnz+tLrQeI/G/SYThcc=", "CRWaiWNMv0UWnOBLJi30yXyAB0MeizVWzdFIw8kGdVk=", "7YOM92rJwnRfeaWtTVVIT+our4Bjn2zuvaFiu5zcwAk=", "m2L18+PPI2vgIlW8yYOVHf9wy2y5TVBOHMn4HJpK3Ow=", "SAipoed1ufiTvJq32g3pEj61pPES3t+332yv934fFVE=", "2rdabpN9KYXa4H5y39xR9FdKh7HQ6lKB0K2oGC1LJD4=", "6Tc0v2qwh5ILhcL0RWQuKNzzmlPRv8A0CZdmi1dNxEM=", "nIm9tjBFy19JrJNWwQb9ff4UbS+rxg4wkEHJlnqWXtI=", "ThrTHjDfeZtQawF/B+WvC+L22kGuUb6Sjt/MDQcnI/M=", "iigOD5QE7mG/jydH4IrwxTxhIYlYW8hL+Bdp6we3C0I=", "/0rURpz5OLOn57gHKH68d/SKjatjZRLnFjX52HyixHA=", "s/5r2AZ1ZNeKGtDOPstgJbsgylVEd5MJdT9xpzkuY7s=", "UVHYBr2Yf+RXxMrmg4uvtIyrnJgFUpiHuM5nRiSxsQk=", "Ptr6VBu0apLDyJ/Yu2aJSyVheAoT+fS3WSV3B4dRqtw=", "X4SRZy3SHtfe7qWQEdxZVOVMShhHaX0balkifpmdG40=", "4XfizdypDTN3E0iu5eo7YF/cA8WIyImZN1J3x67/16U=", "4QahwKilD+pSukV+Ny7JrdKoAaZl+BjusCx5xbCNpM4=", "bGakmsO0ur1LK06FcFWue0xRHcfYkXleWF02RV9yCUQ=", "238QKuztAOqD7hhTB162BNV6kelt9PamzZaOhVAEcVY=", "ZMDSWZ9EPKAeKUpcNdFViGn2TWKwRjNrRTdC80TJmV4=", "orYDaCoQ717QQkEuGtCao582Bvjmt2cmr3UZICYgVaA=", "BstRjAv7a2gDpeghAjUMWlCPwTk61qtnR0iX7WXJ/XU=", "P9gsqhzVWpbijuGcZhsdvfFvyXObc3OKYhJRwmbC60U=", "COaI/0oLPKEf8G2aRlHUBo0SjcEB40CPDtdbAGWZ5IY=", "SkJuFQh6zS7QhM9KX7EPgV6N3Qn6oPkbFj648pPdTGg=", "1lyykp/fz2xWAY0f8/tX5qrN4B2m3w8L/1xFylTHvCA=", "nxqE4qWkH4EchZL389FqVlieuGaJRJknlkRidoAbViQ=", "N/Ad6Cb5plRHKuETh1Frym/Rdc70uZIA8adS3RDU+qA=", "BtgIk7qR9c7SiiKBClMlP0vEJd9vxAcLVjDxF9ffwb4=", "a4X3VVQFG0pZo16CCwnVBn1gsYnKDhyrdElPoMtWldE=", "eYoui1ifNc4HOfZJWzy3O1EGiB8Gidoxbw1NW9U6LkU=", "zGOcwy++XdLS/JY5lIy4vcMV9ofABKMX8OMvHtb8OYk=", "9pQTcVZxkTGTxPGPPQ7AYPGGEWCXvje86sfvbPcnp/o=", "WrQG2ESY7v548SYvXKA1uZc4NYjIGP6FfHZCYwZKIiE=", "dneoZDRQq0VTWsAjCQedQQPJoW7oo0YxiYOIxvp7CeE=", "7QaKWkQWyeNRjM2qWinaCwoezV/7s/kZgNLt1pRk5bc=", "niGS/HbxURkzdSScxj6Z1LbbPP04DKbK1gLyGJZ6+AY=", "4Eil5yN7F+lrvBDAD3JJQ3Azz86lhR9U83NIiBuatSE=", "X+/JzwBDemST6XoG1dvZs1AIrwFvzD2Jff2La0mX5w0=", "kqMv77Mp9kH7gwKA+JUcZl10FoDwjDVs860StVEBJ8k=", "ekfvANhAJMYRMfkoJE87nxne0aGThHzDFRCVLGs6YFI=", "sfWcT9T/JgIZBmw6aJV7EOTk1pn/0U9f/rsfiOViGTc=", "UnYvu+xqqyIWdfEsE2CwurFicY9OEbCp0R1jUOe9Zvc=", "SnPn2LYMCOGODRhONzHxcUaZZUG66a+pgqC+iwJt26E=", "u+t4VPD8g8eJwCjHjDStLln5Ebs8Tr3nPeeJi8Vk4JQ=", "HILESO4z5kQinRis7eoE8C/GQPR1eRcznJCnIoHrZyY=", "OzBK+QX/Hlr9JY36UzfEWqFTKtJu+CObt6n4ZWer1ZU=", "v+Lwq+pqgh8qkH/Q+QfQP65bWewtfxTJUMSgHICsSt4=", "+m2lRCKB7LYGixEz4WOHgn3EsQ4VfVYrq/OSEx1Gw5Q=", "qdzwO7apFmBng0Vgr7cCxvydDBbKPCsBB9u26NZwiCw=", "OxBs4Gmwqj3XSpHZ2tREr+kPhmalhiadbZfzDjMDm58=", "NXplciq5Uadz3FipW4y/1HrxPuD43yZGGcfsg2xDEHY=", "oBKssYcZPTr0HSdYUa4oCGPwKuWa3yBZdmQyfy5f4BA=", "v6OJUBRg8sOGVi3RkP73uuactQoZ6a/jRBkJ8q6L/Kk=", "3vezKg0YkOf8/WpEgsdzvoPB5oAwd/1xOTUuwbYwud0=", "TNGy/ptkt0bjnBC7rRpjWqudUGSgh//nT7sPeHhyZlI=", "L7ijtAUaAkCbeOf76r/0CGpDPDtT660xYL4U9zt9Gks=", "7zMz4+HSNsM/LGqaCA9+WmWQlCFZZg2MZuoyVD8RROc=", "X83b0VbsnzaeOew/W58YeJWyvRmNsu+X+jMjnHbNC1A=", "8Vwkb1C3X8ZkTW5YRB+QckoYn+gJRCRtUfmevs2G52U=", "WUfD35DO4Z0+AtW3/d6CviOIYFbwAPx5lRycrboENWE=", "P5kJMCUa85gsgx/DbIJ+0mYn4/sPfQkW63yIjJ3nlq0=", "qa6flPht3yZwJ6l4R87usX6QyHXVmtGTguKzSIu+kD0=", "kkvJfYHhe+yLXBNonDbzSWhot6/njn9hv01dMyXY37g=", "xRz9iy1KGnO9cbwR1kK1+tE08O1mhyK4gpNcvwdj8LM=", "w+/L1LZmaC8iBVb2DFD3oJ4D4mqXHMd/kd9ZzSbBzb4=", "Af7Kfk7KCso4nRQmjKPTwQxCb27wLHVbeDLQb0Zq/9o=", "i2VKl/K0IM1MDcmX45q7GKdkaj5RAH072sXayDLAu3s=", "67zogt7M+luNs9DNyCm6DoisrJrXrjPAJHUR8X/NqC0=", "jw/6DhqPqcS7yauYRdtihJrYmr1dhVd7UOZc/1RRJVM=", "L4bW4OTFhXg1sclMa3Vck1FkUskCBXFOI9fMwVbDdPU=", "2O/c8JProyxLFGUx9z/gOoKTk3uMXor1rkueTRt4Hfg=", "G/jPvgcLS4Bw3Yj2NAq9hdn/kiWYMRdeN0SehPF0eJQ=", "/TRxbUzjKu4BHaW3ukayYCbx+GwWbE+bcTQPcHjSBBE=", "2PDsia5VDrSuit3BmD41eQ8F434EUJpeAaoSrNzxby4=", "yR8xBH7LKTpz1G1xnf22v4BRQ8cCv+4hUCHx6CZmKq0=", "AVGW8kIpKVHz4sM6SabWqVBz7Dwso2h8OzX4ia25UW8=", "M2i8TzKGPFnomEcuOH8O74JYBFgwCf0ZPYZdSVErAOI=", "aiss0gjtiHVPjylb3hVS4f4YilDdLZwN2Ryal97hK/M=", "WTzrYZdp61CUJ0z/EjyI12QsFyVRbUutA14OWAEeYgc=", "llEtUXIYgk6QBnXFQIrBidebPBgPk6TrqPFBgII2SH0=", "b9Xe3jpImhTgaVoLq/ZB01JvVHMaB0+qmDtTNsowdZI=", "Yi86Xods+/J415tYICyM/N+wKeEqqXHhkVVF+BiCbJM=", "CbDbi8H+abxENUKDrvLnqViWAlba5xjTuyXIuz1Kobw=", "5RSGhfuf6eoLmrSfI/XSxK5C3knZD/LJqI65mHjVuv8=", "bB9fiCiRc3CDZGE6ipJVxZ+p+B7qTu7kVh6GeXP2FI4=", "rhF/DLsPyWQ+4s1S5DAlND4J29V8jC0qgI7z9SOKhj4=", "gW+8eOVR4F0pRyATXG3YXM7EfMM+/4PwxKun4l6saVs=", "0r722tpU+l3bcSjIeBMKSR8P4exTb8lQ00t7S4B8T/g=", "yfQallAmtERyQUVJRQpg529XGPBB9O5QWLTO9AyZfZo=", "b1V8q5TK8rMjEc9AA/YUxp9jiLUcFYMz8GnTYFWBZug=", "s8PYVP4z/apL+asnxfGYQbMmAcblVrmeQ+Xs1M4vogo=", "qY96v5gzCne4M7nBPAhkKfNfk7lZNcIUoD2VLMf2gxo=", "6yZsqU18Ko6nfwCZ9P4qSFBRbrE7sM4nQS+FXboCXHQ=", "qetZ2iNIsR21eNr+bEMUiDGh66nsCLmSwoKsI4QLzBU=", "nICUddkMgg8uDonQ9z9VMLMcLdSxcFx2vIOjGlj3dyY=", "OJfxwAonygk6ApSU5/6OdbtRk5MNTQRdWGpQVfTch3U=", "JfrkUW88aLBRX6zwXY9nx1WM28vYgHTwVrA0rhBhIEA=", "Bs3wS/vBByCTIWyQiJcBcf1w/QGbtR5VLdDMDWZDI90=", "0+695KLC967jFusUfqBAjSVwT3ePyZMN77hnqFP9OKI=", "o1kbU69Yy3Jmrp1cDFYCX9g0KaEFWNPbDLpX+feAHm8=", "yTGwZFyrg4ajLqaYjttwkpo1b+twXHjazzpOE+C4QeI=", "PgGxbWbY1sJ2+5+3f4/4PPZyP73yOWafqdAKnLK1ekI=", "Ua5f0t9zOr6pRCnFOTx21G20EjCxBSdW4Oy5zXGAKYw=", "EuU75Nsezm6FJsf7qAj5zau+0TFho+JAIb0yMK2Dxg8=", "KqNuitTt76RV8sjqZoMy5+hTiS9MqknwOxMlXHb2AnY=", "CX+sPAzQPkfzg+I1QAJVYz+EWnky9XFp8zwqnBx+h+Q=", "cDvaSTAvvJvDV0twkM9vzlznWnnPZ9Yd4JFYoCMcefo=", "L7SBZvwO6+4EERkle9535KIZfgtQf6ZIR2rwRu9WcGE=", "Kfnp0YWoKkpWPhnwoW/E9Z4hVpCbyWE8rGy2eN4/iHA=", "Scwf0LNqXwOWW4iP5FzPoNl2Ir7GbMQRTWSmAL60CPg=", "RdOzK0Jpal43Rqj0vwJwK8TjC3ilXsZl6l9+XcCtZ/c=", "O1yvqQe1m39hzqzSf6qQoB+rV7+3mpy6C3W837V+vYk=", "xLf+DjmhVYBAmjGW2Z/Dkum8e6us4yKCZFkJeLTwuc0=", "AKIysNXQrbObpxauv27TTgm0PZWOTCrj7lezcb+4nes=", "l7F1KNiUuCBRUB8XJg4HK4rLbW5yhKOCURGsIti/ks4=", "3ETurXJp+vGvidHwxAwwgNoJzpMJ9lT9cG2bbKIPCyk=", "xX/dtqbl1eR60TM8+9Ti579FRQGfcVsHoPBeSQPuX1E=", "YI3llOXMJnXdLAiwGZg2HvuY/A+xGs9g+hVDFY+TGuA=", "8cJWBUkGYtFYSj+N6iMjVEVBxW/5PAcBPi1T86UWD1M=", "Q11Bj1XoDkHTvaShRmkujYVOuZA8b4TqsMOaYGCMqwg=", "UFjFVf5oQDEhVU5UN5qmuSLo4cWN1ZuxCie+vOqHhzM=", "OQFVuxkB5Z2yrmMXOasB9O8wTJjylQ/+BPavXAEZwNo=", "zkOViUv02G+One0CiW+m8Kdib4y6NZhPQ/I6P5wwt+s=", "jq0H8rcSibzAH+7HSpu8Jud3tbRgQ8Ma332SurElje8=", "anTCZJb3NwN1QS61Kp74FisYwXlOXYliflsZM/ZIjf8=", "TKUybwi9NAq2fI2G80T75iipVAEn5BrklXPUOXsQ3bk=", "1apzXyOxnoM90dPmLZcs2F7oFgnKt8HR+Nm2MEoQSjc=", "4pAY2QH9BW5hdCtBnvcWSxBhv2x/fyEG5fgfVzHo0dI=", "xW2CBHPi2UgPRCdDt5XzgGEn1d1pcpoPJrAS33gUSRo=", "fZ+u2EZKHvquo8p3Sf8wdBUfVBz5GTVtlWNTUed6MOI=", "PWK5kt5D508CNOwS/Bt19XyMFH8mHthSHZ6nypzZ+y8=", "mh7lXg/0rwwOVksujiqkQpPd9tvIqm5E7MA8CzA4rwo=", "1zXtB3K9lUEUlLbfs4DjZhGgPCeFsHiSTlkCJwP+Cdw=", "JGF+sbxaq0pC8NXVYeZQR+mjioS9cL6MGzXdHqOcMfg=", "by+jVGNw7JmqcKpQ0B9cAz3u2p/l3OfwJJfBTraeCwY=", "vzqabFPxKY75zmLO6vek8eHJjIJ4B2adhO1NdHj7ii8=", "V+PdUs/0T43ACQ9GSndPkytAZpR3g0AwV6hpNYH+oNE=", "raX/31g17wfBYTTODsenGvWdw4RfsaQ4vDK5aaKECT8=", "Wz1lh3hfDeZy+mJmMtK2BnUcem8vbbqk2FrnTOKCcTI=", "SHK5BizqymzNV2cUDuttr1FTilg6pQixou2SEYXOQK4=", "sPczEDpszXUaFK97hb9aWHyJRp95Uz1HD6IWy8XHeRs=", "nbjP8/DVg59W8cMdToiZPqUDWG8+WTqSj3Ln3e2t0SM=", "0Q2zBTzsqMK7uN3odUBUNqseO2tduCtOvsTHQbxkGIg=", "Y4840CAD0vOMT6Aficc+mH4wromQkidL7L2X/XvaC0g=", "cGs3rbA2tH4FWI0pfb/gX/45LcaQVWMHKXT5bKuhX84=", "KYtAuPrmFwHO+TFBYOVNrFkXcoRcSdXX/vkHpXA/Dao=", "DSvNVK6mEdYwNpJZKZjASJFJ7s+jgYkDbnw1NSC08I8=", "M/qhNQwGdx7PtcF5lU5V7BWqSIRf698B0z2khFyG8Zc=", "3g7974mezK3A/DGAVrfgj3pvNKiXbP6sC6PvBXEkt/g=", "tAqwKl0IocwT+6DackwMZtcPGYkxRM9+MiJi6nakj4o=", "+fp2atGiVa5cYJ+rnic44U70lkGOW0TaiV6FUQsePrc=", "T1KLZ65t8fiYsZL5wo1AGwBsd3zWAHCiuN31lvjeu+o=", "jCzqaHESqnoywshag+5SDf/mOd7KfKyDNlUB0+2qH9k=", "omqDfPhJg4RbEuNaBYLBhB5titgG6sL0hgp0xpal2BI="], "CachedAssets": {"X8++Pjsh1crbljnq8Ynrp1qjnuykTJv05LOa59o2X8I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\d77l2qy8cy-yjsawtmxkf.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popup.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\popup.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxg8mej01i", "Integrity": "Rvo5730Gqc6T+GqkJ6GJvLu0f5e+7JwxpNxOB0I4uhY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\popup.min.js", "FileLength": 4306, "LastWriteTime": "2025-07-24T03:33:23.6000006+00:00"}, "yubiEYDPKZYfRNQtmzUfO6TT5rfIlC7wOmkyfIdPebA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\kg2ram854o-pry39u351y.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popupsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\popupsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7prbqzg2di", "Integrity": "BVnGTQOmUYbnk2m8HmX+/4HRIm1xrdJ33yUCM4kwTw4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\popupsbase.min.js", "FileLength": 4448, "LastWriteTime": "2025-07-24T03:33:23.5988398+00:00"}, "obHL0iusFbEmr2/fGvr3iE/0kxBGG/fY5FeQV0cQIE8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\idlbwfjf3e-wxxhl6bdxk.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/sf-svg-export.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\sf-svg-export.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iuml4f9otc", "Integrity": "6hZwMpO379FzNpK8fKPpdaPUkyErTNNRp0BRYubQprU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\sf-svg-export.min.js", "FileLength": 4906, "LastWriteTime": "2025-07-24T03:33:23.6087242+00:00"}, "ofc+Gz3NNwq4nwWCnY0wp8z0wbPcW1QTv3I3lZU9TVk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ckt86gem57-uwltx3nw3a.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/svgbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\svgbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bhq7u1hmuk", "Integrity": "MWzH94Njw8GJVA+ElnVeCEwZJgGTF1T7hFqCzBctCJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\svgbase.min.js", "FileLength": 12642, "LastWriteTime": "2025-07-24T03:33:23.6149007+00:00"}, "zt+LetTzWiqMOloYwUdaAISpzrDPDtugGOfe20VaGW4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\dnwq9wk44u-apzeiyhkym.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor-base.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nrwla0anoc", "Integrity": "3bxpHWKdwYzyeK86J0KT525Cq87UoMczNQezhwQnFG0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "FileLength": 77607, "LastWriteTime": "2025-07-24T03:33:23.618698+00:00"}, "59GtGPMU7fFrdm5z8FXq5lga2guZzDbWjCy+YumupXY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\5h81voagx1-jb1fsvbwmh.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9kzci13enq", "Integrity": "CeVUc2cK5bhULgJK2K/jML+zPvKDO7ycrM82wQ75tZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "FileLength": 902633, "LastWriteTime": "2025-07-24T03:33:23.8261434+00:00"}, "kxqOT0FWrBxTw6yXrC0ZFZc3hnz+tLrQeI/G/SYThcc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\2ndbdiwf0k-egxkhblomv.gz", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/sf-spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\28.1.33\\staticwebassets\\scripts\\sf-spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zgwrhatjg0", "Integrity": "I4bViHX8ZAQTvTskuTIMTGR4FGRp34aP0UOQH0YohSg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\28.1.33\\staticwebassets\\scripts\\sf-spinner.min.js", "FileLength": 471, "LastWriteTime": "2025-07-24T03:33:23.6257799+00:00"}, "CRWaiWNMv0UWnOBLJi30yXyAB0MeizVWzdFIw8kGdVk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\g591birpkc-k2vwsc1rrx.gz", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\28.1.33\\staticwebassets\\scripts\\spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9uoxj9nspg", "Integrity": "ASMhIRhUeJ4inpc60499TEBeyh8/F1RamietsyLmCRg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\28.1.33\\staticwebassets\\scripts\\spinner.min.js", "FileLength": 3385, "LastWriteTime": "2025-07-24T03:33:23.6426907+00:00"}, "7YOM92rJwnRfeaWtTVVIT+our4Bjn2zuvaFiu5zcwAk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ha3ck1f4wq-5n19sw3ck4.gz", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-floating-action-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\28.1.33\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a24etay6qm", "Integrity": "7q7MeOs8ecXBTqKSTGymu4crfwa2bVVLFAjU334qvrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\28.1.33\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "FileLength": 711, "LastWriteTime": "2025-07-24T03:33:23.6595877+00:00"}, "m2L18+PPI2vgIlW8yYOVHf9wy2y5TVBOHMn4HJpK3Ow=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\onzwzcnqot-c8qcbx19x1.gz", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-speeddial.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\28.1.33\\staticwebassets\\scripts\\sf-speeddial.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etc3rn1cfe", "Integrity": "oircZYCULIkKbIGu+fxo7W3XflXW7Wa3cmlwFK+v/Xk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\28.1.33\\staticwebassets\\scripts\\sf-speeddial.min.js", "FileLength": 2523, "LastWriteTime": "2025-07-24T03:33:23.679535+00:00"}, "SAipoed1ufiTvJq32g3pEj61pPES3t+332yv934fFVE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\k2lc9f2kwg-uva34w6zt2.gz", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-dialog.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\28.1.33\\staticwebassets\\scripts\\sf-dialog.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gi6vfa4dvy", "Integrity": "uIntZVOcNAl6v/Xf2UKMoq+I+B5CBbFZbAsR9nxQq3M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\28.1.33\\staticwebassets\\scripts\\sf-dialog.min.js", "FileLength": 5562, "LastWriteTime": "2025-07-24T03:33:23.683532+00:00"}, "2rdabpN9KYXa4H5y39xR9FdKh7HQ6lKB0K2oGC1LJD4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\3spo2eianh-7x1dq6ioj2.gz", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-tooltip.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\28.1.33\\staticwebassets\\scripts\\sf-tooltip.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t236jp9orh", "Integrity": "FdyB1US+TfAONHW8RWN9hJf4G/4BddIaE5axEct3E6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\28.1.33\\staticwebassets\\scripts\\sf-tooltip.min.js", "FileLength": 6990, "LastWriteTime": "2025-07-24T03:33:23.6875139+00:00"}, "6Tc0v2qwh5ILhcL0RWQuKNzzmlPRv8A0CZdmi1dNxEM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\bm0n96jk7v-5n8nqsbnwi.gz", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/sf-drop-down-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\28.1.33\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gotbe9esbo", "Integrity": "AN0LG+nOR5UW2zwEUkdy0kN/iBGNX6zc5a+wOhnKt/A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\28.1.33\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "FileLength": 2443, "LastWriteTime": "2025-07-24T03:33:23.6915026+00:00"}, "nIm9tjBFy19JrJNWwQb9ff4UbS+rxg4wkEHJlnqWXtI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\p2zgtah1ds-5b68rh03lf.gz", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/splitbuttonsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\28.1.33\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pybw830qdw", "Integrity": "b4e8DHQ2jMeVeSqP/x3mBCBVw7mVF7maoW5HIt1Heo4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\28.1.33\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "FileLength": 1447, "LastWriteTime": "2025-07-24T03:33:23.6934972+00:00"}, "ThrTHjDfeZtQawF/B+WvC+L22kGuUb6Sjt/MDQcnI/M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\9zg8dqxfdk-eaupmbvzkh.gz", "SourceId": "Syncfusion.Blazor.Notifications", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Notifications", "RelativePath": "scripts/sf-toast.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\28.1.33\\staticwebassets\\scripts\\sf-toast.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1f29n6q86", "Integrity": "of05eyVq8uxYAnDkeSFe7MGaXCqCCqY0gl26o383nOo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\28.1.33\\staticwebassets\\scripts\\sf-toast.min.js", "FileLength": 2215, "LastWriteTime": "2025-07-24T03:33:23.7074957+00:00"}, "iigOD5QE7mG/jydH4IrwxTxhIYlYW8hL+Bdp6we3C0I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\lc6ok7c9tb-l85ebp93ri.gz", "SourceId": "Syncfusion.Blazor.Data", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Data", "RelativePath": "scripts/data.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\28.1.33\\staticwebassets\\scripts\\data.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cgt1e5lmxy", "Integrity": "XB3Mn3s2CrRwPnCthwBgNvEZRW4TZAReb82gbuX1CKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\28.1.33\\staticwebassets\\scripts\\data.min.js", "FileLength": 23541, "LastWriteTime": "2025-07-24T03:33:23.7164461+00:00"}, "/0rURpz5OLOn57gHKH68d/SKjatjZRLnFjX52HyixHA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\4l15mon31j-fz2x7ir898.gz", "SourceId": "Syncfusion.Blazor.Lists", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Lists", "RelativePath": "scripts/sf-listview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\28.1.33\\staticwebassets\\scripts\\sf-listview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "714s1mc4x9", "Integrity": "lYNlFHX/5vrZhS2mdU1cDNSeMcfJPo7uk8nHfKylsas=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\28.1.33\\staticwebassets\\scripts\\sf-listview.min.js", "FileLength": 5167, "LastWriteTime": "2025-07-24T03:33:23.7214227+00:00"}, "s/5r2AZ1ZNeKGtDOPstgJbsgylVEd5MJdT9xpzkuY7s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\jsshet654m-062l3blnyv.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-colorpicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-colorpicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h3xyw6fshk", "Integrity": "8zvxN7jLkgTQNAUibZIeoUhPg5a699P2g0vrxVKM8kg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-colorpicker.min.js", "FileLength": 1936, "LastWriteTime": "2025-07-24T03:33:23.7284035+00:00"}, "UVHYBr2Yf+RXxMrmg4uvtIyrnJgFUpiHuM5nRiSxsQk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\6wjchtuxco-md22cen34k.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-maskedtextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "93vk5zc9f0", "Integrity": "ZWtn6YXEoBcG5Wejw4Smcgy2oxA5WJVzbJ51HYWXIGM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "FileLength": 2567, "LastWriteTime": "2025-07-24T03:33:23.7330665+00:00"}, "Ptr6VBu0apLDyJ/Yu2aJSyVheAoT+fS3WSV3B4dRqtw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sadf1bszs1-ldxptwqz63.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-numerictextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dt9v9omfaw", "Integrity": "cWxnyMNLhcoJTryrq7AbrT8NgAxp2ANxvMybeDDl0bI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "FileLength": 2985, "LastWriteTime": "2025-07-24T03:33:23.7363833+00:00"}, "X4SRZy3SHtfe7qWQEdxZVOVMShhHaX0balkifpmdG40=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\12khajnlde-en71m9z60k.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-otp-input.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-otp-input.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u3sz72nyuy", "Integrity": "ChhHkDeoiDJfI4PvyAHbOHkUWCVik0+2s62FmG40J6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-otp-input.min.js", "FileLength": 949, "LastWriteTime": "2025-07-24T03:33:23.5988398+00:00"}, "4XfizdypDTN3E0iu5eo7YF/cA8WIyImZN1J3x67/16U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\vbtp9tnch6-r9etc6lo8c.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-rating.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-rating.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bsus4y1qj8", "Integrity": "JZmmT4t+37e9ZZ5WBRadovdsWvBjmRskJhudNPQ3NyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-rating.min.js", "FileLength": 2257, "LastWriteTime": "2025-07-24T03:33:23.605732+00:00"}, "4QahwKilD+pSukV+Ny7JrdKoAaZl+BjusCx5xbCNpM4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sb4o3odzm8-0e1uinced3.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-signature.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-signature.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x8tmthci5f", "Integrity": "b2FNlWEJoP2zL+iB4HSfuKOBxlpgG4jyqZ+4/z4XrSs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-signature.min.js", "FileLength": 5750, "LastWriteTime": "2025-07-24T03:33:23.6168055+00:00"}, "bGakmsO0ur1LK06FcFWue0xRHcfYkXleWF02RV9yCUQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\jazegy34qu-c6u0qf2no5.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-slider.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-slider.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8x2qn4ksxm", "Integrity": "tCdwZf3thTyl+ppV1KXCxi3B9M1kKv/r+mtHSOTX4ao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-slider.min.js", "FileLength": 6202, "LastWriteTime": "2025-07-24T03:33:23.6266757+00:00"}, "238QKuztAOqD7hhTB162BNV6kelt9PamzZaOhVAEcVY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ffhxaeu1yb-l6yft8nmjx.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textarea.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-textarea.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "es7sksk9kw", "Integrity": "kuWf/VugSIWsgjrwp3RYT5n5KQNrwakOrniMHZTUC6w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-textarea.min.js", "FileLength": 606, "LastWriteTime": "2025-07-24T03:33:23.6396461+00:00"}, "ZMDSWZ9EPKAeKUpcNdFViGn2TWKwRjNrRTdC80TJmV4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\pq6ds00kew-txr38xaew3.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-textbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6bc63icqsb", "Integrity": "LtFPfUjcv4vYQaK+1AsCEjy2FS/IRGjJm18viDVDBkU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-textbox.min.js", "FileLength": 1026, "LastWriteTime": "2025-07-24T03:33:23.6446283+00:00"}, "orYDaCoQ717QQkEuGtCao582Bvjmt2cmr3UZICYgVaA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\gk0sdbowrt-zqfxqc7h78.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-uploader.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-uploader.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j6pvo2h1j6", "Integrity": "xb6DZK+7yMLPDrjyGVgvMuur5D1eKUsIZpuXggZv7gM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\staticwebassets\\scripts\\sf-uploader.min.js", "FileLength": 18962, "LastWriteTime": "2025-07-24T03:33:23.6543431+00:00"}, "BstRjAv7a2gDpeghAjUMWlCPwTk61qtnR0iX7WXJ/XU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sekkta200r-fkeymg2bem.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-dropdownlist.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "egz2otb51e", "Integrity": "AkQcxmEBG7uciCt+SlZByhjBdnSutZgtg820zarK4+g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "FileLength": 9173, "LastWriteTime": "2025-07-24T03:33:23.6595877+00:00"}, "P9gsqhzVWpbijuGcZhsdvfFvyXObc3OKYhJRwmbC60U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\0yyctx1peb-bgrzg7rlmy.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-listbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-listbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yxm76dhseo", "Integrity": "5pk2qT4onCoKRvJYfbN7StsvqLaLlvyDq2tBcJqUhdo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-listbox.min.js", "FileLength": 1900, "LastWriteTime": "2025-07-24T03:33:23.6625798+00:00"}, "COaI/0oLPKEf8G2aRlHUBo0SjcEB40CPDtdbAGWZ5IY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\eiq2m65tqw-ybs121n133.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-mention.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-mention.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pwxpc9hkwa", "Integrity": "6dd6ofTQxw4A7dXrUi0Jn35MqG/V270uSRlcoyPf/gI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-mention.min.js", "FileLength": 5510, "LastWriteTime": "2025-07-24T03:33:23.6655731+00:00"}, "SkJuFQh6zS7QhM9KX7EPgV6N3Qn6oPkbFj648pPdTGg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\juc69m01qj-he4dq8bd6g.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-multiselect.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-multiselect.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rmk0p0y0jx", "Integrity": "jb5nsQrzeWfjZNMHeLJDUXswpIFYBmKzHRLUrhGYxxE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sf-multiselect.min.js", "FileLength": 8421, "LastWriteTime": "2025-07-24T03:33:23.6697833+00:00"}, "1lyykp/fz2xWAY0f8/tX5qrN4B2m3w8L/1xFylTHvCA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\anji1rpska-fry9ul2cjz.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sortable.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sortable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhsi0whmqx", "Integrity": "Oo9WrWwheMXDWKtwBnQFird5/XN8rxasSMIkS+PclrE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\staticwebassets\\scripts\\sortable.min.js", "FileLength": 3074, "LastWriteTime": "2025-07-24T03:33:23.673551+00:00"}, "nxqE4qWkH4EchZL389FqVlieuGaJRJknlkRidoAbViQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\vihrpo2kci-izdm59gffl.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/navigationsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\navigationsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zm72hurp3", "Integrity": "DzZHDmHTTNk3xnoUXfYEHqTt60qb1c+b3wbnboR4eDo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\navigationsbase.min.js", "FileLength": 3965, "LastWriteTime": "2025-07-24T03:33:23.6755457+00:00"}, "N/Ad6Cb5plRHKuETh1Frym/Rdc70uZIA8adS3RDU+qA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\nr2uivr86d-prhazfhqkb.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-accordion.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-accordion.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "odjes354jk", "Integrity": "nDHvbxXNZ0oszpGJo7OBhOHrLR3PXOAnYwgptRu/DGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-accordion.min.js", "FileLength": 3214, "LastWriteTime": "2025-07-24T03:33:23.683532+00:00"}, "BtgIk7qR9c7SiiKBClMlP0vEJd9vxAcLVjDxF9ffwb4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\0pcagc9na8-ptzcx29gqk.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-breadcrumb.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ingfd9l4rw", "Integrity": "8ryXWVnUAhrBJcJBanxzIXjqiaeQISPxHHVTosyVAf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "FileLength": 1582, "LastWriteTime": "2025-07-24T03:33:23.6875139+00:00"}, "a4X3VVQFG0pZo16CCwnVBn1gsYnKDhyrdElPoMtWldE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\9ispkuv7ij-9j4184f5ps.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-carousel.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-carousel.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sslw0rcvfi", "Integrity": "XAK8Ed2/1u5T5SE1yxu3r93Nv6FgUfQO+GELyXlZcVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-carousel.min.js", "FileLength": 1729, "LastWriteTime": "2025-07-24T03:33:23.6915026+00:00"}, "eYoui1ifNc4HOfZJWzy3O1EGiB8Gidoxbw1NW9U6LkU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\tqhdg3t9bq-7cprwqmhxb.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-contextmenu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-contextmenu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dybqdtg15m", "Integrity": "BcAwr2fdmdFZACquabOQk41u9FKkan22GHuOp7saPrQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-contextmenu.min.js", "FileLength": 4628, "LastWriteTime": "2025-07-24T03:33:23.7115085+00:00"}, "zGOcwy++XdLS/JY5lIy4vcMV9ofABKMX8OMvHtb8OYk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\eejr38ihun-1h83i2jdjo.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-dropdowntree.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zxtjy2sds7", "Integrity": "V8gRuLgbN/wlOREOZS7EunAm7XJXyv1TO+88UtgtR+M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "FileLength": 5690, "LastWriteTime": "2025-07-24T03:33:23.718606+00:00"}, "9pQTcVZxkTGTxPGPPQ7AYPGGEWCXvje86sfvbPcnp/o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\djau0kle4k-v86lnv86r9.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-menu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-menu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8an5usm9d", "Integrity": "nzA8QB17tg1DhUzCePhupdlAFL8r7U58CWsxwbeB+04=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-menu.min.js", "FileLength": 3843, "LastWriteTime": "2025-07-24T03:33:23.7224564+00:00"}, "WrQG2ESY7v548SYvXKA1uZc4NYjIGP6FfHZCYwZKIiE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\mfqtlt087y-20g5spjpjl.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-pager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-pager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pbpdgwf0my", "Integrity": "dUGI8+QK1sMvyE8fcuehshZvQF/8RcxS97K5gnkwy2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-pager.min.js", "FileLength": 2279, "LastWriteTime": "2025-07-24T03:33:23.7254444+00:00"}, "dneoZDRQq0VTWsAjCQedQQPJoW7oo0YxiYOIxvp7CeE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ukontf0to4-ytl9r33jrx.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-sidebar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-sidebar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v1g2piujio", "Integrity": "w54GAGmjm2kdRLSV73S1eVe9DBzUHjNCgDOA0RJHCSs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-sidebar.min.js", "FileLength": 2751, "LastWriteTime": "2025-07-24T03:33:23.5958069+00:00"}, "7QaKWkQWyeNRjM2qWinaCwoezV/7s/kZgNLt1pRk5bc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\grozcvm4ej-z31qfl1oq9.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-stepper.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-stepper.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3l1yn4jv7w", "Integrity": "ecwA9Sxf23L8SgjmXh79KIbkeh+eIV4CpsRejRpDM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-stepper.min.js", "FileLength": 3488, "LastWriteTime": "2025-07-24T03:33:23.603875+00:00"}, "niGS/HbxURkzdSScxj6Z1LbbPP04DKbK1gLyGJZ6+AY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\jhhm3fquz3-278xpgbrpw.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-tab.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-tab.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hbtv9aa3u7", "Integrity": "0+jcnwlgZEPb4BcCCcZ+FjE+BBXDdmL4F9i3X/1W708=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-tab.min.js", "FileLength": 7649, "LastWriteTime": "2025-07-24T03:33:23.6097219+00:00"}, "4Eil5yN7F+lrvBDAD3JJQ3Azz86lhR9U83NIiBuatSE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\2m61bm772c-l6175u0glx.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-toolbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-toolbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gnggofq9jg", "Integrity": "TEy9uurzjcJaNFzDTeQNKvy7MgsHp90MkWuSMEfKem4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-toolbar.min.js", "FileLength": 9303, "LastWriteTime": "2025-07-24T03:33:23.6236837+00:00"}, "X+/JzwBDemST6XoG1dvZs1AIrwFvzD2Jff2La0mX5w0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\diku15o4fe-at8sajt0xd.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-treeview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-treeview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8pxjaqgwrf", "Integrity": "XCNjftGJSk43jDmIVSX3JkweGFaWb39ioHevXMAtyrk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\staticwebassets\\scripts\\sf-treeview.min.js", "FileLength": 11897, "LastWriteTime": "2025-07-24T03:33:23.628756+00:00"}, "kqMv77Mp9kH7gwKA+JUcZl10FoDwjDVs860StVEBJ8k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\pl2kkyous1-rrq57mnj2a.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-calendar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-calendar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c5tz5rbpg6", "Integrity": "UN/Kndl/1MUQl0eHiTKPM+cMOQXxtdMxZ52eWOdUWdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-calendar.min.js", "FileLength": 811, "LastWriteTime": "2025-07-24T03:33:23.6336573+00:00"}, "ekfvANhAJMYRMfkoJE87nxne0aGThHzDFRCVLGs6YFI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\u3460sy28a-sjnzg8udn3.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-datepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-datepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyaqos3a2g", "Integrity": "CY7O+yzP7/n6tvsuhQbLNpR4N88MSAHdtkhcfjjNHrQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-datepicker.min.js", "FileLength": 8630, "LastWriteTime": "2025-07-24T03:33:23.6466226+00:00"}, "sfWcT9T/JgIZBmw6aJV7EOTk1pn/0U9f/rsfiOViGTc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\nq0zaazork-h4dszf0prz.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-daterangepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ds497am19q", "Integrity": "fe9u/GkjiOpJkhMQNHKgPKUl0bSEoEPJ/IbtP6XoXtE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "FileLength": 3819, "LastWriteTime": "2025-07-24T03:33:23.6496149+00:00"}, "UnYvu+xqqyIWdfEsE2CwurFicY9OEbCp0R1jUOe9Zvc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\6efprx52i0-am4733672y.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-timepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-timepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uungdjnj0t", "Integrity": "xmXweODkw/JvgklAaQKH2GLes5LUeFL2kNCSjrcjFSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\staticwebassets\\scripts\\sf-timepicker.min.js", "FileLength": 7454, "LastWriteTime": "2025-07-24T03:33:23.6526091+00:00"}, "SnPn2LYMCOGODRhONzHxcUaZZUG66a+pgqC+iwJt26E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\klmnspddx3-vn5vow5nyi.gz", "SourceId": "Syncfusion.Blazor.Grid", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Grid", "RelativePath": "scripts/sf-grid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\28.1.33\\staticwebassets\\scripts\\sf-grid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cmq9c6l1nc", "Integrity": "BOMbYBprBi40C7huVCDuRGzdLi9ypi5yzxUU5Hi0lyA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\28.1.33\\staticwebassets\\scripts\\sf-grid.min.js", "FileLength": 58754, "LastWriteTime": "2025-07-24T03:33:23.6625798+00:00"}, "u+t4VPD8g8eJwCjHjDStLln5Ebs8Tr3nPeeJi8Vk4JQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\naq8k6d23h-na82tswc18.gz", "SourceId": "Syncfusion.Blazor.TreeGrid", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.TreeGrid", "RelativePath": "scripts/sf-treegrid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.treegrid\\28.1.33\\staticwebassets\\scripts\\sf-treegrid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ni1slykhc2", "Integrity": "UYwkJYSo5WGTeR1u0+sSDqbA0TnfHmRsuxBkINHdMm0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.treegrid\\28.1.33\\staticwebassets\\scripts\\sf-treegrid.min.js", "FileLength": 7255, "LastWriteTime": "2025-07-24T03:33:23.6665697+00:00"}, "HILESO4z5kQinRis7eoE8C/GQPR1eRcznJCnIoHrZyY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\rece5i0o3b-o6ugt603on.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bds.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qu61oepmjx", "Integrity": "8vgVkyiiiDUhEmFeG+Y9mOdqazOfu34T52Pab8aZvw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds.css", "FileLength": 423206, "LastWriteTime": "2025-07-24T03:33:23.7712888+00:00"}, "OzBK+QX/Hlr9JY36UzfEWqFTKtJu+CObt6n4ZWer1ZU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ha92l7h15r-zefggmk0nt.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bds-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ulj9nerdfn", "Integrity": "ftXrT9rJW6CUMpGNeJCnVzcH9H483BzquObEHMyZXuU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds-dark.css", "FileLength": 424004, "LastWriteTime": "2025-07-24T03:33:23.9009421+00:00"}, "v+Lwq+pqgh8qkH/Q+QfQP65bWewtfxTJUMSgHICsSt4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\lv8nbelh2x-3v8h5lbbgb.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bds-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xsil1i1c1o", "Integrity": "j3TsezL6rY70+wMugoEyp7X2/DtSqGHxbnOpDtiVemg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds-dark-lite.css", "FileLength": 362261, "LastWriteTime": "2025-07-24T03:33:23.9916999+00:00"}, "+m2lRCKB7LYGixEz4WOHgn3EsQ4VfVYrq/OSEx1Gw5Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\8qaxuxlnbt-43cyh335js.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bds-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1blz1tbm3d", "Integrity": "Y0QC9jfMOZDx8izZt/xWRl9BXPuL+KVtobhziCeSKw4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bds-lite.css", "FileLength": 361615, "LastWriteTime": "2025-07-24T03:33:24.0556363+00:00"}, "qdzwO7apFmBng0Vgr7cCxvydDBbKPCsBB9u26NZwiCw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\g9w5tdrjvt-lk58hctl27.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iwj74qyfju", "Integrity": "gmxhXW2IR+Fpg+QSKbrPaGi6k+XZPP23O617TwF6NZI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap.css", "FileLength": 448867, "LastWriteTime": "2025-07-24T03:33:23.8201586+00:00"}, "OxBs4Gmwqj3XSpHZ2tREr+kPhmalhiadbZfzDjMDm58=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ev84ybuhfb-dt7m417fhx.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o7jyh1vi0m", "Integrity": "PKV9IiWDPYiDkwRIz8u8v+TSYekBKHjzBsT/rzbjHgs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap-dark.css", "FileLength": 448160, "LastWriteTime": "2025-07-24T03:33:23.9468202+00:00"}, "NXplciq5Uadz3FipW4y/1HrxPuD43yZGGcfsg2xDEHY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ll0ck2c3pw-a9z24x7hmu.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ax2stqjje", "Integrity": "AKVl42PT34ZLzxXcYMMdksav4llhtrjVaDEoyhlGN8E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap-dark-lite.css", "FileLength": 391921, "LastWriteTime": "2025-07-24T03:33:24.0132667+00:00"}, "oBKssYcZPTr0HSdYUa4oCGPwKuWa3yBZdmQyfy5f4BA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\m9pq0qobmp-tqsfpldjn8.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffxygss455", "Integrity": "RWILIgkwrQPG6/BgDbGBzepydPhwmzT5m/0nLLWY+Ok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap-lite.css", "FileLength": 392535, "LastWriteTime": "2025-07-24T03:33:24.0624481+00:00"}, "v6OJUBRg8sOGVi3RkP73uuactQoZ6a/jRBkJ8q6L/Kk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\g0kkhqlx3b-6lk4wmu5hz.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap4.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cgl1dze0be", "Integrity": "JGR3MAo3qdyWirSaHyklHrXoII55oQj1cHkur9RviGA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap4.css", "FileLength": 438633, "LastWriteTime": "2025-07-24T03:33:24.1066485+00:00"}, "3vezKg0YkOf8/WpEgsdzvoPB5oAwd/1xOTUuwbYwud0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\fx6f6w99v8-5q77kzjaeq.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap4-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w95fx690ba", "Integrity": "jA7t7IiYNQeJg3gVtvc3VaCaGKDJNGJkWz0k0Ji8PTs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap4-lite.css", "FileLength": 379064, "LastWriteTime": "2025-07-24T03:33:23.6964894+00:00"}, "TNGy/ptkt0bjnBC7rRpjWqudUGSgh//nT7sPeHhyZlI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\czad4b1t5w-9j9908pwo0.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6asc5u4pbl", "Integrity": "0ghIUuKT9zLhY3WtW+i2YXYJIWQAyS4+EFF6nTcJ9V8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.css", "FileLength": 441914, "LastWriteTime": "2025-07-24T03:33:23.8620459+00:00"}, "L7ijtAUaAkCbeOf76r/0CGpDPDtT660xYL4U9zt9Gks=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\w7wzav3zkk-cu27sdzqbe.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pwbr03bgp9", "Integrity": "CREq8vQeE3UKPBaxJxgOdcrmVP1lZrlnNudU04qQrzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5-dark.css", "FileLength": 442193, "LastWriteTime": "2025-07-24T03:33:23.9797315+00:00"}, "7zMz4+HSNsM/LGqaCA9+WmWQlCFZZg2MZuoyVD8RROc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ezik3was9u-6trzohy1t4.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3yio2k5t4a", "Integrity": "6xeugcbLOz5mtwozd37L5HcTiMnEOjN5JW0DxP5F1lw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5-dark-lite.css", "FileLength": 382508, "LastWriteTime": "2025-07-24T03:33:24.0405695+00:00"}, "X83b0VbsnzaeOew/W58YeJWyvRmNsu+X+jMjnHbNC1A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\0gmeqlood7-b2xd4q2sn2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "amr6kqh9yi", "Integrity": "rqKbQvm1FZrR3f58hN4+nRgTQYGNSaJvzOt33Ac7QXs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5-lite.css", "FileLength": 382289, "LastWriteTime": "2025-07-24T03:33:24.0864497+00:00"}, "8Vwkb1C3X8ZkTW5YRB+QckoYn+gJRCRtUfmevs2G52U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\rceg06wfiw-z8uq8fxwub.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mg8o8v0l6k", "Integrity": "Kpv2DkxoO+H8NayVRk8Yw3bgnwpu607QTuwPo+wZOkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3.css", "FileLength": 449174, "LastWriteTime": "2025-07-24T03:33:24.1356249+00:00"}, "WUfD35DO4Z0+AtW3/d6CviOIYFbwAPx5lRycrboENWE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\5q2prff4m2-n0zb8p1hih.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ou8qcskpre", "Integrity": "UKtE8ozIhVMdowdb1UDC0sxrah2F2NuEFColjOM+pgk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3-dark.css", "FileLength": 448295, "LastWriteTime": "2025-07-24T03:33:24.2111131+00:00"}, "P5kJMCUa85gsgx/DbIJ+0mYn4/sPfQkW63yIjJ3nlq0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\awwokdaaq0-zr0y78wzde.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2q9n5vjoku", "Integrity": "+sHa4h4K1xRVDHeN1a5FZo1AoylXAOTK2RwvGzmLX9Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3-dark-lite.css", "FileLength": 388066, "LastWriteTime": "2025-07-24T03:33:24.3327876+00:00"}, "qa6flPht3yZwJ6l4R87usX6QyHXVmtGTguKzSIu+kD0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\984b865z1b-na72ep61u7.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jiuch5tmj2", "Integrity": "e1oLgYZemVtPo/0/ObHFSojWnR5coZFgHPIelYb7dBs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\bootstrap5.3-lite.css", "FileLength": 388840, "LastWriteTime": "2025-07-24T03:33:24.4105806+00:00"}, "kkvJfYHhe+yLXBNonDbzSWhot6/njn9hv01dMyXY37g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ytu8lus5q2-4lnxs905cb.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uikknbn2k9", "Integrity": "GujP/WV5nme3wZIv56SUvMCU0FPrx3BbCcOmqjyOZoA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\material.css", "FileLength": 476669, "LastWriteTime": "2025-07-24T03:33:24.4744103+00:00"}, "xRz9iy1KGnO9cbwR1kK1+tE08O1mhyK4gpNcvwdj8LM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\mpbuet7hpe-q7wntmxvcd.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qq0kuuvuxv", "Integrity": "PjlGwApWgUeH+3NN7WlG8fxPV5b4lg9joxQmBewoHOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\material-dark.css", "FileLength": 484001, "LastWriteTime": "2025-07-24T03:33:24.5462172+00:00"}, "w+/L1LZmaC8iBVb2DFD3oJ4D4mqXHMd/kd9ZzSbBzb4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\pxx57cfmdu-u9s3nwaj6a.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iylo2bbhgy", "Integrity": "KsPZobht7EVu9sa1hP28Bme719GkdQdaPdd2bKfiPSo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\tailwind.css", "FileLength": 409544, "LastWriteTime": "2025-07-24T03:33:24.6010988+00:00"}, "Af7Kfk7KCso4nRQmjKPTwQxCb27wLHVbeDLQb0Zq/9o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\yu10ux9zg2-o31w52ni3h.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "647z9yz6m7", "Integrity": "xi+QfYcZOhhUxnVB9Ly+Pv9YTX0JHeazkykW12DPMyA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\customized\\tailwind-dark.css", "FileLength": 411475, "LastWriteTime": "2025-07-24T03:33:24.6479448+00:00"}, "i2VKl/K0IM1MDcmX45q7GKdkaj5RAH072sXayDLAu3s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\xj9e5awasb-m8g935ynqp.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x5gz9mtaxn", "Integrity": "lU1Kd52bDbsIC0iNse6XTSdrRP4XqXzFpbKzAQghJUg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric.css", "FileLength": 426650, "LastWriteTime": "2025-07-24T03:33:24.692826+00:00"}, "67zogt7M+luNs9DNyCm6DoisrJrXrjPAJHUR8X/NqC0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\f1vaqyeoju-ie90u7x7hb.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vv8uq0cb6", "Integrity": "tCbGWTYJiA3f2cNlX9Qr+Ah30QqoadCibztUxXh67sA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric-dark.css", "FileLength": 437207, "LastWriteTime": "2025-07-24T03:33:24.7450004+00:00"}, "jw/6DhqPqcS7yauYRdtihJrYmr1dhVd7UOZc/1RRJVM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\8fhnih39yd-mnmgnsumqh.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iq6vpjj8rx", "Integrity": "2kiga8Udq0UzZ7/Fh2PhkEzf3avY0WVTjJY6FqgVMww=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric-dark-lite.css", "FileLength": 380037, "LastWriteTime": "2025-07-24T03:33:24.7835834+00:00"}, "L4bW4OTFhXg1sclMa3Vck1FkUskCBXFOI9fMwVbDdPU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\aqas0gzsh0-zha4j1pf2h.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkko9kgwnh", "Integrity": "LfdAuFsK3MxNwKiYuw4Y0WdLA/KRsQuD0vqgM8kXVeg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fabric-lite.css", "FileLength": 369912, "LastWriteTime": "2025-07-24T03:33:24.8254709+00:00"}, "2O/c8JProyxLFGUx9z/gOoKTk3uMXor1rkueTRt4Hfg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\r5ssla993m-thdws2uew5.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vi9bhzxlb3", "Integrity": "CY6MAbmtLdTIPjz/OPHXlsA9zYNKDg/FtRdcFmdEq2g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent.css", "FileLength": 425370, "LastWriteTime": "2025-07-24T03:33:23.8171664+00:00"}, "G/jPvgcLS4Bw3Yj2NAq9hdn/kiWYMRdeN0SehPF0eJQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\yqbsc2e4c2-b77w3r5k70.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2mo8nvyxz", "Integrity": "vLknNgY4XfVLJ35ukMLZ/23XVeKtTXo1g7Suow+WdGI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent-dark.css", "FileLength": 425822, "LastWriteTime": "2025-07-24T03:33:23.9388436+00:00"}, "/TRxbUzjKu4BHaW3ukayYCbx+GwWbE+bcTQPcHjSBBE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\2dszv1c0dc-xgjknv1f9l.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7p8cs06hkj", "Integrity": "C/MymC4uZ7hLug5BXmMtJ+rJHTGL2QTSBkP5PZd5d8c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent-dark-lite.css", "FileLength": 364650, "LastWriteTime": "2025-07-24T03:33:23.8909687+00:00"}, "2PDsia5VDrSuit3BmD41eQ8F434EUJpeAaoSrNzxby4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ubxng02trl-34q15ejefc.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "djx6<PERSON><PERSON><PERSON>", "Integrity": "SWeHStrc315bUEB+YkJC0TJNFnYiji9WabPyZbAfFB8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent-lite.css", "FileLength": 364211, "LastWriteTime": "2025-07-24T03:33:23.6615822+00:00"}, "yR8xBH7LKTpz1G1xnf22v4BRQ8cCv+4hUCHx6CZmKq0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\h56puxhs03-k4kbjg40vd.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tlsa5dn8s2", "Integrity": "r2fN2t94cuy9X2XqTzgSTg8ik12zIsUVq7rTzssEA44=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2.css", "FileLength": 459713, "LastWriteTime": "2025-07-24T03:33:23.7872485+00:00"}, "AVGW8kIpKVHz4sM6SabWqVBz7Dwso2h8OzX4ia25UW8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\oaindoncb5-5erjzbea3s.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vtyvkrlw60", "Integrity": "E/JQwzUFGYQ/RbLBrwJEDPSIzF7f2RlxXd3Hkp6aroA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-dark.css", "FileLength": 456599, "LastWriteTime": "2025-07-24T03:33:23.8979501+00:00"}, "M2i8TzKGPFnomEcuOH8O74JYBFgwCf0ZPYZdSVErAOI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\xhfo9ezgkd-b9f5gwl3w0.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zq02in53hw", "Integrity": "tXFFRinA+XrqOuDDvuqkNQSijzk4P2QN2ZpLU7fiq9E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-dark-lite.css", "FileLength": 386177, "LastWriteTime": "2025-07-24T03:33:23.9608897+00:00"}, "aiss0gjtiHVPjylb3hVS4f4YilDdLZwN2Ryal97hK/M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\1e4urs263g-snxb7syil9.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "szkj8l816d", "Integrity": "+NI0xgb27O3TANfWD5AWteTaXJDWeb5f8G+cEFb6h1o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-highcontrast.css", "FileLength": 458106, "LastWriteTime": "2025-07-24T03:33:24.0495451+00:00"}, "WTzrYZdp61CUJ0z/EjyI12QsFyVRbUutA14OWAEeYgc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\4qsv9tgae5-e9tk8e1qh7.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s1pag9xoks", "Integrity": "sH7Txr7V5oGcz2yg5H/qYo5bhrfU2cqfzagS41vjMWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-highcontrast-lite.css", "FileLength": 387609, "LastWriteTime": "2025-07-24T03:33:24.1014143+00:00"}, "llEtUXIYgk6QBnXFQIrBidebPBgPk6TrqPFBgII2SH0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\lf21o06s6y-8uxq70ddc0.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6qy8bmsru4", "Integrity": "MhtBpnKsJ5qW2iJzbiyqswAUGl/DRhg3kjIfesD9HAM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\fluent2-lite.css", "FileLength": 388936, "LastWriteTime": "2025-07-24T03:33:24.1532726+00:00"}, "b9Xe3jpImhTgaVoLq/ZB01JvVHMaB0+qmDtTNsowdZI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\8u73xo5u52-6gxsm3xos7.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "efxa0i0wow", "Integrity": "uk/3jqJ9y4co3ExMQLNxWW1lwxo7j9nYq412ZUzKD24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\highcontrast.css", "FileLength": 425092, "LastWriteTime": "2025-07-24T03:33:24.2290651+00:00"}, "Yi86Xods+/J415tYICyM/N+wKeEqqXHhkVVF+BiCbJM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sa1475ed2m-azv0ito82g.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7a2nhnzw2t", "Integrity": "Twh2c+S02hRhbtRAV2GJ4csA5PTcfOZ4Y3hq87nUmfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\highcontrast-lite.css", "FileLength": 368457, "LastWriteTime": "2025-07-24T03:33:24.3418481+00:00"}, "CbDbi8H+abxENUKDrvLnqViWAlba5xjTuyXIuz1Kobw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\w0orhpiaxo-qvr5wdla8y.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zta80hvrsj", "Integrity": "KYN5JYKhxRWQDGB/OrwuUkWAWshwFFR0CRg4KR5YEm4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material.css", "FileLength": 476684, "LastWriteTime": "2025-07-24T03:33:24.4115769+00:00"}, "5RSGhfuf6eoLmrSfI/XSxK5C3knZD/LJqI65mHjVuv8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\4ulei2vwud-t05930eolh.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dlm03b1i1n", "Integrity": "YfEOPz4nyJk4O2zKYoqwY38x4UjotamakYVI1olSo5E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material-dark.css", "FileLength": 484030, "LastWriteTime": "2025-07-24T03:33:24.478399+00:00"}, "bB9fiCiRc3CDZGE6ipJVxZ+p+B7qTu7kVh6GeXP2FI4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ay4l9je90m-u0axjgxzv6.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mh6kzyoo9", "Integrity": "sL6rWtsBU4sBN+0KRXV99CdQtfHb8qG2wJrrjw/noE0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material-dark-lite.css", "FileLength": 410819, "LastWriteTime": "2025-07-24T03:33:24.537308+00:00"}, "rhF/DLsPyWQ+4s1S5DAlND4J29V8jC0qgI7z9SOKhj4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\fsp2o9negp-vdawn4qwmg.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jftpb25yzn", "Integrity": "wZtv93PQJNOztcFrf0E3NDKj2CcCvfCozCOGDAkyfI4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material-lite.css", "FileLength": 402890, "LastWriteTime": "2025-07-24T03:33:24.5940889+00:00"}, "gW+8eOVR4F0pRyATXG3YXM7EfMM+/4PwxKun4l6saVs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\s2yf70i8pu-zvrr5x1kt8.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ymn3bor1sd", "Integrity": "liqvKIICL9DEjNq4Msbtu6AKo5PYlM5NesccfcM2yfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3.css", "FileLength": 428305, "LastWriteTime": "2025-07-24T03:33:24.649943+00:00"}, "0r722tpU+l3bcSjIeBMKSR8P4exTb8lQ00t7S4B8T/g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\wa7m2rhtxr-hncarjbpmk.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ksq7ngt44c", "Integrity": "ZArfuklWUHAUD6fFOv9x2ivSiaas7TfwfAPvcyYqDrk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3-dark.css", "FileLength": 427913, "LastWriteTime": "2025-07-24T03:33:24.7037955+00:00"}, "yfQallAmtERyQUVJRQpg529XGPBB9O5QWLTO9AyZfZo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\yjifzcnj40-xfoh8quy7d.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x2txw73yx3", "Integrity": "eV1VVrl0AkAWo9d1ogh1PTvAlCuTiW3onuGLLgRSCrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3-dark-lite.css", "FileLength": 350258, "LastWriteTime": "2025-07-24T03:33:24.7426928+00:00"}, "b1V8q5TK8rMjEc9AA/YUxp9jiLUcFYMz8GnTYFWBZug=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\wkxfmls346-4hue68ryjm.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zmga7c5x5h", "Integrity": "uxlV48LNydc6UchR36F2E4HiNfRfWY4jwPVqvQwL9bI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\material3-lite.css", "FileLength": 350718, "LastWriteTime": "2025-07-24T03:33:24.7805906+00:00"}, "s8PYVP4z/apL+asnxfGYQbMmAcblVrmeQ+Xs1M4vogo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\h5th63hdh4-q95uvqwjwc.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zc2htlevjp", "Integrity": "M3f2vEKe7rEuPdYjIsRrr5OwaVVOQxmn9F4PY4mFouE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind.css", "FileLength": 409628, "LastWriteTime": "2025-07-24T03:33:24.8354443+00:00"}, "qY96v5gzCne4M7nBPAhkKfNfk7lZNcIUoD2VLMf2gxo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sxqxspocya-1iu0y7n4nf.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wmh94lyj3c", "Integrity": "eGbBrTCTA6fAYJTQAaqV4gXCFgKOqEj3V19ICEB+iqY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind-dark.css", "FileLength": 411551, "LastWriteTime": "2025-07-24T03:33:24.8863112+00:00"}, "6yZsqU18Ko6nfwCZ9P4qSFBRbrE7sM4nQS+FXboCXHQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\rd6io8kvbb-x5c6cvdjq5.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ojwfrk4kl8", "Integrity": "fKJNPIrKUxa604yossctViJrCO551KxCuXr2nfT8YhA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind-dark-lite.css", "FileLength": 350277, "LastWriteTime": "2025-07-24T03:33:23.9837211+00:00"}, "qetZ2iNIsR21eNr+bEMUiDGh66nsCLmSwoKsI4QLzBU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\g3fd5cikf4-lgsuv00uwy.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9whewhx383", "Integrity": "GSYjDVc+sJoTUtOI7TUfn58HOA6NAs6Gz39faEbPno8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind-lite.css", "FileLength": 348298, "LastWriteTime": "2025-07-24T03:33:23.683532+00:00"}, "nICUddkMgg8uDonQ9z9VMLMcLdSxcFx2vIOjGlj3dyY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\k08v6vvvwj-z5g4pzkvem.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2brmjr8kpz", "Integrity": "vvLwh8oscf5qbBOAKo40fIlEe4mAxHlnuiH/2P0wJDs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3.css", "FileLength": 468232, "LastWriteTime": "2025-07-24T03:33:23.7792672+00:00"}, "OJfxwAonygk6ApSU5/6OdbtRk5MNTQRdWGpQVfTch3U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\npcbabgmoa-06sbibpvtm.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zbvs25ubk5", "Integrity": "n6NFZeuVy+R7P6KNupG0qeamiLjG0746i9Bq2U7c794=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3-dark.css", "FileLength": 465915, "LastWriteTime": "2025-07-24T03:33:23.8859838+00:00"}, "JfrkUW88aLBRX6zwXY9nx1WM28vYgHTwVrA0rhBhIEA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\4cc4cl8qb1-skljnngt8b.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8p1brwoxx3", "Integrity": "XBidPaLitr9r0nZyxYuYh9QXRf3IYKPMwIxvxMAaxbI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3-dark-lite.css", "FileLength": 395517, "LastWriteTime": "2025-07-24T03:33:23.9657694+00:00"}, "Bs3wS/vBByCTIWyQiJcBcf1w/QGbtR5VLdDMDWZDI90=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\3itjbw64bu-cwkhrrymgv.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kv4b7dp2ke", "Integrity": "cLgmOdmW1qMBZ5Wq5XhKXqExwJQi2g8BSCwuH/FGlhw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\staticwebassets\\tailwind3-lite.css", "FileLength": 397570, "LastWriteTime": "2025-07-24T03:33:24.0206224+00:00"}, "0+695KLC967jFusUfqBAjSVwT3ePyZMN77hnqFP9OKI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\1wpo26mjip-cth2wrxc13.gz", "SourceId": "Syncfusion.Blazor.Schedule", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Schedule", "RelativePath": "scripts/sf-schedule.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.schedule\\28.1.33\\staticwebassets\\scripts\\sf-schedule.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a53yq5nn7z", "Integrity": "bgINXfg4tPZhGttYxKrW/lQrq83OimW3VEP10b1GPAE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.schedule\\28.1.33\\staticwebassets\\scripts\\sf-schedule.min.js", "FileLength": 48258, "LastWriteTime": "2025-07-24T03:33:24.0286008+00:00"}, "o1kbU69Yy3Jmrp1cDFYCX9g0KaEFWNPbDLpX+feAHm8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\x6wjrj69gv-vnlhfx3to4.gz", "SourceId": "Syncfusion.Blazor.RichTextEditor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.RichTextEditor", "RelativePath": "scripts/sf-richtexteditor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.richtexteditor\\28.1.33\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9x5m9pqhby", "Integrity": "fVR9/J7sw6d6M+ZNMjlVomxp5psKFS+IQ3mYLzf2Mus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.richtexteditor\\28.1.33\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "FileLength": 152043, "LastWriteTime": "2025-07-24T03:33:24.0556363+00:00"}, "yTGwZFyrg4ajLqaYjttwkpo1b+twXHjazzpOE+C4QeI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\7fo5fkl0dh-zchb7mna6v.gz", "SourceId": "Syncfusion.Blazor.ProgressBar", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.ProgressBar", "RelativePath": "scripts/sf-progressbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.progressbar\\28.1.33\\staticwebassets\\scripts\\sf-progressbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tw6bzh1ufk", "Integrity": "Q5HIDZ5iA9LvDGkB7Bqd8rNO1lAxBMLK2YkNPSFwn3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.progressbar\\28.1.33\\staticwebassets\\scripts\\sf-progressbar.min.js", "FileLength": 3641, "LastWriteTime": "2025-07-24T03:33:23.9119137+00:00"}, "PgGxbWbY1sJ2+5+3f4/4PPZyP73yOWafqdAKnLK1ekI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\f1df7vucqv-i1vljdho4f.gz", "SourceId": "Syncfusion.Blazor.Layouts", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Layouts", "RelativePath": "scripts/sf-dashboard-layout.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.layouts\\28.1.33\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpm4ncylut", "Integrity": "79Jtevn4gBIspsqLMsQMJKI5i+DJwP1pIPB5BjV/o/I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.layouts\\28.1.33\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "FileLength": 10076, "LastWriteTime": "2025-07-24T03:33:23.9153259+00:00"}, "Ua5f0t9zOr6pRCnFOTx21G20EjCxBSdW4Oy5zXGAKYw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\fnyfq6bvm1-cm81czm0c6.gz", "SourceId": "Syncfusion.Blazor.Layouts", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Layouts", "RelativePath": "scripts/sf-splitter.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.layouts\\28.1.33\\staticwebassets\\scripts\\sf-splitter.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtyphb9z9r", "Integrity": "eXYF6BMaqqLdEiPlKmJY82JuaLD1jX3xndHKwCWZA3M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.layouts\\28.1.33\\staticwebassets\\scripts\\sf-splitter.min.js", "FileLength": 9266, "LastWriteTime": "2025-07-24T03:33:23.9191087+00:00"}, "EuU75Nsezm6FJsf7qAj5zau+0TFho+JAIb0yMK2Dxg8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\b5cgi8pe23-1o9ei7qovm.gz", "SourceId": "Syncfusion.Blazor.Diagram", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Diagram", "RelativePath": "scripts/sf-diagramcomponent.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.diagram\\28.1.33\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d19n4l5ael", "Integrity": "gVXC2JUIG0of+jaPXKwHrpIh9YCOG3gSxp7D7m7f78o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.diagram\\28.1.33\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "FileLength": 16215, "LastWriteTime": "2025-07-24T03:33:23.9408357+00:00"}, "KqNuitTt76RV8sjqZoMy5+hTiS9MqknwOxMlXHb2AnY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\l38986f9ca-8r21q8eplu.gz", "SourceId": "Syncfusion.Blazor.Charts", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Charts", "RelativePath": "scripts/sf-accumulation-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.charts\\28.1.33\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nybhvqiryv", "Integrity": "z135K0WhcQMStnn05LHDJg7ZgJ61S9zljBAbm6KAYoo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.charts\\28.1.33\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "FileLength": 5276, "LastWriteTime": "2025-07-24T03:33:23.9328574+00:00"}, "CX+sPAzQPkfzg+I1QAJVYz+EWnky9XFp8zwqnBx+h+Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\5wmhaphab1-ua9g71wjvr.gz", "SourceId": "Syncfusion.Blazor.Charts", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Charts", "RelativePath": "scripts/sf-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.charts\\28.1.33\\staticwebassets\\scripts\\sf-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "moqvgbktas", "Integrity": "1jQqw0dyNkhj20mr0zLa94X9MuDipwPjdNSEHHu3ZFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.charts\\28.1.33\\staticwebassets\\scripts\\sf-chart.min.js", "FileLength": 49376, "LastWriteTime": "2025-07-24T03:33:23.9438334+00:00"}, "cDvaSTAvvJvDV0twkM9vzlznWnnPZ9Yd4JFYoCMcefo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\k1bx7x8zjr-khy4lop6wu.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "app#[.{fingerprint=khy4lop6wu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "596e5mu3rg", "Integrity": "KfTtGgOr4gFqWP6bDC3erwKnY0fchsCFGM8SFQeyGO0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\app.css", "FileLength": 1571, "LastWriteTime": "2025-07-24T03:33:23.9458237+00:00"}, "L7SBZvwO6+4EERkle9535KIZfgtQf6ZIR2rwRu9WcGE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\b5tbbjbk3x-bqjiyaj88i.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-24T03:33:23.9557962+00:00"}, "Kfnp0YWoKkpWPhnwoW/E9Z4hVpCbyWE8rGy2eN4/iHA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sfe6nkll3q-c2jlpeoesf.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-24T03:33:23.9617801+00:00"}, "Scwf0LNqXwOWW4iP5FzPoNl2Ir7GbMQRTWSmAL60CPg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\g9toa6ycsm-erw9l3u2r3.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-24T03:33:23.986713+00:00"}, "RdOzK0Jpal43Rqj0vwJwK8TjC3ilXsZl6l9+XcCtZ/c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\7z9534cqx6-aexeepp0ev.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-24T03:33:23.9928421+00:00"}, "O1yvqQe1m39hzqzSf6qQoB+rV7+3mpy6C3W837V+vYk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\5bhekyrpny-d7shbmvgxk.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-24T03:33:23.9956891+00:00"}, "xLf+DjmhVYBAmjGW2Z/Dkum8e6us4yKCZFkJeLTwuc0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\vobqgdxxiz-ausgxo2sd3.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-24T03:33:24.000719+00:00"}, "AKIysNXQrbObpxauv27TTgm0PZWOTCrj7lezcb+4nes=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\5he5g9xhq6-k8d9w2qqmf.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-24T03:33:23.5947612+00:00"}, "l7F1KNiUuCBRUB8XJg4HK4rLbW5yhKOCURGsIti/ks4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\snryx66u9l-cosvhxvwiu.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-24T03:33:23.6007446+00:00"}, "3ETurXJp+vGvidHwxAwwgNoJzpMJ9lT9cG2bbKIPCyk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\jy7knpvwc3-ub07r2b239.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-24T03:33:23.6087242+00:00"}, "xX/dtqbl1eR60TM8+9Ti579FRQGfcVsHoPBeSQPuX1E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\xrys6v0rnk-fvhpjtyr6v.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-24T03:33:23.6168055+00:00"}, "YI3llOXMJnXdLAiwGZg2HvuY/A+xGs9g+hVDFY+TGuA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\oae00gm7wy-b7pk76d08c.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-24T03:33:23.6236837+00:00"}, "8cJWBUkGYtFYSj+N6iMjVEVBxW/5PAcBPi1T86UWD1M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\8p6g9dbwpw-fsbi9cje9m.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-24T03:33:23.6296677+00:00"}, "Q11Bj1XoDkHTvaShRmkujYVOuZA8b4TqsMOaYGCMqwg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\vwk20p5hd6-rzd6atqjts.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-24T03:33:23.6356525+00:00"}, "UFjFVf5oQDEhVU5UN5qmuSLo4cWN1ZuxCie+vOqHhzM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\52qks6c2sy-ee0r1s7dh0.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-24T03:33:23.6436308+00:00"}, "OQFVuxkB5Z2yrmMXOasB9O8wTJjylQ/+BPavXAEZwNo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\x66inki7jc-dxx9fxp4il.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-24T03:33:23.6486171+00:00"}, "zkOViUv02G+One0CiW+m8Kdib4y6NZhPQ/I6P5wwt+s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\2df46fpq4d-jd9uben2k1.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-24T03:33:23.6526091+00:00"}, "jq0H8rcSibzAH+7HSpu8Jud3tbRgQ8Ma332SurElje8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\6k07w5kpzu-khv3u5hwcm.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-24T03:33:23.6567692+00:00"}, "anTCZJb3NwN1QS61Kp74FisYwXlOXYliflsZM/ZIjf8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\hw1ofkf049-r4e9w2rdcm.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-24T03:33:23.6845221+00:00"}, "TKUybwi9NAq2fI2G80T75iipVAEn5BrklXPUOXsQ3bk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\dybu093gtq-lcd1t2u6c8.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-24T03:33:23.6886298+00:00"}, "1apzXyOxnoM90dPmLZcs2F7oFgnKt8HR+Nm2MEoQSjc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ksuf4w1of5-c2oey78nd0.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-24T03:33:23.6944945+00:00"}, "4pAY2QH9BW5hdCtBnvcWSxBhv2x/fyEG5fgfVzHo0dI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\i0mi9re74c-tdbxkamptv.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-24T03:33:23.6994807+00:00"}, "xW2CBHPi2UgPRCdDt5XzgGEn1d1pcpoPJrAS33gUSRo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\vby8so27x2-j5mq2jizvt.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-24T03:33:23.7094543+00:00"}, "fZ+u2EZKHvquo8p3Sf8wdBUfVBz5GTVtlWNTUed6MOI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\fk08p6inpj-06098lyss8.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-24T03:33:23.7134434+00:00"}, "PWK5kt5D508CNOwS/Bt19XyMFH8mHthSHZ6nypzZ+y8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\94qil4y4ag-nvvlpmu67g.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-24T03:33:23.718606+00:00"}, "mh7lXg/0rwwOVksujiqkQpPd9tvIqm5E7MA8CzA4rwo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\i4iqhka4mm-s35ty4nyc5.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-24T03:33:23.7254444+00:00"}, "1zXtB3K9lUEUlLbfs4DjZhGgPCeFsHiSTlkCJwP+Cdw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ga9f1ak8cu-pj5nd1wqec.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-24T03:33:23.7433648+00:00"}, "JGF+sbxaq0pC8NXVYeZQR+mjioS9cL6MGzXdHqOcMfg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\lcs6nbgqa4-46ein0sx1k.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-24T03:33:23.6007446+00:00"}, "by+jVGNw7JmqcKpQ0B9cAz3u2p/l3OfwJJfBTraeCwY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\56j4bjn0bs-v0zj4ognzu.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-24T03:33:23.6246812+00:00"}, "vzqabFPxKY75zmLO6vek8eHJjIJ4B2adhO1NdHj7ii8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\vhp347yxtr-37tfw0ft22.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-24T03:33:23.6366498+00:00"}, "V+PdUs/0T43ACQ9GSndPkytAZpR3g0AwV6hpNYH+oNE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\2ieqhaco6i-hrwsygsryq.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-24T03:33:23.6585927+00:00"}, "raX/31g17wfBYTTODsenGvWdw4RfsaQ4vDK5aaKECT8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sn6jm6w28c-pk9g2wxc8p.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-24T03:33:23.6964894+00:00"}, "Wz1lh3hfDeZy+mJmMtK2BnUcem8vbbqk2FrnTOKCcTI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\qdjpk9bont-ft3s53vfgj.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-24T03:33:23.7134434+00:00"}, "SHK5BizqymzNV2cUDuttr1FTilg6pQixou2SEYXOQK4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\l5pwizo8ir-6cfz1n2cew.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-24T03:33:23.7214227+00:00"}, "sPczEDpszXUaFK97hb9aWHyJRp95Uz1HD6IWy8XHeRs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\lcdaucgab6-6pdc2jztkx.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-24T03:33:23.7593607+00:00"}, "nbjP8/DVg59W8cMdToiZPqUDWG8+WTqSj3Ln3e2t0SM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\sfmcp9tmx9-493y06b0oq.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-24T03:33:23.8121802+00:00"}, "0Q2zBTzsqMK7uN3odUBUNqseO2tduCtOvsTHQbxkGIg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\cwtj62lvzv-iovd86k7lj.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-24T03:33:23.8381108+00:00"}, "Y4840CAD0vOMT6Aficc+mH4wromQkidL7L2X/XvaC0g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\a2fhtyhqoo-vr1egmr9el.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-24T03:33:23.8670338+00:00"}, "cGs3rbA2tH4FWI0pfb/gX/45LcaQVWMHKXT5bKuhX84=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\lrmdsslcn5-kbrnm935zg.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-24T03:33:23.8783904+00:00"}, "KYtAuPrmFwHO+TFBYOVNrFkXcoRcSdXX/vkHpXA/Dao=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\8t0oinh1qn-jj8uyg4cgr.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-24T03:33:23.8969531+00:00"}, "DSvNVK6mEdYwNpJZKZjASJFJ7s+jgYkDbnw1NSC08I8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\ofwyqg1eqh-y7v9cxd14o.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-24T03:33:23.905929+00:00"}, "M/qhNQwGdx7PtcF5lU5V7BWqSIRf698B0z2khFyG8Zc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\nzurnd3gd4-notf2xhcfb.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-24T03:33:23.9298655+00:00"}, "3g7974mezK3A/DGAVrfgj3pvNKiXbP6sC6PvBXEkt/g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\5bb49q9qm4-h1s4sie4z3.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-24T03:33:23.7932314+00:00"}, "tAqwKl0IocwT+6DackwMZtcPGYkxRM9+MiJi6nakj4o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\xiswmmvh2e-63fj8s7r0e.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-24T03:33:23.8281376+00:00"}, "+fp2atGiVa5cYJ+rnic44U70lkGOW0TaiV6FUQsePrc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\d5fymgvmqo-0j3bgjxly4.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-24T03:33:23.8640404+00:00"}, "omqDfPhJg4RbEuNaBYLBhB5titgG6sL0hgp0xpal2BI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\x92e3fnsmi-gssrxao95f.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "CarSystemsWebApp#[.{fingerprint=gssrxao95f}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CarSystemsWebApp.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h9izgdfnrm", "Integrity": "NVuq7S6aPX18jgpPCJ4075+Azen62/UQbBhTJEbf8D8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CarSystemsWebApp.bundle.scp.css", "FileLength": 1956, "LastWriteTime": "2025-07-24T23:57:02.0027683+00:00"}, "jCzqaHESqnoywshag+5SDf/mOd7KfKyDNlUB0+2qH9k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\rp0bupjl7n-gssrxao95f.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "CarSystemsWebApp#[.{fingerprint=gssrxao95f}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CarSystemsWebApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h9izgdfnrm", "Integrity": "NVuq7S6aPX18jgpPCJ4075+Azen62/UQbBhTJEbf8D8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CarSystemsWebApp.styles.css", "FileLength": 1956, "LastWriteTime": "2025-07-24T23:57:02.0012577+00:00"}, "T1KLZ65t8fiYsZL5wo1AGwBsd3zWAHCiuN31lvjeu+o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\6r29g0moq6-rnbh6v1352.gz", "SourceId": "CarSystemsWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CarSystemsWebApp", "RelativePath": "css/filters#[.{fingerprint=rnbh6v1352}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\css\\filters.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ijpaudbly1", "Integrity": "Mj4Wz60WYW7gRAN5lbNByvu3Uwend9t/6jzTnS8/rvM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\wwwroot\\css\\filters.css", "FileLength": 1187, "LastWriteTime": "2025-07-27T22:41:02.2377045+00:00"}}, "CachedCopyCandidates": {}}