﻿using System;
using System.Collections.Generic;

namespace CarSystemsWebApp.Models;

public partial class CS_ECR
{
    public string code_societe { get; set; }

    public string num_ex { get; set; }

    public string code_jrn { get; set; }

    public string num_ecr { get; set; }

    public string numlig { get; set; }

    public string numlig_let { get; set; }

    public string numlig_tva { get; set; }

    public string code_etablissement { get; set; }

    public string datecompta { get; set; }

    public string type { get; set; }

    public string etat { get; set; }

    public string typpiece { get; set; }

    public string numpiece { get; set; }

    public string datepiece { get; set; }

    public string num_compte_gen { get; set; }

    public string num_compte_aux { get; set; }

    public string libelle { get; set; }

    public string debit { get; set; }

    public string credit { get; set; }

    public string dateech { get; set; }

    public string code_reg { get; set; }

    public string reflettrage { get; set; }

    public string let { get; set; }

    public string letpartiel { get; set; }

    public string datelettrage { get; set; }

    public string letsuivant { get; set; }

    public string pointe { get; set; }

    public string code_tva { get; set; }

    public string typassutva { get; set; }

    public string litige { get; set; }

    public string central { get; set; }

    public string annul { get; set; }

    public string transfer { get; set; }

    public string dateheuretransfer { get; set; }

    public string code_parc { get; set; }

    public string code_prest { get; set; }

    public string code_agence { get; set; }

    public string datecrea { get; set; }

    public string datemodi { get; set; }

    public string code_utilis_crea { get; set; }

    public string code_utilis_modi { get; set; }

    public string zalp1 { get; set; }

    public string zalp2 { get; set; }

    public string zalp3 { get; set; }

    public string zdat1 { get; set; }

    public string zdat2 { get; set; }

    public string zdat3 { get; set; }

    public string znum1 { get; set; }

    public string znum2 { get; set; }

    public string znum3 { get; set; }

    public string zonecle { get; set; }

    public string saisie { get; set; }

    public string debiteuro { get; set; }

    public string crediteuro { get; set; }

    public string debitpays { get; set; }

    public string creditpays { get; set; }

    public string deviserefeuro { get; set; }

    public string moistva { get; set; }

    public string datevaleur { get; set; }

    public string num_reg { get; set; }

    public string contrepartie { get; set; }

    public string moyenpaie { get; set; }

    public string numfacfisc { get; set; }

    public string extractDW { get; set; }

    public string z40alp1 { get; set; }

    public string z40alp2 { get; set; }

    public string z40alp3 { get; set; }

    public string z40alp4 { get; set; }

    public string z40alp5 { get; set; }

    public DateTime? datecompta2 { get; set; }
}
