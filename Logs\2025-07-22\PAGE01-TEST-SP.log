22/07/2025 : 11:26:01.843 : Testing stored procedure directly...
22/07/2025 : 11:26:01.844 : Database connection opened successfully
22/07/2025 : 11:26:02.362 : Stored procedure returned 19 columns:
22/07/2025 : 11:26:02.362 :   0: UniqueID (Int64)
22/07/2025 : 11:26:02.363 :   1: codesociete (String)
22/07/2025 : 11:26:02.363 :   2: typefac (String)
22/07/2025 : 11:26:02.364 :   3: numfac (String)
22/07/2025 : 11:26:02.364 :   4: facavoir (String)
22/07/2025 : 11:26:02.364 :   5: facturation (String)
22/07/2025 : 11:26:02.365 :   6: libelle (String)
22/07/2025 : 11:26:02.365 :   7: montant (String)
22/07/2025 : 11:26:02.366 :   8: datefac (String)
22/07/2025 : 11:26:02.366 :   9: codeparc (String)
22/07/2025 : 11:26:02.367 :   10: integre (String)
22/07/2025 : 11:26:02.367 :   11: etat (String)
22/07/2025 : 11:26:02.368 :   12: codeprest (String)
22/07/2025 : 11:26:02.368 :   13: codetiers (String)
22/07/2025 : 11:26:02.368 :   14: tottvaprestht (String)
22/07/2025 : 11:26:02.369 :   15: typetva (String)
22/07/2025 : 11:26:02.370 :   16: codeutiliscrea (String)
22/07/2025 : 11:26:02.370 :   17: codeutilismodi (String)
22/07/2025 : 11:26:02.370 :   18: datecrea (String)
22/07/2025 : 11:26:02.371 : Column containing 'datefac' present: True
22/07/2025 : 11:26:02.371 : Row 1 sample data:
22/07/2025 : 11:26:02.374 :   UniqueID: 8
22/07/2025 : 11:26:02.374 :   codesociete: 01
22/07/2025 : 11:26:02.374 :   typefac: ENT
22/07/2025 : 11:26:02.374 :   numfac: '
22/07/2025 : 11:26:02.375 :   facavoir: F
22/07/2025 : 11:26:02.375 : Row 2 sample data:
22/07/2025 : 11:26:02.375 :   UniqueID: 8
22/07/2025 : 11:26:02.376 :   codesociete: 01
22/07/2025 : 11:26:02.376 :   typefac: ENT
22/07/2025 : 11:26:02.376 :   numfac: '
22/07/2025 : 11:26:02.377 :   facavoir: F
22/07/2025 : 11:26:02.377 : Total rows processed: 2
