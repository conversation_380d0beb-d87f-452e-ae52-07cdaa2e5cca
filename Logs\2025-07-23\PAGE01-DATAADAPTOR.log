23/07/2025 : 08:04:53.487 :     Entering
23/07/2025 : 08:04:53.494 :         Client           = 
23/07/2025 : 08:04:53.498 :         DateFactureStart = 
23/07/2025 : 08:04:53.504 :         DateFactureEnd   = 
23/07/2025 : 08:04:58.840 :         Count = 8832132
23/07/2025 : 08:04:58.841 :     Leaving
23/07/2025 : 08:04:58.961 :     Entering
23/07/2025 : 08:04:58.962 :         Client           = 
23/07/2025 : 08:04:58.964 :         DateFactureStart = 
23/07/2025 : 08:04:58.964 :         DateFactureEnd   = 
23/07/2025 : 08:05:01.554 :         Count = 8832132
23/07/2025 : 08:05:01.555 :     Leaving
23/07/2025 : 08:05:10.568 :     Entering
23/07/2025 : 08:05:10.569 :         Client           = 
23/07/2025 : 08:05:10.571 :         DateFactureStart = 
23/07/2025 : 08:05:10.572 :         DateFactureEnd   = 
23/07/2025 : 08:05:41.593 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 08:05:41.605 :         Entering
23/07/2025 : 08:05:41.605 :             Client           = 
23/07/2025 : 08:05:41.606 :             DateFactureStart = 
23/07/2025 : 08:05:41.606 :             DateFactureEnd   = 
23/07/2025 : 08:06:03.302 :             Count = 8832132
23/07/2025 : 08:06:03.303 :         Leaving
23/07/2025 : 08:06:06.730 :         Entering
23/07/2025 : 08:06:06.730 :             Client           = 
23/07/2025 : 08:06:06.731 :             DateFactureStart = 
23/07/2025 : 08:06:06.731 :             DateFactureEnd   = 
23/07/2025 : 08:06:09.446 :             Count = 8832132
23/07/2025 : 08:06:09.447 :         Leaving
23/07/2025 : 08:06:15.919 :         Entering
23/07/2025 : 08:06:15.920 :             Client           = 
23/07/2025 : 08:06:15.924 :             DateFactureStart = 
23/07/2025 : 08:06:15.925 :             DateFactureEnd   = 
23/07/2025 : 08:06:32.123 :             Count = 8832132
23/07/2025 : 08:06:32.124 :         Leaving
23/07/2025 : 08:06:32.136 :         Entering
23/07/2025 : 08:06:32.168 :             Client           = 
23/07/2025 : 08:06:32.173 :             DateFactureStart = 
23/07/2025 : 08:06:32.175 :             DateFactureEnd   = 
23/07/2025 : 08:06:47.130 :             Count = 8832132
23/07/2025 : 08:06:47.130 :         Leaving
23/07/2025 : 08:06:47.146 :         Entering
23/07/2025 : 08:06:47.146 :             Client           = 
23/07/2025 : 08:06:47.147 :             DateFactureStart = 
23/07/2025 : 08:06:47.147 :             DateFactureEnd   = 
23/07/2025 : 08:06:49.896 :             Count = 8832132
23/07/2025 : 08:06:49.896 :         Leaving
23/07/2025 : 08:07:01.893 :         Entering
23/07/2025 : 08:07:01.893 :             Client           = 
23/07/2025 : 08:07:01.894 :             DateFactureStart = 
23/07/2025 : 08:07:01.896 :             DateFactureEnd   = 05/02/2020 00:00:00
23/07/2025 : 08:07:02.954 :             The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 08:24:26.554 : Entering
23/07/2025 : 08:24:26.558 :     Client           = 
23/07/2025 : 08:24:26.561 :     DateFactureStart = 23/04/2025 09:24:26
23/07/2025 : 08:24:26.562 :     DateFactureEnd   = 23/07/2025 09:24:26
23/07/2025 : 08:25:01.032 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 08:25:01.586 :     Entering
23/07/2025 : 08:25:01.587 :         Client           = 
23/07/2025 : 08:25:01.587 :         DateFactureStart = 23/04/2025 09:24:26
23/07/2025 : 08:25:01.588 :         DateFactureEnd   = 23/07/2025 09:24:26
23/07/2025 : 08:25:05.088 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
