{"version": 2, "dgSpecHash": "3kGklRq1cq4=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\CarSystemsWebApp.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bitmiracle.libtiff.net\\2.4.649\\bitmiracle.libtiff.net.2.4.649.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp\\7.3.0.2\\harfbuzzsharp.7.3.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.macos\\7.3.0.2\\harfbuzzsharp.nativeassets.macos.7.3.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.win32\\7.3.0.2\\harfbuzzsharp.nativeassets.win32.7.3.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\9.0.0\\microsoft.aspnetcore.authorization.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\9.0.0\\microsoft.aspnetcore.components.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\9.0.0\\microsoft.aspnetcore.components.analyzers.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\9.0.0\\microsoft.aspnetcore.components.forms.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\9.0.0\\microsoft.aspnetcore.components.web.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\9.0.3\\microsoft.aspnetcore.cryptography.internal.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.keyderivation\\9.0.3\\microsoft.aspnetcore.cryptography.keyderivation.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.diagnostics.entityframeworkcore\\9.0.3\\microsoft.aspnetcore.diagnostics.entityframeworkcore.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.entityframeworkcore\\9.0.3\\microsoft.aspnetcore.identity.entityframeworkcore.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\9.0.0\\microsoft.aspnetcore.metadata.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\7.0.0\\microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\17.8.3\\microsoft.build.framework.17.8.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.locator\\1.7.8\\microsoft.build.locator.1.7.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.8.0\\microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.8.0\\microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.workspaces\\4.8.0\\microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.common\\4.8.0\\microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.msbuild\\4.8.0\\microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.5.0\\microsoft.csharp.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\5.1.6\\microsoft.data.sqlclient.5.1.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\5.1.1\\microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\9.0.3\\microsoft.entityframeworkcore.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\9.0.3\\microsoft.entityframeworkcore.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\9.0.3\\microsoft.entityframeworkcore.analyzers.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.design\\9.0.3\\microsoft.entityframeworkcore.design.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\9.0.3\\microsoft.entityframeworkcore.relational.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlserver\\9.0.3\\microsoft.entityframeworkcore.sqlserver.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.tools\\9.0.3\\microsoft.entityframeworkcore.tools.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.3\\microsoft.extensions.caching.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.3\\microsoft.extensions.caching.memory.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.3\\microsoft.extensions.configuration.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.3\\microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.3\\microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.3\\microsoft.extensions.dependencymodel.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.core\\9.0.3\\microsoft.extensions.identity.core.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.stores\\9.0.3\\microsoft.extensions.identity.stores.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.3\\microsoft.extensions.logging.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.3\\microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.3\\microsoft.extensions.options.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.3\\microsoft.extensions.primitives.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\6.35.0\\microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.35.0\\microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.35.0\\microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\6.35.0\\microsoft.identitymodel.protocols.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\6.35.0\\microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.35.0\\microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\9.0.0\\microsoft.jsinterop.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mono.texttemplating\\3.0.0\\mono.texttemplating.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.8\\skiasharp.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.harfbuzz\\2.88.8\\skiasharp.harfbuzz.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.8\\skiasharp.nativeassets.macos.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.8\\skiasharp.nativeassets.win32.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\28.1.33\\syncfusion.blazor.buttons.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\28.1.33\\syncfusion.blazor.calendars.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.charts\\28.1.33\\syncfusion.blazor.charts.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\28.1.33\\syncfusion.blazor.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\28.1.33\\syncfusion.blazor.data.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dataform\\28.1.33\\syncfusion.blazor.dataform.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.datavizcommon\\28.1.33\\syncfusion.blazor.datavizcommon.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.diagram\\28.1.33\\syncfusion.blazor.diagram.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\28.1.33\\syncfusion.blazor.dropdowns.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\28.1.33\\syncfusion.blazor.grid.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\28.1.33\\syncfusion.blazor.inputs.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.layouts\\28.1.33\\syncfusion.blazor.layouts.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\28.1.33\\syncfusion.blazor.lists.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\28.1.33\\syncfusion.blazor.navigations.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\28.1.33\\syncfusion.blazor.notifications.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\28.1.33\\syncfusion.blazor.popups.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.progressbar\\28.1.33\\syncfusion.blazor.progressbar.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.richtexteditor\\28.1.33\\syncfusion.blazor.richtexteditor.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.schedule\\28.1.33\\syncfusion.blazor.schedule.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\28.1.33\\syncfusion.blazor.spinner.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\28.1.33\\syncfusion.blazor.splitbuttons.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\28.1.33\\syncfusion.blazor.themes.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.treegrid\\28.1.33\\syncfusion.blazor.treegrid.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.compression.net.core\\28.1.33\\syncfusion.compression.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.excelexport.net.core\\28.1.33\\syncfusion.excelexport.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.licensing\\28.1.33\\syncfusion.licensing.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.metafilerenderer.net\\28.1.33\\syncfusion.metafilerenderer.net.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.pdf.imaging.net.core\\28.1.33\\syncfusion.pdf.imaging.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.pdf.net.core\\28.1.33\\syncfusion.pdf.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.pdfexport.net.core\\28.1.33\\syncfusion.pdfexport.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.skiasharphelper.net.core\\28.1.33\\syncfusion.skiasharphelper.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.xlsio.net.core\\28.1.33\\syncfusion.xlsio.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.xlsiorenderer.net.core\\28.1.33\\syncfusion.xlsiorenderer.net.core.28.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\6.0.0\\system.codedom.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\7.0.0\\system.collections.immutable.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition\\7.0.0\\system.composition.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.attributedmodel\\7.0.0\\system.composition.attributedmodel.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.convention\\7.0.0\\system.composition.convention.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.hosting\\7.0.0\\system.composition.hosting.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.runtime\\7.0.0\\system.composition.runtime.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.typedparts\\7.0.0\\system.composition.typedparts.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\6.0.1\\system.configuration.configurationmanager.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\9.0.3\\system.formats.asn1.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.35.0\\system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\7.0.0\\system.io.pipelines.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\7.0.0\\system.reflection.metadata.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\6.0.0\\system.runtime.caching.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.3\\system.text.json.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\7.0.0\\system.threading.channels.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512"], "logs": []}