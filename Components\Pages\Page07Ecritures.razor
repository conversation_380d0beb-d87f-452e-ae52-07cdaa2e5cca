@page "/ecritures"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using CarSystemsWebApp.Services
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection
@using Syncfusion.Drawing

@inject IJSRuntime      _jsRuntime
@inject SfDialogService _dialogService
@inject PageTitleService PageTitleService



<!-- ============== -->
<!-- PROFESSIONAL FILTER BAR -->
<!-- ============== -->

<div class="professional-filter-container">
    <div class="filter-header">
        <div class="filter-title">Filtres</div>
        <button class="btn-clear-all" @onclick="ClearAllFilters">Effacer tout</button>
    </div>

    <!-- First Row: Main Filters -->
    <div class="row filter-row align-items-center">
        <div class="col-auto">
            <SfDropDownList TValue="string" TItem="string"
                           DataSource="@Codes"
                           ShowClearButton="true"
                           Placeholder="Code journal..."
                           PopupHeight="220"
                           @bind-Value="@SelectedCodeJournal"
                           Width="240px">
            </SfDropDownList>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedNumCompteGeneral" ShowClearButton="true" Placeholder="Compte général..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedNumCompteAux" ShowClearButton="true" Placeholder="Compte auxiliaire..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedNumEcriture" ShowClearButton="true" Placeholder="Numéro d'écriture..." CssClass="compact-input-text"></SfTextBox>
        </div>
    </div>

    <!-- Second Row: Date Filters and Actions -->
    <div class="row filter-row align-items-center" style="margin-top: 12px;">
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date min..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateMin"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date max..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateMax"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfButton Type="button" @onclick="Helper_Refresh" CssClass="btn-inline">
                Appliquer
            </SfButton>
        </div>
        <div class="col-auto">
            <SfButton OnClick="OnButtonExport" CssClass="btn-inline-export">
                Export
            </SfButton>
        </div>
    </div>
</div>

<!-- ======= -->
<!-- RESULTS -->
<!-- ======= -->

@* <SfToolbar>
    <ToolbarItems>

        <ToolbarItem Id=HRRefreshGrid
        PrefixIcon="e-icons e-refresh"
        Tooltiptext="Actualiser"
        OnClick="OnToolbarButtonRefresh" />

        <ToolbarItem Type=ItemType.Separator />

    </ToolbarItems>
</SfToolbar> *@

@* style="height: calc(100vh - 205px) !important;" *@

<div>
    <SfGrid ID="MyGrid" 
    TValue="CS_ECR_ITEM"
    @ref="@MyGrid"
    Query="@MyQuery"
    AllowPaging="true"
    AllowFiltering="false"
    AllowSorting="true"
    AllowResizing="true" EnablePersistence="true"
    ShowColumnChooser="true" Toolbar=@ToolbarItems
    EnableInfiniteScrolling="false"
    Height="100%">
        <GridEvents TValue="CS_ECR_ITEM"
        RowSelected="OnRowSelected" 
        OnDataBound="DataBoundHandler"
        OnActionBegin="OnActionBegin"
        OnActionComplete="OnActionComplete"/>
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="22" />
        <GridColumns>
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_societe) HeaderText="code_societe" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_ex) HeaderText="num_ex" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_jrn) HeaderText="code_jrn" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datecompta2) HeaderText="datecompta" Format="dd/MM/yyy" Type=ColumnType.DateOnly Width="145" />

            <GridColumn Field=@nameof(CS_ECR_ITEM.num_ecr) HeaderText="num_ecr" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numlig) HeaderText="numlig" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numlig_let) HeaderText="numlig_let" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numlig_tva) HeaderText="numlig_tva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_etablissement) Visible="false" ShowInColumnChooser="true" HeaderText="code_etablissement" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.type) HeaderText="type" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.etat) HeaderText="etat" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.typpiece) Visible="false" ShowInColumnChooser="true" HeaderText="typpiece" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numpiece) Visible="false" ShowInColumnChooser="true" HeaderText="numpiece" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datepiece) HeaderText="datepiece" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_compte_gen) HeaderText="num_compte_gen" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_compte_aux) HeaderText="num_compte_aux" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.libelle) HeaderText="libelle" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.debit)  Visible="false" ShowInColumnChooser="true" HeaderText="debit" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.credit) Visible="false" ShowInColumnChooser="true" HeaderText="credit" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.dateech) Visible="false" ShowInColumnChooser="true" HeaderText="dateech" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_reg) Visible="false" ShowInColumnChooser="true"  HeaderText="code_reg" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.reflettrage) Visible="false" ShowInColumnChooser="true" HeaderText="reflettrage" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.let) Visible="false" ShowInColumnChooser="true" HeaderText="let" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.letpartiel) Visible="false" ShowInColumnChooser="true" HeaderText="letpartiel" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datelettrage) Visible="false" ShowInColumnChooser="true" HeaderText="datelettrage" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.letsuivant) Visible="false" ShowInColumnChooser="true" HeaderText="letsuivant" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.pointe) Visible="false" ShowInColumnChooser="true" HeaderText="pointe" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_tva) HeaderText="code_tva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.typassutva) Visible="false" ShowInColumnChooser="true" HeaderText="typassutva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.litige) Visible="false" ShowInColumnChooser="true" HeaderText="litige" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.central) Visible="false" ShowInColumnChooser="true" HeaderText="central" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.annul) Visible="false" ShowInColumnChooser="true" HeaderText="annul" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.transfer) Visible="false" ShowInColumnChooser="true" HeaderText="transfer" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.dateheuretransfer) HeaderText="dateheuretransfer" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_parc) HeaderText="code_parc" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_prest) HeaderText="code_prest" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_agence) HeaderText="code_agence" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datecrea) HeaderText="datecrea" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datemodi) HeaderText="datemodi" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_utilis_crea) HeaderText="code_utilis_crea" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_utilis_modi) HeaderText="code_utilis_modi" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zalp1) Visible="false" ShowInColumnChooser="true" HeaderText="zalp1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zalp2) Visible="false" ShowInColumnChooser="true" HeaderText="zalp2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zalp3) Visible="false" ShowInColumnChooser="true" HeaderText="zalp3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zdat1) Visible="false" ShowInColumnChooser="true" HeaderText="zdat1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zdat2) Visible="false" ShowInColumnChooser="true" HeaderText="zdat2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zdat3) Visible="false" ShowInColumnChooser="true" HeaderText="zdat3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.znum1) Visible="false" ShowInColumnChooser="true" HeaderText="znum1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.znum2) Visible="false" ShowInColumnChooser="true" HeaderText="znum2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.znum3) Visible="false" ShowInColumnChooser="true" HeaderText="znum3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zonecle) Visible="false" ShowInColumnChooser="true" HeaderText="zonecle" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.saisie) Visible="false" ShowInColumnChooser="true" HeaderText="saisie" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.debiteuro) HeaderText="debiteuro" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.crediteuro) HeaderText="crediteuro" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.debitpays) HeaderText="debitpays" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.creditpays) HeaderText="creditpays" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.deviserefeuro) Visible="false" ShowInColumnChooser="true" HeaderText="deviserefeuro" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.moistva) Visible="false" ShowInColumnChooser="true" HeaderText="moistva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datevaleur) Visible="false" ShowInColumnChooser="true" HeaderText="datevaleur" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_reg) Visible="false" ShowInColumnChooser="true" HeaderText="num_reg" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.contrepartie) Visible="false" ShowInColumnChooser="true" HeaderText="contrepartie" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.moyenpaie) Visible="false" ShowInColumnChooser="true" HeaderText="moyenpaie" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numfacfisc) Visible="false" ShowInColumnChooser="true" HeaderText="numfacfisc" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.extractDW) Visible="false" ShowInColumnChooser="true" HeaderText="extractDW" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp1) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp2) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp3) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp4) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp4" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp5) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp5" />
        </GridColumns>
    </SfGrid>
</div>

<script type="text/javascript">

    function saveAsFile(filename, bytesBase64) {
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++) {
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_ECR_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;

    private DateTime?   SelectedDateMin;
    private DateTime?   SelectedDateMax;
    private string      SelectedNumCompteGeneral;
    private string      SelectedNumCompteAux;
    private string      SelectedCodeJournal;
    private string      SelectedNumEcriture;


    protected override void OnInitialized()
    {
        // Set page title
        PageTitleService.SetTitle("Écritures");

        this.MyQuery = new Query()
            .AddParams("DateMin", null)
            .AddParams("DateMax", null)
            .AddParams("NumCompteGeneral", null)
            .AddParams("NumCompteAux", null)
            .AddParams("CodeJournal", null)
            .AddParams("NumEcriture", null);

        this.Codes = new List<string>() {
            "WA", "AS", "AN", "EF", "OD",
            "FE", "CA", "EN", "V1", "DE",
            "PA", "FO", "BQ", "PR", "CV",
            "VV", "FR", "FV", "AP", "FL", "VT"
        };

        this.Codes.Sort();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_ECR_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_ECR_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_ECR_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_ECR_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["DateMin"]           = this.SelectedDateMin;
        MyQuery.Queries.Params["DateMax"]           = this.SelectedDateMax;
        MyQuery.Queries.Params["NumCompteGeneral"]  = this.SelectedNumCompteGeneral;
        MyQuery.Queries.Params["NumCompteAux"]      = this.SelectedNumCompteAux;
        MyQuery.Queries.Params["CodeJournal"]       = this.SelectedCodeJournal;
        MyQuery.Queries.Params["NumEcriture"]       = this.SelectedNumEcriture;

        // Reset pagination to first page when applying new filters
        await MyGrid.GoToPageAsync(1);
        await MyGrid.Refresh();
    }

    private async Task ClearAllFilters()
    {
        SelectedDateMin = null;
        SelectedDateMax = null;
        SelectedNumCompteGeneral = null;
        SelectedNumCompteAux = null;
        SelectedCodeJournal = null;
        SelectedNumEcriture = null;
        await Helper_Refresh();
    }

    private async void OnButtonExport()
    {
        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // Create a workbook with 1 worksheet
            IWorkbook workbook = application.Workbooks.Create(1);
            IWorksheet worksheet = workbook.Worksheets[0];
            worksheet.Name = "ECRITURES";

            // Get data for export (large limit to get all data)
            string sortcolumn = "code_societe";
            string sortdir = "ascending";

            var items = MyCustomAdaptor.Helper_GetData(
                this.SelectedDateMin,
                this.SelectedDateMax,
                this.SelectedNumCompteGeneral,
                this.SelectedNumCompteAux,
                this.SelectedCodeJournal,
                this.SelectedNumEcriture,
                0, 100000,
                sortcolumn, sortdir, 900); // 15 minute timeout for export

            // Create a professional header design with WafaLLD orange branding
            // Main title with orange background
            worksheet["A1"].Text = "EXPORT ECRITURES";
            worksheet["A1"].CellStyle.Font.Bold = true;
            worksheet["A1"].CellStyle.Font.Size = 16;
            worksheet["A1"].CellStyle.Font.Color = ExcelKnownColors.White;
            worksheet["A1"].CellStyle.Color = Color.FromArgb(255, 140, 0); // WafaLLD Orange background
            worksheet["A1"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            worksheet["A1"].CellStyle.VerticalAlignment = ExcelVAlign.VAlignCenter;
            worksheet.Range["A1:R1"].Merge();
            worksheet.Range["A1:R1"].RowHeight = 25;

            // Export info with light orange background
            worksheet["A2"].Text = $"Exporté le: {DateTime.Now:dd/MM/yyyy HH:mm}";
            worksheet["A2"].CellStyle.Font.Size = 10;
            worksheet["A2"].CellStyle.Font.Italic = true;
            worksheet["A2"].CellStyle.Color = Color.FromArgb(255, 228, 196); // Light orange background
            worksheet["A2"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A2:R2"].Merge();

            // Add filter criteria info
            string filterInfo = "Filtres appliqués: ";
            if (SelectedDateMin.HasValue)
            {
                filterInfo += $"Date Min: {SelectedDateMin?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (SelectedDateMax.HasValue)
            {
                filterInfo += $"Date Max: {SelectedDateMax?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (!string.IsNullOrEmpty(SelectedNumCompteGeneral))
            {
                filterInfo += $"Compte Général: {SelectedNumCompteGeneral} | ";
            }
            if (!string.IsNullOrEmpty(SelectedNumCompteAux))
            {
                filterInfo += $"Compte Auxiliaire: {SelectedNumCompteAux} | ";
            }
            if (!string.IsNullOrEmpty(SelectedCodeJournal))
            {
                filterInfo += $"Code Journal: {SelectedCodeJournal} | ";
            }
            if (!string.IsNullOrEmpty(SelectedNumEcriture))
            {
                filterInfo += $"N° Écriture: {SelectedNumEcriture} | ";
            }
            if (filterInfo.EndsWith(" | "))
            {
                filterInfo = filterInfo.Substring(0, filterInfo.Length - 3);
            }
            else if (filterInfo == "Filtres appliqués: ")
            {
                filterInfo = "Aucun filtre appliqué";
            }

            worksheet["A3"].Text = filterInfo;
            worksheet["A3"].CellStyle.Font.Size = 9;
            worksheet["A3"].CellStyle.Color = Color.FromArgb(255, 248, 240); // Very light orange/cream
            worksheet["A3"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A3:R3"].Merge();

            // Add a separator line with orange accent
            worksheet.Range["A4:R4"].CellStyle.Color = Color.FromArgb(255, 165, 0); // Orange accent line
            worksheet.Range["A4:R4"].RowHeight = 3;

            // Convert to DataTable for Excel export
            DataTable table = new DataTable();
            Type type = typeof(CS_ECR_ITEM);
            PropertyInfo[] properties = type.GetProperties();

            // Create columns
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);

            // Add data rows
            foreach (var item in items)
            {
                DataRow row = table.NewRow();
                int k = 0;
                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }
                table.Rows.Add(row);
            }

            // Export data to Excel worksheet starting from row 6 to leave space for header design
            worksheet.ImportDataTable(table, true, 6, 1);

            // Style the data header row (column names) with orange theme
            IRange headerRange = worksheet.Range[6, 1, 6, properties.Length];
            headerRange.CellStyle.Font.Bold = true;
            headerRange.CellStyle.Font.Color = ExcelKnownColors.White;
            headerRange.CellStyle.Color = Color.FromArgb(255, 165, 0); // Medium orange background
            headerRange.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
            headerRange.RowHeight = 20;

            // Add alternating row colors and borders to all data
            if (items.Count > 0)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    int rowIndex = 7 + i; // Start from row 7 (after header)
                    IRange rowRange = worksheet.Range[rowIndex, 1, rowIndex, properties.Length];

                    // Alternating row colors with orange theme
                    if (i % 2 == 0)
                    {
                        rowRange.CellStyle.Color = Color.FromArgb(255, 250, 240); // Very light orange/cream
                    }
                    else
                    {
                        rowRange.CellStyle.Color = Color.White;
                    }

                    // Add borders
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
                }
            }

            worksheet.UsedRange.AutofitColumns();

            // Save and download
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                string filename = $"ExportEcritures_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "ECRITURES-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                List<CS_ECR_ITEM> empty;
                empty = Enumerable.Empty<CS_ECR_ITEM>().ToList();

                if (dm.Params is null || dm.Params.Count == 0)
                {
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Nothing to do", true);
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                    return dm.RequiresCounts ? new DataResult() { Result = empty, Count = 0 } : (object)empty;
                }

                var dateMin             = dm.Params["DateMin"] as DateTime?;
                var dateMax             = dm.Params["DateMax"] as DateTime?;
                var numCompteGeneral    = dm.Params["NumCompteGeneral"] as string;
                var numCompteAux        = dm.Params["NumCompteAux"] as string;
                var codeJournal         = dm.Params["CodeJournal"] as string;
                var numEcriture         = dm.Params["NumEcriture"] as string;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMin = {dateMin}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMax = {dateMax}", true);

                // string sortcolumn   = "code_societe";  // OLD: Hardcoded sorting
                // string sortdir      = "ascending";     // OLD: Hardcoded sorting

                // NEW: Dynamic sorting from DataManagerRequest
                string sortcolumn   = "datecompta2";  // Default sort column
                string sortdir      = "ascending";  // Default sort direction

                // Extract sorting information from DataManagerRequest
                if (dm.Sorted != null && dm.Sorted.Count > 0)
                {
                    var sortInfo = dm.Sorted[0]; // Get the first sort column
                    sortcolumn = sortInfo.Name;
                    sortdir = sortInfo.Direction;
                }

                CarSystemsContext model;
                model = new CarSystemsContext();

                /*
                var items = model.CS_ECR_ITEMS.FromSqlRaw("EXECUTE dbo.GetEcrItems {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}",
                    dateMin,
                    dateMax,
                    numCompteGeneral,
                    numCompteAux,
                    dm.Skip,
                    dm.Take,
                    sortcolumn,
                    sortdir).ToList();
                */

                var items = Helper_GetData(dateMin, dateMax, numCompteGeneral, numCompteAux, codeJournal, numEcriture, dm.Skip, dm.Take, sortcolumn, sortdir);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetEcrItemsCount_V2 {0}, {1}, {2}, {3}, {4}, {5}",
                        dateMin,
                        dateMax,
                        numCompteGeneral,
                        numCompteAux,
                        codeJournal,
                        numEcriture)
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);

                return null;
            }
        }

        public static List<CS_ECR_ITEM> Helper_GetData(DateTime? dateMin, DateTime? dateMax, string numCompteGeneral, string numCompteAux, string codeJournal, string numEcriture, int skip, int take, string sortcolumn, string sortdir, int timeoutSeconds = 30)
        {
            CarSystemsContext model;
            model = new CarSystemsContext();

            // Set command timeout for long-running operations
            model.Database.SetCommandTimeout(timeoutSeconds);

            var items = model.CS_ECR_ITEMS.FromSqlRaw("EXECUTE dbo.GetEcrItems_V2 {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}",
                dateMin,
                dateMax,
                numCompteGeneral,
                numCompteAux,
                codeJournal,
                numEcriture,
                skip,
                take,
                sortcolumn,
                sortdir).ToList();

            return items;
        }
    }
}
