﻿@inject IConfiguration _configuration

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <!-- <base href="/carsystemswebapp/" /> -->
    <link rel="stylesheet" href="@Assets["lib/bootstrap/dist/css/bootstrap.min.css"]" />
    <link rel="stylesheet" href="@Assets["app.css"]" />
    <link rel="stylesheet" href="@Assets["CarSystemsWebApp.styles.css"]" />
    <link rel="stylesheet" href="@Assets["css/filters.css"]" />
    <ImportMap />
    <link rel="icon" type="image/png" href="favicon.png" />
    <HeadOutlet />

    <!-- Syncfusion Styles -->
    <link rel="stylesheet" href="https://cdn.syncfusion.com/blazor/28.1.33/styles/tailwind.css" />

</head>

<body>
    <Routes />

    <!-- Syncfusion Javascript Library -->
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>

    <script src="_framework/blazor.web.js"></script>
</body>

</html>

@code {

    protected override Task OnInitializedAsync()
    {
        Utils.LogFolder = _configuration.GetValue<string>("appSettings:LOGFOLDER");

        // TEST

        // Assembly assembly;
        // assembly = Assembly.LoadFrom(@"D:\Dev\VTSupport\Dev2\VTCustomAppOncfLib2\bin\Debug\net8.0\VTCustomAppOncfLib2.dll");

        // Type type = assembly.GetType("VTCustomAppOncfLib2.Component1");

        return base.OnInitializedAsync();
    }
}
