15/07/2025 : 08:04:09.226 : Entering
15/07/2025 : 08:04:09.232 :     Client           = 
15/07/2025 : 08:04:09.233 :     DateFactureStart = 
15/07/2025 : 08:04:09.234 :     DateFactureEnd   = 
15/07/2025 : 08:04:09.234 :     NO SORTING: Using default sort
15/07/2025 : 08:04:22.031 :     Count = 8832132
15/07/2025 : 08:04:22.032 : Leaving
15/07/2025 : 08:04:22.663 : Entering
15/07/2025 : 08:04:22.663 :     Client           = 
15/07/2025 : 08:04:22.664 :     DateFactureStart = 
15/07/2025 : 08:04:22.664 :     DateFactureEnd   = 
15/07/2025 : 08:04:22.665 :     SORTING: Column = facavoir, Direction = ascending
15/07/2025 : 08:04:26.014 :     Count = 8832132
15/07/2025 : 08:04:26.015 : Leaving
15/07/2025 : 08:05:21.643 : Entering
15/07/2025 : 08:05:21.643 :     Client           = 
15/07/2025 : 08:05:21.644 :     DateFactureStart = 
15/07/2025 : 08:05:21.644 :     DateFactureEnd   = 
15/07/2025 : 08:05:21.644 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 08:05:24.385 :     Count = 8832132
15/07/2025 : 08:05:24.385 : Leaving
15/07/2025 : 08:05:24.458 : Entering
15/07/2025 : 08:05:24.458 :     Client           = 
15/07/2025 : 08:05:24.459 :     DateFactureStart = 
15/07/2025 : 08:05:24.460 :     DateFactureEnd   = 
15/07/2025 : 08:05:24.461 :     SORTING: Column = montant, Direction = descending
15/07/2025 : 08:05:26.289 :     Count = 8832132
15/07/2025 : 08:05:26.290 : Leaving
15/07/2025 : 08:05:26.314 : Entering
15/07/2025 : 08:05:26.315 :     Client           = 
15/07/2025 : 08:05:26.316 :     DateFactureStart = 
15/07/2025 : 08:05:26.316 :     DateFactureEnd   = 
15/07/2025 : 08:05:26.317 :     NO SORTING: Using default sort
15/07/2025 : 08:05:28.829 :     Count = 8832132
15/07/2025 : 08:05:28.829 : Leaving
15/07/2025 : 08:05:30.798 : Entering
15/07/2025 : 08:05:30.799 :     Client           = 
15/07/2025 : 08:05:30.800 :     DateFactureStart = 
15/07/2025 : 08:05:30.801 :     DateFactureEnd   = 
15/07/2025 : 08:05:30.802 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 08:05:32.680 :     Count = 8832132
15/07/2025 : 08:05:32.680 : Leaving
15/07/2025 : 08:05:32.707 : Entering
15/07/2025 : 08:05:32.707 :     Client           = 
15/07/2025 : 08:05:32.708 :     DateFactureStart = 
15/07/2025 : 08:05:32.708 :     DateFactureEnd   = 
15/07/2025 : 08:05:32.709 :     SORTING: Column = montant, Direction = descending
15/07/2025 : 08:05:34.540 :     Count = 8832132
15/07/2025 : 08:05:34.541 : Leaving
15/07/2025 : 08:05:44.395 : Entering
15/07/2025 : 08:05:44.396 :     Client           = 
15/07/2025 : 08:05:44.397 :     DateFactureStart = 
15/07/2025 : 08:05:44.398 :     DateFactureEnd   = 
15/07/2025 : 08:05:44.399 :     SORTING: Column = libelle, Direction = ascending
15/07/2025 : 08:05:46.728 :     Count = 8832132
15/07/2025 : 08:05:46.728 : Leaving
15/07/2025 : 08:05:46.742 : Entering
15/07/2025 : 08:05:46.743 :     Client           = 
15/07/2025 : 08:05:46.745 :     DateFactureStart = 
15/07/2025 : 08:05:46.745 :     DateFactureEnd   = 
15/07/2025 : 08:05:46.746 :     SORTING: Column = libelle, Direction = descending
15/07/2025 : 08:05:48.563 :     Count = 8832132
15/07/2025 : 08:05:48.564 : Leaving
15/07/2025 : 08:05:51.599 : Entering
15/07/2025 : 08:05:51.600 :     Client           = 
15/07/2025 : 08:05:51.602 :     DateFactureStart = 
15/07/2025 : 08:05:51.602 :     DateFactureEnd   = 
15/07/2025 : 08:05:51.604 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 08:05:53.573 :     Count = 8832132
15/07/2025 : 08:05:53.574 : Leaving
15/07/2025 : 08:05:53.605 : Entering
15/07/2025 : 08:05:53.606 :     Client           = 
15/07/2025 : 08:05:53.606 :     DateFactureStart = 
15/07/2025 : 08:05:53.616 :     DateFactureEnd   = 
15/07/2025 : 08:05:53.617 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 08:05:55.698 :     Count = 8832132
15/07/2025 : 08:05:55.698 : Leaving
15/07/2025 : 08:05:59.921 : Entering
15/07/2025 : 08:05:59.922 :     Client           = 
15/07/2025 : 08:05:59.923 :     DateFactureStart = 
15/07/2025 : 08:05:59.923 :     DateFactureEnd   = 
15/07/2025 : 08:05:59.924 :     NO SORTING: Using default sort
15/07/2025 : 08:06:01.809 :     Count = 8832132
15/07/2025 : 08:06:01.810 : Leaving
15/07/2025 : 08:06:01.823 : Entering
15/07/2025 : 08:06:01.823 :     Client           = 
15/07/2025 : 08:06:01.824 :     DateFactureStart = 
15/07/2025 : 08:06:01.824 :     DateFactureEnd   = 
15/07/2025 : 08:06:01.825 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 08:06:03.732 :     Count = 8832132
15/07/2025 : 08:06:03.733 : Leaving
15/07/2025 : 08:06:03.746 : Entering
15/07/2025 : 08:06:03.747 :     Client           = 
15/07/2025 : 08:06:03.748 :     DateFactureStart = 
15/07/2025 : 08:06:03.748 :     DateFactureEnd   = 
15/07/2025 : 08:06:03.749 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 08:06:05.613 :     Count = 8832132
15/07/2025 : 08:06:05.615 : Leaving
15/07/2025 : 08:06:05.651 : Entering
15/07/2025 : 08:06:05.651 :     Client           = 
15/07/2025 : 08:06:05.652 :     DateFactureStart = 
15/07/2025 : 08:06:05.653 :     DateFactureEnd   = 
15/07/2025 : 08:06:05.653 :     NO SORTING: Using default sort
15/07/2025 : 08:06:07.505 :     Count = 8832132
15/07/2025 : 08:06:07.506 : Leaving
15/07/2025 : 08:12:58.721 : Entering
15/07/2025 : 08:12:58.722 :     Client           = 1
15/07/2025 : 08:12:58.722 :     DateFactureStart = 
15/07/2025 : 08:12:58.722 :     DateFactureEnd   = 
15/07/2025 : 08:12:58.723 :     NO SORTING: Using default sort
15/07/2025 : 08:12:59.449 :     Count = 0
15/07/2025 : 08:12:59.450 : Leaving
15/07/2025 : 08:13:04.488 : Entering
15/07/2025 : 08:13:04.489 :     Client           = 12
15/07/2025 : 08:13:04.490 :     DateFactureStart = 
15/07/2025 : 08:13:04.491 :     DateFactureEnd   = 
15/07/2025 : 08:13:04.493 :     NO SORTING: Using default sort
15/07/2025 : 08:13:04.858 :     Count = 0
15/07/2025 : 08:13:04.859 : Leaving
15/07/2025 : 09:17:05.005 : Entering
15/07/2025 : 09:17:05.006 :     Client           = 0026
15/07/2025 : 09:17:05.008 :     DateFactureStart = 
15/07/2025 : 09:17:05.009 :     DateFactureEnd   = 
15/07/2025 : 09:17:05.012 :     NO SORTING: Using default sort
15/07/2025 : 09:17:09.433 :     Count = 55904
15/07/2025 : 09:17:09.434 : Leaving
15/07/2025 : 09:17:16.733 : Entering
15/07/2025 : 09:17:16.733 :     Client           = 0026
15/07/2025 : 09:17:16.734 :     DateFactureStart = 
15/07/2025 : 09:17:16.736 :     DateFactureEnd   = 
15/07/2025 : 09:17:16.736 :     NO SORTING: Using default sort
15/07/2025 : 09:17:25.582 :     Count = 55904
15/07/2025 : 09:17:25.583 : Leaving
15/07/2025 : 10:35:33.194 : Entering
15/07/2025 : 10:35:33.196 :     Client           = 
15/07/2025 : 10:35:33.197 :     DateFactureStart = 
15/07/2025 : 10:35:33.198 :     DateFactureEnd   = 
15/07/2025 : 10:35:33.199 :     NO SORTING: Using default sort
15/07/2025 : 10:35:38.463 :     Count = 8832132
15/07/2025 : 10:35:38.464 : Leaving
15/07/2025 : 10:35:38.595 : Entering
15/07/2025 : 10:35:38.596 :     Client           = 
15/07/2025 : 10:35:38.597 :     DateFactureStart = 
15/07/2025 : 10:35:38.597 :     DateFactureEnd   = 
15/07/2025 : 10:35:38.598 :     NO SORTING: Using default sort
15/07/2025 : 10:35:42.585 :     Count = 8832132
15/07/2025 : 10:35:42.585 : Leaving
15/07/2025 : 10:35:51.144 : Entering
15/07/2025 : 10:35:51.145 :     Client           = 
15/07/2025 : 10:35:51.148 :     DateFactureStart = 
15/07/2025 : 10:35:51.150 :     DateFactureEnd   = 
15/07/2025 : 10:35:51.151 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 10:35:55.231 :     Count = 8832132
15/07/2025 : 10:35:55.232 : Leaving
15/07/2025 : 10:35:55.262 : Entering
15/07/2025 : 10:35:55.263 :     Client           = 
15/07/2025 : 10:35:55.264 :     DateFactureStart = 
15/07/2025 : 10:35:55.266 :     DateFactureEnd   = 
15/07/2025 : 10:35:55.268 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:35:57.678 :     Count = 8832132
15/07/2025 : 10:35:57.679 : Leaving
15/07/2025 : 10:35:57.708 : Entering
15/07/2025 : 10:35:57.710 :     Client           = 
15/07/2025 : 10:35:57.713 :     DateFactureStart = 
15/07/2025 : 10:35:57.715 :     DateFactureEnd   = 
15/07/2025 : 10:35:57.716 :     NO SORTING: Using default sort
15/07/2025 : 10:36:00.271 :     Count = 8832132
15/07/2025 : 10:36:00.272 : Leaving
15/07/2025 : 10:36:00.306 : Entering
15/07/2025 : 10:36:00.307 :     Client           = 
15/07/2025 : 10:36:00.308 :     DateFactureStart = 
15/07/2025 : 10:36:00.309 :     DateFactureEnd   = 
15/07/2025 : 10:36:00.311 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 10:36:02.920 :     Count = 8832132
15/07/2025 : 10:36:02.921 : Leaving
15/07/2025 : 10:36:05.016 : Entering
15/07/2025 : 10:36:05.017 :     Client           = 
15/07/2025 : 10:36:05.020 :     DateFactureStart = 
15/07/2025 : 10:36:05.022 :     DateFactureEnd   = 
15/07/2025 : 10:36:05.024 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:36:07.194 :     Count = 8832132
15/07/2025 : 10:36:07.195 : Leaving
15/07/2025 : 10:36:07.223 : Entering
15/07/2025 : 10:36:07.225 :     Client           = 
15/07/2025 : 10:36:07.229 :     DateFactureStart = 
15/07/2025 : 10:36:07.232 :     DateFactureEnd   = 
15/07/2025 : 10:36:07.233 :     NO SORTING: Using default sort
15/07/2025 : 10:36:09.333 :     Count = 8832132
15/07/2025 : 10:36:09.334 : Leaving
15/07/2025 : 10:36:09.360 : Entering
15/07/2025 : 10:36:09.361 :     Client           = 
15/07/2025 : 10:36:09.362 :     DateFactureStart = 
15/07/2025 : 10:36:09.362 :     DateFactureEnd   = 
15/07/2025 : 10:36:09.364 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 10:36:11.694 :     Count = 8832132
15/07/2025 : 10:36:11.695 : Leaving
15/07/2025 : 10:36:11.704 : Entering
15/07/2025 : 10:36:11.704 :     Client           = 
15/07/2025 : 10:36:11.705 :     DateFactureStart = 
15/07/2025 : 10:36:11.705 :     DateFactureEnd   = 
15/07/2025 : 10:36:11.706 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:36:14.088 :     Count = 8832132
15/07/2025 : 10:36:14.089 : Leaving
15/07/2025 : 10:37:05.446 : Entering
15/07/2025 : 10:37:05.447 :     Client           = 
15/07/2025 : 10:37:05.449 :     DateFactureStart = 15/07/2005 00:00:00
15/07/2025 : 10:37:05.450 :     DateFactureEnd   = 15/07/2015 00:00:00
15/07/2025 : 10:37:05.450 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:37:06.370 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
15/07/2025 : 10:42:18.527 : Entering
15/07/2025 : 10:42:18.529 :     Client           = 
15/07/2025 : 10:42:18.529 :     DateFactureStart = 
15/07/2025 : 10:42:18.530 :     DateFactureEnd   = 
15/07/2025 : 10:42:18.530 :     NO SORTING: Using default sort
15/07/2025 : 10:42:20.863 :     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:42:21.073 :     Entering
15/07/2025 : 10:42:21.074 :         Client           = 
15/07/2025 : 10:42:21.074 :         DateFactureStart = 
15/07/2025 : 10:42:21.074 :         DateFactureEnd   = 
15/07/2025 : 10:42:21.074 :         SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:42:22.966 :         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:43:28.528 :         Entering
15/07/2025 : 10:43:28.528 :             Client           = 0026
15/07/2025 : 10:43:28.528 :             DateFactureStart = 
15/07/2025 : 10:43:28.529 :             DateFactureEnd   = 
15/07/2025 : 10:43:28.529 :             SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:43:29.162 :             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:43:37.090 :             Entering
15/07/2025 : 10:43:37.092 :                 Client           = 0026
15/07/2025 : 10:43:37.096 :                 DateFactureStart = 
15/07/2025 : 10:43:37.100 :                 DateFactureEnd   = 
15/07/2025 : 10:43:37.104 :                 SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:43:37.384 :                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:43:42.033 :                 Entering
15/07/2025 : 10:43:42.033 :                     Client           = 0026
15/07/2025 : 10:43:42.034 :                     DateFactureStart = 
15/07/2025 : 10:43:42.035 :                     DateFactureEnd   = 
15/07/2025 : 10:43:42.036 :                     SORTING: Column = codetiers, Direction = ascending
15/07/2025 : 10:43:42.677 :                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:43:42.694 :                     Entering
15/07/2025 : 10:43:42.694 :                         Client           = 0026
15/07/2025 : 10:43:42.695 :                         DateFactureStart = 
15/07/2025 : 10:43:42.695 :                         DateFactureEnd   = 
15/07/2025 : 10:43:42.696 :                         SORTING: Column = codetiers, Direction = descending
15/07/2025 : 10:43:43.723 :                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:48:13.590 : Entering
15/07/2025 : 10:48:13.592 :     Client           = 
15/07/2025 : 10:48:13.592 :     DateFactureStart = 
15/07/2025 : 10:48:13.593 :     DateFactureEnd   = 
15/07/2025 : 10:48:13.593 :     NO SORTING: Using default sort
15/07/2025 : 10:48:16.230 :     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:48:16.629 :     Entering
15/07/2025 : 10:48:16.630 :         Client           = 
15/07/2025 : 10:48:16.630 :         DateFactureStart = 
15/07/2025 : 10:48:16.631 :         DateFactureEnd   = 
15/07/2025 : 10:48:16.631 :         SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:48:18.219 :         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:54:28.497 : Entering
15/07/2025 : 10:54:28.500 :     Client           = 
15/07/2025 : 10:54:28.501 :     DateFactureStart = 
15/07/2025 : 10:54:28.501 :     DateFactureEnd   = 
15/07/2025 : 10:54:28.501 :     NO SORTING: Using default sort
15/07/2025 : 10:54:31.872 :     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:54:32.149 :     Entering
15/07/2025 : 10:54:32.149 :         Client           = 
15/07/2025 : 10:54:32.149 :         DateFactureStart = 
15/07/2025 : 10:54:32.149 :         DateFactureEnd   = 
15/07/2025 : 10:54:32.149 :         SORTING: Column = datecrea, Direction = descending
15/07/2025 : 10:54:33.531 :         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:57:47.415 :         Entering
15/07/2025 : 10:57:47.415 :             Client           = 
15/07/2025 : 10:57:47.416 :             DateFactureStart = 
15/07/2025 : 10:57:47.416 :             DateFactureEnd   = 
15/07/2025 : 10:57:47.417 :             SORTING: Column = montant, Direction = ascending
15/07/2025 : 10:58:05.300 :             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:58:05.319 :             Entering
15/07/2025 : 10:58:05.319 :                 Client           = 
15/07/2025 : 10:58:05.320 :                 DateFactureStart = 
15/07/2025 : 10:58:05.320 :                 DateFactureEnd   = 
15/07/2025 : 10:58:05.321 :                 SORTING: Column = montant, Direction = descending
15/07/2025 : 10:58:21.590 :                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:58:21.605 :                 Entering
15/07/2025 : 10:58:21.606 :                     Client           = 
15/07/2025 : 10:58:21.607 :                     DateFactureStart = 
15/07/2025 : 10:58:21.607 :                     DateFactureEnd   = 
15/07/2025 : 10:58:21.608 :                     NO SORTING: Using default sort
15/07/2025 : 10:58:21.823 :                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:58:21.837 :                     Entering
15/07/2025 : 10:58:21.837 :                         Client           = 
15/07/2025 : 10:58:21.838 :                         DateFactureStart = 
15/07/2025 : 10:58:21.839 :                         DateFactureEnd   = 
15/07/2025 : 10:58:21.840 :                         SORTING: Column = montant, Direction = ascending
15/07/2025 : 10:58:38.887 :                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:58:38.916 :                         Entering
15/07/2025 : 10:58:38.917 :                             Client           = 
15/07/2025 : 10:58:38.918 :                             DateFactureStart = 
15/07/2025 : 10:58:38.919 :                             DateFactureEnd   = 
15/07/2025 : 10:58:38.919 :                             SORTING: Column = montant, Direction = descending
15/07/2025 : 10:58:52.174 :                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:58:52.182 :                             Entering
15/07/2025 : 10:58:52.183 :                                 Client           = 
15/07/2025 : 10:58:52.185 :                                 DateFactureStart = 
15/07/2025 : 10:58:52.186 :                                 DateFactureEnd   = 
15/07/2025 : 10:58:52.187 :                                 NO SORTING: Using default sort
15/07/2025 : 10:58:52.313 :                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:58:52.318 :                                 Entering
15/07/2025 : 10:58:52.318 :                                     Client           = 
15/07/2025 : 10:58:52.319 :                                     DateFactureStart = 
15/07/2025 : 10:58:52.319 :                                     DateFactureEnd   = 
15/07/2025 : 10:58:52.320 :                                     SORTING: Column = montant, Direction = ascending
15/07/2025 : 10:59:08.391 :                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:59:08.403 :                                     Entering
15/07/2025 : 10:59:08.404 :                                         Client           = 
15/07/2025 : 10:59:08.404 :                                         DateFactureStart = 
15/07/2025 : 10:59:08.405 :                                         DateFactureEnd   = 
15/07/2025 : 10:59:08.406 :                                         SORTING: Column = montant, Direction = descending
15/07/2025 : 10:59:21.374 :                                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:59:21.391 :                                         Entering
15/07/2025 : 10:59:21.401 :                                             Client           = 
15/07/2025 : 10:59:21.402 :                                             DateFactureStart = 
15/07/2025 : 10:59:21.402 :                                             DateFactureEnd   = 
15/07/2025 : 10:59:21.403 :                                             NO SORTING: Using default sort
15/07/2025 : 10:59:21.534 :                                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:59:21.548 :                                             Entering
15/07/2025 : 10:59:21.549 :                                                 Client           = 
15/07/2025 : 10:59:21.551 :                                                 DateFactureStart = 
15/07/2025 : 10:59:21.552 :                                                 DateFactureEnd   = 
15/07/2025 : 10:59:21.556 :                                                 SORTING: Column = montant, Direction = ascending
15/07/2025 : 10:59:36.192 :                                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:59:36.199 :                                                 Entering
15/07/2025 : 10:59:36.199 :                                                     Client           = 
15/07/2025 : 10:59:36.200 :                                                     DateFactureStart = 
15/07/2025 : 10:59:36.200 :                                                     DateFactureEnd   = 
15/07/2025 : 10:59:36.201 :                                                     SORTING: Column = montant, Direction = descending
15/07/2025 : 10:59:48.765 :                                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:59:48.774 :                                                     Entering
15/07/2025 : 10:59:48.774 :                                                         Client           = 
15/07/2025 : 10:59:48.775 :                                                         DateFactureStart = 
15/07/2025 : 10:59:48.776 :                                                         DateFactureEnd   = 
15/07/2025 : 10:59:48.777 :                                                         NO SORTING: Using default sort
15/07/2025 : 10:59:48.887 :                                                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 10:59:48.893 :                                                         Entering
15/07/2025 : 10:59:48.893 :                                                             Client           = 
15/07/2025 : 10:59:48.894 :                                                             DateFactureStart = 
15/07/2025 : 10:59:48.895 :                                                             DateFactureEnd   = 
15/07/2025 : 10:59:48.895 :                                                             SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:00:08.696 :                                                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:08.710 :                                                             Entering
15/07/2025 : 11:00:08.711 :                                                                 Client           = 
15/07/2025 : 11:00:08.713 :                                                                 DateFactureStart = 
15/07/2025 : 11:00:08.715 :                                                                 DateFactureEnd   = 
15/07/2025 : 11:00:08.718 :                                                                 SORTING: Column = etat, Direction = ascending
15/07/2025 : 11:00:10.086 :                                                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:10.189 :                                                                 Entering
15/07/2025 : 11:00:10.205 :                                                                     Client           = 
15/07/2025 : 11:00:10.207 :                                                                     DateFactureStart = 
15/07/2025 : 11:00:10.208 :                                                                     DateFactureEnd   = 
15/07/2025 : 11:00:10.209 :                                                                     SORTING: Column = etat, Direction = descending
15/07/2025 : 11:00:12.270 :                                                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:12.294 :                                                                     Entering
15/07/2025 : 11:00:12.295 :                                                                         Client           = 
15/07/2025 : 11:00:12.295 :                                                                         DateFactureStart = 
15/07/2025 : 11:00:12.295 :                                                                         DateFactureEnd   = 
15/07/2025 : 11:00:12.296 :                                                                         NO SORTING: Using default sort
15/07/2025 : 11:00:12.418 :                                                                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:12.424 :                                                                         Entering
15/07/2025 : 11:00:12.425 :                                                                             Client           = 
15/07/2025 : 11:00:12.425 :                                                                             DateFactureStart = 
15/07/2025 : 11:00:12.425 :                                                                             DateFactureEnd   = 
15/07/2025 : 11:00:12.426 :                                                                             SORTING: Column = etat, Direction = ascending
15/07/2025 : 11:00:13.899 :                                                                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:13.904 :                                                                             Entering
15/07/2025 : 11:00:13.904 :                                                                                 Client           = 
15/07/2025 : 11:00:13.905 :                                                                                 DateFactureStart = 
15/07/2025 : 11:00:13.905 :                                                                                 DateFactureEnd   = 
15/07/2025 : 11:00:13.906 :                                                                                 SORTING: Column = integre, Direction = ascending
15/07/2025 : 11:00:15.126 :                                                                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:15.137 :                                                                                 Entering
15/07/2025 : 11:00:15.137 :                                                                                     Client           = 
15/07/2025 : 11:00:15.138 :                                                                                     DateFactureStart = 
15/07/2025 : 11:00:15.138 :                                                                                     DateFactureEnd   = 
15/07/2025 : 11:00:15.139 :                                                                                     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 11:00:16.214 :                                                                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:16.220 :                                                                                     Entering
15/07/2025 : 11:00:16.221 :                                                                                         Client           = 
15/07/2025 : 11:00:16.221 :                                                                                         DateFactureStart = 
15/07/2025 : 11:00:16.224 :                                                                                         DateFactureEnd   = 
15/07/2025 : 11:00:16.225 :                                                                                         SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:00:17.210 :                                                                                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:17.217 :                                                                                         Entering
15/07/2025 : 11:00:17.217 :                                                                                             Client           = 
15/07/2025 : 11:00:17.219 :                                                                                             DateFactureStart = 
15/07/2025 : 11:00:17.221 :                                                                                             DateFactureEnd   = 
15/07/2025 : 11:00:17.221 :                                                                                             SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:00:36.282 :                                                                                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:36.288 :                                                                                             Entering
15/07/2025 : 11:00:36.289 :                                                                                                 Client           = 
15/07/2025 : 11:00:36.290 :                                                                                                 DateFactureStart = 
15/07/2025 : 11:00:36.290 :                                                                                                 DateFactureEnd   = 
15/07/2025 : 11:00:36.291 :                                                                                                 SORTING: Column = montant, Direction = descending
15/07/2025 : 11:00:51.513 :                                                                                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:51.523 :                                                                                                 Entering
15/07/2025 : 11:00:51.525 :                                                                                                     Client           = 
15/07/2025 : 11:00:51.527 :                                                                                                     DateFactureStart = 
15/07/2025 : 11:00:51.528 :                                                                                                     DateFactureEnd   = 
15/07/2025 : 11:00:51.529 :                                                                                                     NO SORTING: Using default sort
15/07/2025 : 11:00:51.745 :                                                                                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:00:51.749 :                                                                                                     Entering
15/07/2025 : 11:00:51.749 :                                                                                                         Client           = 
15/07/2025 : 11:00:51.750 :                                                                                                         DateFactureStart = 
15/07/2025 : 11:00:51.750 :                                                                                                         DateFactureEnd   = 
15/07/2025 : 11:00:51.751 :                                                                                                         SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:01:09.223 :                                                                                                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:01:09.295 :                                                                                                         Entering
15/07/2025 : 11:01:09.295 :                                                                                                             Client           = 
15/07/2025 : 11:01:09.296 :                                                                                                             DateFactureStart = 
15/07/2025 : 11:01:09.297 :                                                                                                             DateFactureEnd   = 
15/07/2025 : 11:01:09.297 :                                                                                                             SORTING: Column = montant, Direction = descending
15/07/2025 : 11:01:23.676 :                                                                                                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:01:23.688 :                                                                                                             Entering
15/07/2025 : 11:01:23.688 :                                                                                                                 Client           = 
15/07/2025 : 11:01:23.690 :                                                                                                                 DateFactureStart = 
15/07/2025 : 11:01:23.691 :                                                                                                                 DateFactureEnd   = 
15/07/2025 : 11:01:23.693 :                                                                                                                 NO SORTING: Using default sort
15/07/2025 : 11:01:23.828 :                                                                                                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:01:23.834 :                                                                                                                 Entering
15/07/2025 : 11:01:23.834 :                                                                                                                     Client           = 
15/07/2025 : 11:01:23.835 :                                                                                                                     DateFactureStart = 
15/07/2025 : 11:01:23.836 :                                                                                                                     DateFactureEnd   = 
15/07/2025 : 11:01:23.836 :                                                                                                                     SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:01:37.649 :                                                                                                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:01:37.656 :                                                                                                                     Entering
15/07/2025 : 11:01:37.657 :                                                                                                                         Client           = 
15/07/2025 : 11:01:37.658 :                                                                                                                         DateFactureStart = 
15/07/2025 : 11:01:37.658 :                                                                                                                         DateFactureEnd   = 
15/07/2025 : 11:01:37.659 :                                                                                                                         SORTING: Column = montant, Direction = descending
15/07/2025 : 11:01:50.437 :                                                                                                                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:01:50.476 :                                                                                                                         Entering
15/07/2025 : 11:01:50.476 :                                                                                                                             Client           = 
15/07/2025 : 11:01:50.477 :                                                                                                                             DateFactureStart = 
15/07/2025 : 11:01:50.478 :                                                                                                                             DateFactureEnd   = 
15/07/2025 : 11:01:50.478 :                                                                                                                             NO SORTING: Using default sort
15/07/2025 : 11:01:50.575 :                                                                                                                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:01:50.579 :                                                                                                                             Entering
15/07/2025 : 11:01:50.579 :                                                                                                                                 Client           = 
15/07/2025 : 11:01:50.580 :                                                                                                                                 DateFactureStart = 
15/07/2025 : 11:01:50.580 :                                                                                                                                 DateFactureEnd   = 
15/07/2025 : 11:01:50.581 :                                                                                                                                 SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:02:04.836 :                                                                                                                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:02:04.947 :                                                                                                                                 Entering
15/07/2025 : 11:02:04.947 :                                                                                                                                     Client           = 0026
15/07/2025 : 11:02:04.949 :                                                                                                                                     DateFactureStart = 
15/07/2025 : 11:02:04.950 :                                                                                                                                     DateFactureEnd   = 
15/07/2025 : 11:02:04.951 :                                                                                                                                     SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:02:12.172 :                                                                                                                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:02:12.180 :                                                                                                                                     Entering
15/07/2025 : 11:02:12.181 :                                                                                                                                         Client           = 0026
15/07/2025 : 11:02:12.182 :                                                                                                                                         DateFactureStart = 
15/07/2025 : 11:02:12.182 :                                                                                                                                         DateFactureEnd   = 
15/07/2025 : 11:02:12.183 :                                                                                                                                         SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:02:19.695 :                                                                                                                                         Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:02:19.709 :                                                                                                                                         Entering
15/07/2025 : 11:02:19.709 :                                                                                                                                             Client           = 0026
15/07/2025 : 11:02:19.710 :                                                                                                                                             DateFactureStart = 
15/07/2025 : 11:02:19.710 :                                                                                                                                             DateFactureEnd   = 
15/07/2025 : 11:02:19.711 :                                                                                                                                             SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:02:27.144 :                                                                                                                                             Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:02:27.163 :                                                                                                                                             Entering
15/07/2025 : 11:02:27.164 :                                                                                                                                                 Client           = 0026
15/07/2025 : 11:02:27.167 :                                                                                                                                                 DateFactureStart = 
15/07/2025 : 11:02:27.173 :                                                                                                                                                 DateFactureEnd   = 
15/07/2025 : 11:02:27.174 :                                                                                                                                                 SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:02:34.722 :                                                                                                                                                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:05:12.031 :                                                                                                                                                 Entering
15/07/2025 : 11:05:12.034 :                                                                                                                                                     Client           = 0026
15/07/2025 : 11:05:12.038 :                                                                                                                                                     DateFactureStart = 
15/07/2025 : 11:05:12.038 :                                                                                                                                                     DateFactureEnd   = 
15/07/2025 : 11:05:12.039 :                                                                                                                                                     SORTING: Column = montant, Direction = ascending
15/07/2025 : 11:05:22.451 :                                                                                                                                                     Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:09:55.895 : Entering
15/07/2025 : 11:09:55.897 :     Client           = 
15/07/2025 : 11:09:55.898 :     DateFactureStart = 
15/07/2025 : 11:09:55.898 :     DateFactureEnd   = 
15/07/2025 : 11:09:55.898 :     NO SORTING: Using default sort
15/07/2025 : 11:09:58.693 :     Items retrieved: 0
15/07/2025 : 11:10:02.196 :     Count = 8832132
15/07/2025 : 11:10:02.197 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 11:10:02.199 : Leaving
15/07/2025 : 11:10:02.219 : Entering
15/07/2025 : 11:10:02.219 :     Client           = 
15/07/2025 : 11:10:02.220 :     DateFactureStart = 
15/07/2025 : 11:10:02.220 :     DateFactureEnd   = 
15/07/2025 : 11:10:02.221 :     NO SORTING: Using default sort
15/07/2025 : 11:10:02.401 :     Items retrieved: 0
15/07/2025 : 11:10:05.625 :     Count = 8832132
15/07/2025 : 11:10:05.627 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 11:10:05.632 : Leaving
15/07/2025 : 11:10:05.971 : Entering
15/07/2025 : 11:10:05.972 :     Client           = 
15/07/2025 : 11:10:05.974 :     DateFactureStart = 
15/07/2025 : 11:10:05.974 :     DateFactureEnd   = 
15/07/2025 : 11:10:05.975 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:10:06.107 :     Items retrieved: 0
15/07/2025 : 11:10:08.171 :     Count = 8832132
15/07/2025 : 11:10:08.172 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 11:10:08.176 : Leaving
15/07/2025 : 11:11:36.462 : Entering
15/07/2025 : 11:11:36.463 :     Client           = 
15/07/2025 : 11:11:36.466 :     DateFactureStart = 
15/07/2025 : 11:11:36.468 :     DateFactureEnd   = 
15/07/2025 : 11:11:36.469 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:11:36.657 :     Items retrieved: 0
15/07/2025 : 11:11:38.851 :     Count = 8832132
15/07/2025 : 11:11:38.851 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 11:11:38.852 : Leaving
15/07/2025 : 11:23:24.621 : Entering
15/07/2025 : 11:23:24.621 :     Client           = 
15/07/2025 : 11:23:24.623 :     DateFactureStart = 
15/07/2025 : 11:23:24.624 :     DateFactureEnd   = 
15/07/2025 : 11:23:24.624 :     NO SORTING: Using default sort
15/07/2025 : 11:23:24.939 :     Items retrieved: 0
15/07/2025 : 11:27:11.971 : Entering
15/07/2025 : 11:27:11.974 :     Client           = 
15/07/2025 : 11:27:11.974 :     DateFactureStart = 
15/07/2025 : 11:27:11.974 :     DateFactureEnd   = 
15/07/2025 : 11:27:11.974 :     NO SORTING: Using default sort
15/07/2025 : 11:27:14.941 :     Items retrieved: 100
15/07/2025 : 11:27:17.320 :     Count = 8832132
15/07/2025 : 11:27:17.322 : Leaving
15/07/2025 : 11:27:17.948 : Entering
15/07/2025 : 11:27:17.948 :     Client           = 
15/07/2025 : 11:27:17.948 :     DateFactureStart = 
15/07/2025 : 11:27:17.949 :     DateFactureEnd   = 
15/07/2025 : 11:27:17.949 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:27:18.089 :     Items retrieved: 100
15/07/2025 : 11:27:20.473 :     Count = 8832132
15/07/2025 : 11:27:20.474 : Leaving
15/07/2025 : 11:29:02.949 : Entering
15/07/2025 : 11:29:02.950 :     Client           = 0026
15/07/2025 : 11:29:02.952 :     DateFactureStart = 
15/07/2025 : 11:29:02.952 :     DateFactureEnd   = 
15/07/2025 : 11:29:02.952 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:29:03.049 :     Items retrieved: 100
15/07/2025 : 11:29:04.882 :     Count = 55904
15/07/2025 : 11:29:04.884 : Leaving
15/07/2025 : 11:33:28.259 : Entering
15/07/2025 : 11:33:28.262 :     Client           = 
15/07/2025 : 11:33:28.263 :     DateFactureStart = 
15/07/2025 : 11:33:28.263 :     DateFactureEnd   = 
15/07/2025 : 11:33:28.263 :     NO SORTING: Using default sort
15/07/2025 : 11:33:31.383 :     Items retrieved: 14
15/07/2025 : 11:33:35.838 :     Count = 8832132
15/07/2025 : 11:33:35.840 : Leaving
15/07/2025 : 11:33:36.301 : Entering
15/07/2025 : 11:33:36.302 :     Client           = 
15/07/2025 : 11:33:36.302 :     DateFactureStart = 
15/07/2025 : 11:33:36.302 :     DateFactureEnd   = 
15/07/2025 : 11:33:36.303 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:33:37.309 :     Items retrieved: 14
15/07/2025 : 11:33:40.339 :     Count = 8832132
15/07/2025 : 11:33:40.340 : Leaving
15/07/2025 : 11:33:56.022 : Entering
15/07/2025 : 11:33:56.022 :     Client           = 0026
15/07/2025 : 11:33:56.024 :     DateFactureStart = 
15/07/2025 : 11:33:56.025 :     DateFactureEnd   = 
15/07/2025 : 11:33:56.025 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:33:56.583 :     Items retrieved: 14
15/07/2025 : 11:33:58.294 :     Count = 55904
15/07/2025 : 11:33:58.295 : Leaving
15/07/2025 : 11:34:05.524 : Entering
15/07/2025 : 11:34:05.525 :     Client           = 0026
15/07/2025 : 11:34:05.526 :     DateFactureStart = 
15/07/2025 : 11:34:05.526 :     DateFactureEnd   = 
15/07/2025 : 11:34:05.529 :     NO SORTING: Using default sort
15/07/2025 : 11:34:07.056 :     Items retrieved: 14
15/07/2025 : 11:34:08.984 :     Count = 55904
15/07/2025 : 11:34:08.985 : Leaving
15/07/2025 : 11:34:09.042 : Entering
15/07/2025 : 11:34:09.042 :     Client           = 0026
15/07/2025 : 11:34:09.043 :     DateFactureStart = 
15/07/2025 : 11:34:09.044 :     DateFactureEnd   = 
15/07/2025 : 11:34:09.044 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 11:34:09.263 :     Items retrieved: 14
15/07/2025 : 11:34:11.131 :     Count = 55904
15/07/2025 : 11:34:11.132 : Leaving
15/07/2025 : 11:46:46.758 : Entering
15/07/2025 : 11:46:46.762 :     Client           = 
15/07/2025 : 11:46:46.763 :     DateFactureStart = 
15/07/2025 : 11:46:46.764 :     DateFactureEnd   = 
15/07/2025 : 11:46:46.764 :     NO SORTING: Using default sort
15/07/2025 : 11:46:50.533 :     Items retrieved: 14
15/07/2025 : 11:46:55.548 :     Count = 8832132
15/07/2025 : 11:46:55.551 : Leaving
15/07/2025 : 11:46:55.803 : Entering
15/07/2025 : 11:46:55.803 :     Client           = 
15/07/2025 : 11:46:55.803 :     DateFactureStart = 
15/07/2025 : 11:46:55.803 :     DateFactureEnd   = 
15/07/2025 : 11:46:55.804 :     NO SORTING: Using default sort
15/07/2025 : 11:46:55.915 :     Items retrieved: 14
15/07/2025 : 11:47:00.053 :     Count = 8832132
15/07/2025 : 11:47:00.054 : Leaving
15/07/2025 : 11:47:45.923 : Entering
15/07/2025 : 11:47:45.925 :     Client           = 
15/07/2025 : 11:47:45.926 :     DateFactureStart = 
15/07/2025 : 11:47:45.926 :     DateFactureEnd   = 
15/07/2025 : 11:47:45.926 :     NO SORTING: Using default sort
15/07/2025 : 11:47:47.201 :     Items retrieved: 14
15/07/2025 : 11:47:49.746 :     Count = 8832132
15/07/2025 : 11:47:49.748 : Leaving
15/07/2025 : 11:47:49.994 : Entering
15/07/2025 : 11:47:49.995 :     Client           = 
15/07/2025 : 11:47:49.995 :     DateFactureStart = 
15/07/2025 : 11:47:49.995 :     DateFactureEnd   = 
15/07/2025 : 11:47:49.995 :     NO SORTING: Using default sort
15/07/2025 : 11:47:50.097 :     Items retrieved: 14
15/07/2025 : 11:47:51.907 :     Count = 8832132
15/07/2025 : 11:47:51.907 : Leaving
15/07/2025 : 11:49:15.484 : Entering
15/07/2025 : 11:49:15.485 :     Client           = 
15/07/2025 : 11:49:15.485 :     DateFactureStart = 
15/07/2025 : 11:49:15.486 :     DateFactureEnd   = 
15/07/2025 : 11:49:15.486 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 11:49:16.804 :     Items retrieved: 14
15/07/2025 : 11:49:18.886 :     Count = 8832132
15/07/2025 : 11:49:18.887 : Leaving
15/07/2025 : 11:49:18.901 : Entering
15/07/2025 : 11:49:18.901 :     Client           = 
15/07/2025 : 11:49:18.901 :     DateFactureStart = 
15/07/2025 : 11:49:18.902 :     DateFactureEnd   = 
15/07/2025 : 11:49:18.902 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:49:20.434 :     Items retrieved: 14
15/07/2025 : 11:49:22.223 :     Count = 8832132
15/07/2025 : 11:49:22.225 : Leaving
15/07/2025 : 11:59:23.514 : Entering
15/07/2025 : 11:59:23.515 :     Client           = 
15/07/2025 : 11:59:23.516 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 11:59:23.516 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 11:59:23.516 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 11:59:32.242 :     Items retrieved: 14
15/07/2025 : 11:59:36.187 :     Count = 5388899
15/07/2025 : 11:59:36.188 : Leaving
15/07/2025 : 11:59:52.439 : Entering
15/07/2025 : 11:59:52.439 :     Client           = 
15/07/2025 : 11:59:52.439 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 11:59:52.439 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 11:59:52.440 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 12:00:04.165 :     Items retrieved: 14
15/07/2025 : 12:00:08.351 :     Count = 5388899
15/07/2025 : 12:00:08.351 : Leaving
15/07/2025 : 12:00:30.462 : Entering
15/07/2025 : 12:00:30.462 :     Client           = 0028
15/07/2025 : 12:00:30.463 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:00:30.463 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:00:30.463 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 12:00:30.737 :     Items retrieved: 14
15/07/2025 : 12:00:34.417 :     Count = 14618
15/07/2025 : 12:00:34.418 : Leaving
15/07/2025 : 12:00:43.963 : Entering
15/07/2025 : 12:00:43.964 :     Client           = 0028
15/07/2025 : 12:00:43.964 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:00:43.964 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:00:43.965 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 12:00:44.237 :     Items retrieved: 14
15/07/2025 : 12:00:47.731 :     Count = 14618
15/07/2025 : 12:00:47.732 : Leaving
15/07/2025 : 12:00:57.141 : Entering
15/07/2025 : 12:00:57.142 :     Client           = 0028
15/07/2025 : 12:00:57.142 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:00:57.142 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:00:57.142 :     SORTING: Column = libelle, Direction = ascending
15/07/2025 : 12:00:57.472 :     Items retrieved: 14
15/07/2025 : 12:01:01.083 :     Count = 14618
15/07/2025 : 12:01:01.084 : Leaving
15/07/2025 : 12:01:01.091 : Entering
15/07/2025 : 12:01:01.091 :     Client           = 0028
15/07/2025 : 12:01:01.092 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:01:01.092 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:01:01.093 :     SORTING: Column = libelle, Direction = descending
15/07/2025 : 12:01:01.431 :     Items retrieved: 14
15/07/2025 : 12:01:04.616 :     Count = 14618
15/07/2025 : 12:01:04.617 : Leaving
15/07/2025 : 12:01:04.648 : Entering
15/07/2025 : 12:01:04.650 :     Client           = 0028
15/07/2025 : 12:01:04.651 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:01:04.652 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:01:04.654 :     NO SORTING: Using default sort
15/07/2025 : 12:01:34.755 :     Items retrieved: 0
15/07/2025 : 12:01:38.374 :     Count = 14618
15/07/2025 : 12:01:38.374 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 12:01:38.375 : Leaving
15/07/2025 : 12:01:38.376 : Entering
15/07/2025 : 12:01:38.377 :     Client           = 0028
15/07/2025 : 12:01:38.378 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:01:38.378 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:01:38.379 :     NO SORTING: Using default sort
15/07/2025 : 12:02:00.626 :     Entering
15/07/2025 : 12:02:00.626 :         Client           = 
15/07/2025 : 12:02:00.627 :         DateFactureStart = 
15/07/2025 : 12:02:00.627 :         DateFactureEnd   = 
15/07/2025 : 12:02:00.627 :         NO SORTING: Using default sort
15/07/2025 : 12:02:00.894 :         Items retrieved: 14
15/07/2025 : 12:02:05.606 :         Count = 8832132
15/07/2025 : 12:02:05.607 :     Leaving
15/07/2025 : 12:02:05.693 :     Entering
15/07/2025 : 12:02:05.694 :         Client           = 
15/07/2025 : 12:02:05.694 :         DateFactureStart = 
15/07/2025 : 12:02:05.694 :         DateFactureEnd   = 
15/07/2025 : 12:02:05.694 :         SORTING: Column = datecrea, Direction = descending
15/07/2025 : 12:02:06.920 :         Items retrieved: 14
15/07/2025 : 12:02:08.460 :         Items retrieved: 0
15/07/2025 : 12:02:12.697 :         Count = 8832132
15/07/2025 : 12:02:12.699 :     Leaving
15/07/2025 : 12:02:13.280 :     Count = 14618
15/07/2025 : 12:02:13.281 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 12:02:13.282 : Leaving
15/07/2025 : 12:02:13.303 : Entering
15/07/2025 : 12:02:13.304 :     Client           = 0028
15/07/2025 : 12:02:13.304 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:02:13.305 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:02:13.305 :     SORTING: Column = libelle, Direction = ascending
15/07/2025 : 12:02:13.643 :     Items retrieved: 2
15/07/2025 : 12:02:17.435 :     Count = 14618
15/07/2025 : 12:02:17.436 : Leaving
15/07/2025 : 12:02:17.449 : Entering
15/07/2025 : 12:02:17.450 :     Client           = 0028
15/07/2025 : 12:02:17.450 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:02:17.451 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:02:17.452 :     SORTING: Column = tottvaprestht, Direction = ascending
15/07/2025 : 12:02:18.041 :     Items retrieved: 2
15/07/2025 : 12:02:21.566 :     Count = 14618
15/07/2025 : 12:02:21.567 : Leaving
15/07/2025 : 12:02:21.578 : Entering
15/07/2025 : 12:02:21.579 :     Client           = 0028
15/07/2025 : 12:02:21.579 :     DateFactureStart = 10/07/2009 00:00:00
15/07/2025 : 12:02:21.580 :     DateFactureEnd   = 17/07/2020 00:00:00
15/07/2025 : 12:02:21.580 :     SORTING: Column = tottvaprestht, Direction = descending
15/07/2025 : 12:02:21.961 :     Items retrieved: 2
15/07/2025 : 12:02:25.591 :     Count = 14618
15/07/2025 : 12:02:25.591 : Leaving
15/07/2025 : 12:10:48.592 : Entering
15/07/2025 : 12:10:48.593 :     Client           = 
15/07/2025 : 12:10:48.593 :     DateFactureStart = 
15/07/2025 : 12:10:48.593 :     DateFactureEnd   = 
15/07/2025 : 12:10:48.593 :     NO SORTING: Using default sort
15/07/2025 : 12:10:48.719 :     Items retrieved: 14
15/07/2025 : 12:10:53.401 :     Count = 8832132
15/07/2025 : 12:10:53.402 : Leaving
15/07/2025 : 12:10:53.536 : Entering
15/07/2025 : 12:10:53.536 :     Client           = 
15/07/2025 : 12:10:53.536 :     DateFactureStart = 
15/07/2025 : 12:10:53.536 :     DateFactureEnd   = 
15/07/2025 : 12:10:53.537 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 12:10:54.575 :     Items retrieved: 14
15/07/2025 : 12:10:57.804 :     Count = 8832132
15/07/2025 : 12:10:57.805 : Leaving
15/07/2025 : 12:11:03.269 : Entering
15/07/2025 : 12:11:03.269 :     Client           = 
15/07/2025 : 12:11:03.270 :     DateFactureStart = 
15/07/2025 : 12:11:03.270 :     DateFactureEnd   = 
15/07/2025 : 12:11:03.270 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 12:11:04.105 :     Items retrieved: 14
15/07/2025 : 12:11:06.712 :     Count = 8832132
15/07/2025 : 12:11:06.713 : Leaving
15/07/2025 : 13:19:27.568 : Entering
15/07/2025 : 13:19:27.571 :     Client           = 
15/07/2025 : 13:19:27.572 :     DateFactureStart = 
15/07/2025 : 13:19:27.572 :     DateFactureEnd   = 
15/07/2025 : 13:19:27.573 :     NO SORTING: Using default sort
15/07/2025 : 13:19:31.714 :     Items retrieved: 14
15/07/2025 : 13:19:34.759 :     Count = 8832132
15/07/2025 : 13:19:34.761 : Leaving
15/07/2025 : 13:19:35.271 : Entering
15/07/2025 : 13:19:35.271 :     Client           = 
15/07/2025 : 13:19:35.272 :     DateFactureStart = 
15/07/2025 : 13:19:35.272 :     DateFactureEnd   = 
15/07/2025 : 13:19:35.273 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 13:19:36.662 :     Items retrieved: 14
15/07/2025 : 13:19:38.546 :     Count = 8832132
15/07/2025 : 13:19:38.547 : Leaving
15/07/2025 : 13:19:56.598 : Entering
15/07/2025 : 13:19:56.598 :     Client           = 
15/07/2025 : 13:19:56.598 :     DateFactureStart = 
15/07/2025 : 13:19:56.599 :     DateFactureEnd   = 
15/07/2025 : 13:19:56.599 :     NO SORTING: Using default sort
15/07/2025 : 13:19:56.695 :     Items retrieved: 14
15/07/2025 : 13:20:00.064 :     Count = 8832132
15/07/2025 : 13:20:00.065 : Leaving
15/07/2025 : 13:20:00.182 : Entering
15/07/2025 : 13:20:00.182 :     Client           = 
15/07/2025 : 13:20:00.183 :     DateFactureStart = 
15/07/2025 : 13:20:00.183 :     DateFactureEnd   = 
15/07/2025 : 13:20:00.183 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 13:20:01.541 :     Items retrieved: 14
15/07/2025 : 13:20:04.661 :     Count = 8832132
15/07/2025 : 13:20:04.662 : Leaving
15/07/2025 : 13:30:35.033 : Entering
15/07/2025 : 13:30:35.035 :     Client           = 
15/07/2025 : 13:30:35.036 :     DateFactureStart = 
15/07/2025 : 13:30:35.036 :     DateFactureEnd   = 
15/07/2025 : 13:30:35.037 :     NO SORTING: Using default sort
15/07/2025 : 13:30:36.690 :     Items retrieved: 14
15/07/2025 : 13:30:39.522 :     Count = 8832132
15/07/2025 : 13:30:39.524 : Leaving
15/07/2025 : 13:30:40.203 : Entering
15/07/2025 : 13:30:40.204 :     Client           = 
15/07/2025 : 13:30:40.204 :     DateFactureStart = 
15/07/2025 : 13:30:40.205 :     DateFactureEnd   = 
15/07/2025 : 13:30:40.206 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 13:30:41.597 :     Items retrieved: 14
15/07/2025 : 13:30:44.454 :     Count = 8832132
15/07/2025 : 13:30:44.455 : Leaving
15/07/2025 : 13:30:47.159 : Entering
15/07/2025 : 13:30:47.160 :     Client           = 
15/07/2025 : 13:30:47.160 :     DateFactureStart = 
15/07/2025 : 13:30:47.161 :     DateFactureEnd   = 
15/07/2025 : 13:30:47.161 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 13:30:48.294 :     Items retrieved: 14
15/07/2025 : 13:30:50.401 :     Count = 8832132
15/07/2025 : 13:30:50.402 : Leaving
15/07/2025 : 13:31:08.472 : Entering
15/07/2025 : 13:31:08.473 :     Client           = 
15/07/2025 : 13:31:08.473 :     DateFactureStart = 
15/07/2025 : 13:31:08.474 :     DateFactureEnd   = 
15/07/2025 : 13:31:08.474 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 13:31:09.351 :     Items retrieved: 14
15/07/2025 : 13:31:11.236 :     Count = 8832132
15/07/2025 : 13:31:11.237 : Leaving
15/07/2025 : 13:42:59.214 : Entering
15/07/2025 : 13:42:59.216 :     Client           = 
15/07/2025 : 13:42:59.217 :     DateFactureStart = 
15/07/2025 : 13:42:59.217 :     DateFactureEnd   = 
15/07/2025 : 13:42:59.218 :     NO SORTING: Using default sort
15/07/2025 : 13:43:01.365 :     Items retrieved: 14
15/07/2025 : 13:43:03.673 :     Count = 8832132
15/07/2025 : 13:43:03.675 : Leaving
15/07/2025 : 13:43:04.045 : Entering
15/07/2025 : 13:43:04.046 :     Client           = 
15/07/2025 : 13:43:04.046 :     DateFactureStart = 
15/07/2025 : 13:43:04.047 :     DateFactureEnd   = 
15/07/2025 : 13:43:04.047 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 13:43:05.537 :     Items retrieved: 14
15/07/2025 : 13:43:07.541 :     Count = 8832132
15/07/2025 : 13:43:07.542 : Leaving
15/07/2025 : 13:43:21.032 : Entering
15/07/2025 : 13:43:21.033 :     Client           = 0026
15/07/2025 : 13:43:21.033 :     DateFactureStart = 
15/07/2025 : 13:43:21.034 :     DateFactureEnd   = 
15/07/2025 : 13:43:21.034 :     SORTING: Column = integre, Direction = ascending
15/07/2025 : 13:43:21.288 :     Items retrieved: 14
15/07/2025 : 13:43:23.204 :     Count = 55904
15/07/2025 : 13:43:23.205 : Leaving
15/07/2025 : 13:43:36.607 : Entering
15/07/2025 : 13:43:36.608 :     Client           = 
15/07/2025 : 13:43:36.609 :     DateFactureStart = 
15/07/2025 : 13:43:36.610 :     DateFactureEnd   = 
15/07/2025 : 13:43:36.611 :     NO SORTING: Using default sort
15/07/2025 : 13:43:36.668 :     Items retrieved: 14
15/07/2025 : 13:43:40.935 :     Count = 8832132
15/07/2025 : 13:43:40.936 : Leaving
15/07/2025 : 13:43:40.997 : Entering
15/07/2025 : 13:43:40.998 :     Client           = 
15/07/2025 : 13:43:40.998 :     DateFactureStart = 
15/07/2025 : 13:43:40.998 :     DateFactureEnd   = 
15/07/2025 : 13:43:40.998 :     NO SORTING: Using default sort
15/07/2025 : 13:43:41.052 :     Items retrieved: 14
15/07/2025 : 13:43:43.897 :     Count = 8832132
15/07/2025 : 13:43:43.898 : Leaving
15/07/2025 : 14:03:22.616 : Entering
15/07/2025 : 14:03:22.618 :     Client           = 
15/07/2025 : 14:03:22.619 :     DateFactureStart = 
15/07/2025 : 14:03:22.619 :     DateFactureEnd   = 
15/07/2025 : 14:03:22.620 :     NO SORTING: Using default sort
15/07/2025 : 14:03:24.367 :     Items retrieved: 14
15/07/2025 : 14:03:28.273 :     Count = 8832132
15/07/2025 : 14:03:28.275 : Leaving
15/07/2025 : 14:03:28.560 : Entering
15/07/2025 : 14:03:28.561 :     Client           = 
15/07/2025 : 14:03:28.561 :     DateFactureStart = 
15/07/2025 : 14:03:28.561 :     DateFactureEnd   = 
15/07/2025 : 14:03:28.562 :     NO SORTING: Using default sort
15/07/2025 : 14:03:28.677 :     Items retrieved: 14
15/07/2025 : 14:03:30.885 :     Count = 8832132
15/07/2025 : 14:03:30.886 : Leaving
15/07/2025 : 14:04:34.745 : Entering
15/07/2025 : 14:04:34.746 :     Client           = 
15/07/2025 : 14:04:34.747 :     DateFactureStart = 
15/07/2025 : 14:04:34.748 :     DateFactureEnd   = 
15/07/2025 : 14:04:34.749 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 14:04:35.793 :     Items retrieved: 14
15/07/2025 : 14:04:37.614 :     Count = 8832132
15/07/2025 : 14:04:37.615 : Leaving
15/07/2025 : 14:04:37.650 : Entering
15/07/2025 : 14:04:37.651 :     Client           = 
15/07/2025 : 14:04:37.651 :     DateFactureStart = 
15/07/2025 : 14:04:37.652 :     DateFactureEnd   = 
15/07/2025 : 14:04:37.652 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 14:04:38.624 :     Items retrieved: 14
15/07/2025 : 14:04:40.907 :     Count = 8832132
15/07/2025 : 14:04:40.908 : Leaving
15/07/2025 : 14:04:40.948 : Entering
15/07/2025 : 14:04:40.949 :     Client           = 
15/07/2025 : 14:04:40.950 :     DateFactureStart = 
15/07/2025 : 14:04:40.951 :     DateFactureEnd   = 
15/07/2025 : 14:04:40.952 :     NO SORTING: Using default sort
15/07/2025 : 14:04:41.127 :     Items retrieved: 14
15/07/2025 : 14:04:43.416 :     Count = 8832132
15/07/2025 : 14:04:43.417 : Leaving
15/07/2025 : 14:10:13.116 : Entering
15/07/2025 : 14:10:13.117 :     Client           = 
15/07/2025 : 14:10:13.118 :     DateFactureStart = 
15/07/2025 : 14:10:13.118 :     DateFactureEnd   = 
15/07/2025 : 14:10:13.119 :     NO SORTING: Using default sort
15/07/2025 : 14:10:14.877 :     Items retrieved: 14
15/07/2025 : 14:10:18.196 :     Count = 8832132
15/07/2025 : 14:10:18.197 : Leaving
15/07/2025 : 14:10:18.936 : Entering
15/07/2025 : 14:10:18.936 :     Client           = 
15/07/2025 : 14:10:18.937 :     DateFactureStart = 
15/07/2025 : 14:10:18.938 :     DateFactureEnd   = 
15/07/2025 : 14:10:18.938 :     NO SORTING: Using default sort
15/07/2025 : 14:10:19.175 :     Items retrieved: 14
15/07/2025 : 14:10:21.294 :     Count = 8832132
15/07/2025 : 14:10:21.295 : Leaving
15/07/2025 : 14:10:28.616 : Entering
15/07/2025 : 14:10:28.616 :     Client           = 0026
15/07/2025 : 14:10:28.617 :     DateFactureStart = 
15/07/2025 : 14:10:28.617 :     DateFactureEnd   = 
15/07/2025 : 14:10:28.618 :     NO SORTING: Using default sort
15/07/2025 : 14:10:30.592 :     Items retrieved: 14
15/07/2025 : 14:10:32.376 :     Count = 55904
15/07/2025 : 14:10:32.377 : Leaving
15/07/2025 : 14:10:48.420 : Entering
15/07/2025 : 14:10:48.420 :     Client           = 0026
15/07/2025 : 14:10:48.421 :     DateFactureStart = 
15/07/2025 : 14:10:48.421 :     DateFactureEnd   = 
15/07/2025 : 14:10:48.422 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 14:10:58.188 :     Items retrieved: 14
15/07/2025 : 14:11:00.083 :     Count = 55904
15/07/2025 : 14:11:00.084 : Leaving
15/07/2025 : 14:11:00.107 : Entering
15/07/2025 : 14:11:00.108 :     Client           = 0026
15/07/2025 : 14:11:00.109 :     DateFactureStart = 
15/07/2025 : 14:11:00.109 :     DateFactureEnd   = 
15/07/2025 : 14:11:00.110 :     SORTING: Column = montant, Direction = descending
15/07/2025 : 14:11:08.276 :     Items retrieved: 14
15/07/2025 : 14:11:10.152 :     Count = 55904
15/07/2025 : 14:11:10.153 : Leaving
15/07/2025 : 14:11:10.191 : Entering
15/07/2025 : 14:11:10.191 :     Client           = 0026
15/07/2025 : 14:11:10.192 :     DateFactureStart = 
15/07/2025 : 14:11:10.192 :     DateFactureEnd   = 
15/07/2025 : 14:11:10.192 :     NO SORTING: Using default sort
15/07/2025 : 14:11:11.098 :     Items retrieved: 14
15/07/2025 : 14:11:12.899 :     Count = 55904
15/07/2025 : 14:11:12.900 : Leaving
15/07/2025 : 14:11:12.930 : Entering
15/07/2025 : 14:11:12.931 :     Client           = 0026
15/07/2025 : 14:11:12.933 :     DateFactureStart = 
15/07/2025 : 14:11:12.934 :     DateFactureEnd   = 
15/07/2025 : 14:11:12.935 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 14:11:20.366 :     Items retrieved: 14
15/07/2025 : 14:11:22.181 :     Count = 55904
15/07/2025 : 14:11:22.182 : Leaving
15/07/2025 : 14:14:12.002 : Entering
15/07/2025 : 14:14:12.004 :     Client           = 
15/07/2025 : 14:14:12.005 :     DateFactureStart = 
15/07/2025 : 14:14:12.005 :     DateFactureEnd   = 
15/07/2025 : 14:14:12.005 :     NO SORTING: Using default sort
15/07/2025 : 14:14:13.521 :     Items retrieved: 14
15/07/2025 : 14:14:18.202 :     Count = 8832132
15/07/2025 : 14:14:18.204 : Leaving
15/07/2025 : 14:14:18.519 : Entering
15/07/2025 : 14:14:18.519 :     Client           = 
15/07/2025 : 14:14:18.520 :     DateFactureStart = 
15/07/2025 : 14:14:18.520 :     DateFactureEnd   = 
15/07/2025 : 14:14:18.520 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 14:14:49.022 :     Items retrieved: 0
15/07/2025 : 14:14:53.712 :     Count = 8832132
15/07/2025 : 14:14:53.713 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 14:14:53.714 : Leaving
15/07/2025 : 14:14:54.294 : Entering
15/07/2025 : 14:14:54.295 :     Client           = 
15/07/2025 : 14:14:54.295 :     DateFactureStart = 
15/07/2025 : 14:14:54.295 :     DateFactureEnd   = 
15/07/2025 : 14:14:54.295 :     NO SORTING: Using default sort
15/07/2025 : 14:14:54.841 :     Items retrieved: 14
15/07/2025 : 14:14:59.664 :     Count = 8832132
15/07/2025 : 14:14:59.665 : Leaving
15/07/2025 : 14:14:59.834 : Entering
15/07/2025 : 14:14:59.835 :     Client           = 
15/07/2025 : 14:14:59.835 :     DateFactureStart = 
15/07/2025 : 14:14:59.836 :     DateFactureEnd   = 
15/07/2025 : 14:14:59.836 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 14:15:30.111 :     Items retrieved: 0
15/07/2025 : 14:15:32.839 :     Count = 8832132
15/07/2025 : 14:15:32.839 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 14:15:32.840 : Leaving
15/07/2025 : 14:19:49.438 : Entering
15/07/2025 : 14:19:49.440 :     Client           = 
15/07/2025 : 14:19:49.440 :     DateFactureStart = 
15/07/2025 : 14:19:49.440 :     DateFactureEnd   = 
15/07/2025 : 14:19:49.441 :     NO SORTING: Using default sort
15/07/2025 : 14:19:51.141 :     Items retrieved: 14
15/07/2025 : 14:19:53.476 :     Count = 8832132
15/07/2025 : 14:19:53.478 : Leaving
15/07/2025 : 14:19:53.821 : Entering
15/07/2025 : 14:19:53.822 :     Client           = 
15/07/2025 : 14:19:53.822 :     DateFactureStart = 
15/07/2025 : 14:19:53.822 :     DateFactureEnd   = 
15/07/2025 : 14:19:53.822 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 14:20:24.073 :     Items retrieved: 0
15/07/2025 : 14:20:24.250 :     Entering
15/07/2025 : 14:20:24.250 :         Client           = 
15/07/2025 : 14:20:24.251 :         DateFactureStart = 
15/07/2025 : 14:20:24.251 :         DateFactureEnd   = 
15/07/2025 : 14:20:24.251 :         NO SORTING: Using default sort
15/07/2025 : 14:20:25.305 :         Items retrieved: 14
15/07/2025 : 14:20:27.596 :         Count = 8832132
15/07/2025 : 14:20:27.597 :         WARNING: No items returned from Helper_GetData
15/07/2025 : 14:20:27.598 :     Leaving
15/07/2025 : 14:20:28.877 :     Count = 8832132
15/07/2025 : 14:20:28.877 : Leaving
15/07/2025 : 14:20:28.961 : Entering
15/07/2025 : 14:20:28.962 :     Client           = 
15/07/2025 : 14:20:28.962 :     DateFactureStart = 
15/07/2025 : 14:20:28.963 :     DateFactureEnd   = 
15/07/2025 : 14:20:28.963 :     NO SORTING: Using default sort
15/07/2025 : 14:20:29.025 :     Items retrieved: 14
15/07/2025 : 14:20:32.210 :     Count = 8832132
15/07/2025 : 14:20:32.211 : Leaving
15/07/2025 : 14:22:52.293 : Entering
15/07/2025 : 14:22:52.294 :     Client           = 
15/07/2025 : 14:22:52.295 :     DateFactureStart = 
15/07/2025 : 14:22:52.295 :     DateFactureEnd   = 
15/07/2025 : 14:22:52.296 :     NO SORTING: Using default sort
15/07/2025 : 14:22:54.091 :     Items retrieved: 14
15/07/2025 : 14:22:57.371 :     Count = 8832132
15/07/2025 : 14:22:57.373 : Leaving
15/07/2025 : 14:22:57.730 : Entering
15/07/2025 : 14:22:57.730 :     Client           = 
15/07/2025 : 14:22:57.730 :     DateFactureStart = 
15/07/2025 : 14:22:57.731 :     DateFactureEnd   = 
15/07/2025 : 14:22:57.731 :     NO SORTING: Using default sort
15/07/2025 : 14:22:57.850 :     Items retrieved: 14
15/07/2025 : 14:22:59.985 :     Count = 8832132
15/07/2025 : 14:22:59.986 : Leaving
15/07/2025 : 14:24:02.379 : Entering
15/07/2025 : 14:24:02.379 :     Client           = 
15/07/2025 : 14:24:02.380 :     DateFactureStart = 
15/07/2025 : 14:24:02.380 :     DateFactureEnd   = 
15/07/2025 : 14:24:02.380 :     NO SORTING: Using default sort
15/07/2025 : 14:24:02.440 :     Items retrieved: 14
15/07/2025 : 14:24:04.575 :     Count = 8832132
15/07/2025 : 14:24:04.576 : Leaving
15/07/2025 : 14:24:04.673 : Entering
15/07/2025 : 14:24:04.673 :     Client           = 
15/07/2025 : 14:24:04.674 :     DateFactureStart = 
15/07/2025 : 14:24:04.674 :     DateFactureEnd   = 
15/07/2025 : 14:24:04.674 :     NO SORTING: Using default sort
15/07/2025 : 14:24:04.731 :     Items retrieved: 14
15/07/2025 : 14:24:06.901 :     Count = 8832132
15/07/2025 : 14:24:06.902 : Leaving
15/07/2025 : 14:29:22.413 : Entering
15/07/2025 : 14:29:22.415 :     Client           = 
15/07/2025 : 14:29:22.415 :     DateFactureStart = 
15/07/2025 : 14:29:22.416 :     DateFactureEnd   = 
15/07/2025 : 14:29:22.416 :     NO SORTING: Using default sort
15/07/2025 : 14:29:24.137 :     Items retrieved: 14
15/07/2025 : 14:29:26.325 :     Count = 8832132
15/07/2025 : 14:29:26.328 : Leaving
15/07/2025 : 14:29:26.652 : Entering
15/07/2025 : 14:29:26.652 :     Client           = 
15/07/2025 : 14:29:26.652 :     DateFactureStart = 
15/07/2025 : 14:29:26.653 :     DateFactureEnd   = 
15/07/2025 : 14:29:26.653 :     NO SORTING: Using default sort
15/07/2025 : 14:29:26.767 :     Items retrieved: 14
15/07/2025 : 14:29:28.697 :     Count = 8832132
15/07/2025 : 14:29:28.698 : Leaving
15/07/2025 : 14:30:21.781 : Entering
15/07/2025 : 14:30:21.781 :     Client           = 
15/07/2025 : 14:30:21.782 :     DateFactureStart = 
15/07/2025 : 14:30:21.782 :     DateFactureEnd   = 
15/07/2025 : 14:30:21.782 :     NO SORTING: Using default sort
15/07/2025 : 14:30:21.839 :     Items retrieved: 14
15/07/2025 : 14:30:23.890 :     Count = 8832132
15/07/2025 : 14:30:23.890 : Leaving
15/07/2025 : 14:30:23.979 : Entering
15/07/2025 : 14:30:23.979 :     Client           = 
15/07/2025 : 14:30:23.980 :     DateFactureStart = 
15/07/2025 : 14:30:23.980 :     DateFactureEnd   = 
15/07/2025 : 14:30:23.980 :     NO SORTING: Using default sort
15/07/2025 : 14:30:24.034 :     Items retrieved: 14
15/07/2025 : 14:30:25.863 :     Count = 8832132
15/07/2025 : 14:30:25.865 : Leaving
15/07/2025 : 14:32:49.543 : Entering
15/07/2025 : 14:32:49.546 :     Client           = 
15/07/2025 : 14:32:49.546 :     DateFactureStart = 
15/07/2025 : 14:32:49.547 :     DateFactureEnd   = 
15/07/2025 : 14:32:49.547 :     NO SORTING: Using default sort
15/07/2025 : 14:32:51.955 :     Items retrieved: 14
15/07/2025 : 14:32:54.350 :     Count = 8832132
15/07/2025 : 14:32:54.351 : Leaving
15/07/2025 : 14:32:54.708 : Entering
15/07/2025 : 14:32:54.709 :     Client           = 
15/07/2025 : 14:32:54.709 :     DateFactureStart = 
15/07/2025 : 14:32:54.709 :     DateFactureEnd   = 
15/07/2025 : 14:32:54.710 :     NO SORTING: Using default sort
15/07/2025 : 14:32:54.869 :     Items retrieved: 14
15/07/2025 : 14:32:57.718 :     Count = 8832132
15/07/2025 : 14:32:57.719 : Leaving
15/07/2025 : 14:33:14.277 : Entering
15/07/2025 : 14:33:14.277 :     Client           = 
15/07/2025 : 14:33:14.277 :     DateFactureStart = 
15/07/2025 : 14:33:14.278 :     DateFactureEnd   = 
15/07/2025 : 14:33:14.278 :     NO SORTING: Using default sort
15/07/2025 : 14:33:14.336 :     Items retrieved: 14
15/07/2025 : 14:33:16.486 :     Count = 8832132
15/07/2025 : 14:33:16.486 : Leaving
15/07/2025 : 14:33:16.577 : Entering
15/07/2025 : 14:33:16.578 :     Client           = 
15/07/2025 : 14:33:16.578 :     DateFactureStart = 
15/07/2025 : 14:33:16.578 :     DateFactureEnd   = 
15/07/2025 : 14:33:16.578 :     NO SORTING: Using default sort
15/07/2025 : 14:33:16.636 :     Items retrieved: 14
15/07/2025 : 14:33:18.588 :     Count = 8832132
15/07/2025 : 14:33:18.589 : Leaving
15/07/2025 : 14:33:29.518 : Entering
15/07/2025 : 14:33:29.519 :     Client           = 
15/07/2025 : 14:33:29.520 :     DateFactureStart = 
15/07/2025 : 14:33:29.520 :     DateFactureEnd   = 
15/07/2025 : 14:33:29.521 :     NO SORTING: Using default sort
15/07/2025 : 14:33:29.578 :     Items retrieved: 14
15/07/2025 : 14:33:32.088 :     Count = 8832132
15/07/2025 : 14:33:32.089 : Leaving
15/07/2025 : 14:33:33.480 : Entering
15/07/2025 : 14:33:33.481 :     Client           = 
15/07/2025 : 14:33:33.482 :     DateFactureStart = 
15/07/2025 : 14:33:33.483 :     DateFactureEnd   = 
15/07/2025 : 14:33:33.484 :     NO SORTING: Using default sort
15/07/2025 : 14:33:33.545 :     Items retrieved: 14
15/07/2025 : 14:33:35.581 :     Count = 8832132
15/07/2025 : 14:33:35.582 : Leaving
15/07/2025 : 14:33:35.645 : Entering
15/07/2025 : 14:33:35.646 :     Client           = 
15/07/2025 : 14:33:35.646 :     DateFactureStart = 
15/07/2025 : 14:33:35.647 :     DateFactureEnd   = 
15/07/2025 : 14:33:35.647 :     NO SORTING: Using default sort
15/07/2025 : 14:33:35.703 :     Items retrieved: 14
15/07/2025 : 14:33:37.435 :     Count = 8832132
15/07/2025 : 14:33:37.436 : Leaving
15/07/2025 : 14:33:37.566 : Entering
15/07/2025 : 14:33:37.568 :     Client           = 
15/07/2025 : 14:33:37.569 :     DateFactureStart = 
15/07/2025 : 14:33:37.569 :     DateFactureEnd   = 
15/07/2025 : 14:33:37.571 :     NO SORTING: Using default sort
15/07/2025 : 14:33:37.635 :     Items retrieved: 14
15/07/2025 : 14:33:39.942 :     Count = 8832132
15/07/2025 : 14:33:39.943 : Leaving
15/07/2025 : 14:33:43.524 : Entering
15/07/2025 : 14:33:43.526 :     Client           = 
15/07/2025 : 14:33:43.527 :     DateFactureStart = 
15/07/2025 : 14:33:43.528 :     DateFactureEnd   = 
15/07/2025 : 14:33:43.528 :     NO SORTING: Using default sort
15/07/2025 : 14:33:43.587 :     Items retrieved: 14
15/07/2025 : 14:33:45.505 :     Count = 8832132
15/07/2025 : 14:33:45.506 : Leaving
15/07/2025 : 14:33:45.568 : Entering
15/07/2025 : 14:33:45.569 :     Client           = 
15/07/2025 : 14:33:45.570 :     DateFactureStart = 
15/07/2025 : 14:33:45.570 :     DateFactureEnd   = 
15/07/2025 : 14:33:45.571 :     NO SORTING: Using default sort
15/07/2025 : 14:33:45.630 :     Items retrieved: 14
15/07/2025 : 14:33:47.603 :     Count = 8832132
15/07/2025 : 14:33:47.604 : Leaving
15/07/2025 : 14:33:50.048 : Entering
15/07/2025 : 14:33:50.049 :     Client           = 
15/07/2025 : 14:33:50.050 :     DateFactureStart = 
15/07/2025 : 14:33:50.051 :     DateFactureEnd   = 
15/07/2025 : 14:33:50.052 :     NO SORTING: Using default sort
15/07/2025 : 14:33:50.119 :     Items retrieved: 14
15/07/2025 : 14:33:53.034 :     Count = 8832132
15/07/2025 : 14:33:53.035 : Leaving
15/07/2025 : 14:33:54.026 : Entering
15/07/2025 : 14:33:54.027 :     Client           = 
15/07/2025 : 14:33:54.028 :     DateFactureStart = 
15/07/2025 : 14:33:54.029 :     DateFactureEnd   = 
15/07/2025 : 14:33:54.030 :     NO SORTING: Using default sort
15/07/2025 : 14:33:54.092 :     Items retrieved: 14
15/07/2025 : 14:33:56.053 :     Count = 8832132
15/07/2025 : 14:33:56.054 : Leaving
15/07/2025 : 14:33:56.216 : Entering
15/07/2025 : 14:33:56.217 :     Client           = 
15/07/2025 : 14:33:56.218 :     DateFactureStart = 
15/07/2025 : 14:33:56.218 :     DateFactureEnd   = 
15/07/2025 : 14:33:56.219 :     NO SORTING: Using default sort
15/07/2025 : 14:33:56.280 :     Items retrieved: 14
15/07/2025 : 14:33:58.310 :     Count = 8832132
15/07/2025 : 14:33:58.311 : Leaving
15/07/2025 : 14:33:58.384 : Entering
15/07/2025 : 14:33:58.386 :     Client           = 
15/07/2025 : 14:33:58.387 :     DateFactureStart = 
15/07/2025 : 14:33:58.388 :     DateFactureEnd   = 
15/07/2025 : 14:33:58.388 :     NO SORTING: Using default sort
15/07/2025 : 14:33:58.452 :     Items retrieved: 14
15/07/2025 : 14:34:00.990 :     Count = 8832132
15/07/2025 : 14:34:00.991 : Leaving
15/07/2025 : 14:34:01.088 : Entering
15/07/2025 : 14:34:01.090 :     Client           = 
15/07/2025 : 14:34:01.090 :     DateFactureStart = 
15/07/2025 : 14:34:01.092 :     DateFactureEnd   = 
15/07/2025 : 14:34:01.093 :     NO SORTING: Using default sort
15/07/2025 : 14:34:01.163 :     Items retrieved: 14
15/07/2025 : 14:34:04.217 :     Count = 8832132
15/07/2025 : 14:34:04.219 : Leaving
15/07/2025 : 14:34:04.256 : Entering
15/07/2025 : 14:34:04.256 :     Client           = 
15/07/2025 : 14:34:04.256 :     DateFactureStart = 
15/07/2025 : 14:34:04.256 :     DateFactureEnd   = 
15/07/2025 : 14:34:04.257 :     NO SORTING: Using default sort
15/07/2025 : 14:34:04.314 :     Items retrieved: 14
15/07/2025 : 14:34:06.102 :     Count = 8832132
15/07/2025 : 14:34:06.104 : Leaving
15/07/2025 : 14:34:06.250 : Entering
15/07/2025 : 14:34:06.251 :     Client           = 
15/07/2025 : 14:34:06.252 :     DateFactureStart = 
15/07/2025 : 14:34:06.253 :     DateFactureEnd   = 
15/07/2025 : 14:34:06.254 :     NO SORTING: Using default sort
15/07/2025 : 14:34:06.318 :     Items retrieved: 14
15/07/2025 : 14:34:08.419 :     Count = 8832132
15/07/2025 : 14:34:08.421 : Leaving
15/07/2025 : 14:42:07.088 : Entering
15/07/2025 : 14:42:07.091 :     Client           = 
15/07/2025 : 14:42:07.092 :     DateFactureStart = 
15/07/2025 : 14:42:07.092 :     DateFactureEnd   = 
15/07/2025 : 14:42:07.092 :     NO SORTING: Using default sort
15/07/2025 : 14:42:08.841 :     Items retrieved: 14
15/07/2025 : 14:42:10.995 :     Count = 8832132
15/07/2025 : 14:42:10.997 : Leaving
15/07/2025 : 14:42:11.307 : Entering
15/07/2025 : 14:42:11.307 :     Client           = 
15/07/2025 : 14:42:11.308 :     DateFactureStart = 
15/07/2025 : 14:42:11.308 :     DateFactureEnd   = 
15/07/2025 : 14:42:11.309 :     NO SORTING: Using default sort
15/07/2025 : 14:42:11.426 :     Items retrieved: 14
15/07/2025 : 14:42:13.356 :     Count = 8832132
15/07/2025 : 14:42:13.359 : Leaving
15/07/2025 : 14:42:25.628 : Entering
15/07/2025 : 14:42:25.629 :     Client           = 
15/07/2025 : 14:42:25.629 :     DateFactureStart = 
15/07/2025 : 14:42:25.629 :     DateFactureEnd   = 
15/07/2025 : 14:42:25.630 :     NO SORTING: Using default sort
15/07/2025 : 14:42:25.701 :     Items retrieved: 14
15/07/2025 : 14:42:27.603 :     Count = 8832132
15/07/2025 : 14:42:27.605 : Leaving
15/07/2025 : 14:42:27.764 : Entering
15/07/2025 : 14:42:27.765 :     Client           = 
15/07/2025 : 14:42:27.765 :     DateFactureStart = 
15/07/2025 : 14:42:27.765 :     DateFactureEnd   = 
15/07/2025 : 14:42:27.765 :     NO SORTING: Using default sort
15/07/2025 : 14:42:27.824 :     Items retrieved: 14
15/07/2025 : 14:42:29.636 :     Count = 8832132
15/07/2025 : 14:42:29.637 : Leaving
15/07/2025 : 14:49:54.423 : Entering
15/07/2025 : 14:49:54.425 :     Client           = 
15/07/2025 : 14:49:54.426 :     DateFactureStart = 
15/07/2025 : 14:49:54.426 :     DateFactureEnd   = 
15/07/2025 : 14:49:54.426 :     NO SORTING: Using default sort
15/07/2025 : 14:49:56.133 :     Items retrieved: 14
15/07/2025 : 14:49:59.451 :     Count = 8832132
15/07/2025 : 14:49:59.452 : Leaving
15/07/2025 : 14:49:59.744 : Entering
15/07/2025 : 14:49:59.744 :     Client           = 
15/07/2025 : 14:49:59.745 :     DateFactureStart = 
15/07/2025 : 14:49:59.745 :     DateFactureEnd   = 
15/07/2025 : 14:49:59.745 :     NO SORTING: Using default sort
15/07/2025 : 14:49:59.877 :     Items retrieved: 14
15/07/2025 : 14:50:02.435 :     Count = 8832132
15/07/2025 : 14:50:02.435 : Leaving
15/07/2025 : 14:50:19.658 : Entering
15/07/2025 : 14:50:19.661 :     Client           = 
15/07/2025 : 14:50:19.662 :     DateFactureStart = 
15/07/2025 : 14:50:19.662 :     DateFactureEnd   = 
15/07/2025 : 14:50:19.662 :     NO SORTING: Using default sort
15/07/2025 : 14:50:19.733 :     Items retrieved: 14
15/07/2025 : 14:50:21.811 :     Count = 8832132
15/07/2025 : 14:50:21.812 : Leaving
15/07/2025 : 14:50:21.892 : Entering
15/07/2025 : 14:50:21.893 :     Client           = 
15/07/2025 : 14:50:21.893 :     DateFactureStart = 
15/07/2025 : 14:50:21.893 :     DateFactureEnd   = 
15/07/2025 : 14:50:21.894 :     NO SORTING: Using default sort
15/07/2025 : 14:50:21.953 :     Items retrieved: 14
15/07/2025 : 14:50:23.909 :     Count = 8832132
15/07/2025 : 14:50:23.910 : Leaving
15/07/2025 : 14:50:25.187 : Entering
15/07/2025 : 14:50:25.188 :     Client           = 
15/07/2025 : 14:50:25.188 :     DateFactureStart = 
15/07/2025 : 14:50:25.189 :     DateFactureEnd   = 
15/07/2025 : 14:50:25.189 :     NO SORTING: Using default sort
15/07/2025 : 14:50:25.251 :     Items retrieved: 14
15/07/2025 : 14:50:27.224 :     Count = 8832132
15/07/2025 : 14:50:27.226 : Leaving
15/07/2025 : 14:50:27.281 : Entering
15/07/2025 : 14:50:27.281 :     Client           = 
15/07/2025 : 14:50:27.281 :     DateFactureStart = 
15/07/2025 : 14:50:27.282 :     DateFactureEnd   = 
15/07/2025 : 14:50:27.282 :     NO SORTING: Using default sort
15/07/2025 : 14:50:27.343 :     Items retrieved: 14
15/07/2025 : 14:50:29.125 :     Count = 8832132
15/07/2025 : 14:50:29.126 : Leaving
15/07/2025 : 14:50:29.238 : Entering
15/07/2025 : 14:50:29.238 :     Client           = 
15/07/2025 : 14:50:29.239 :     DateFactureStart = 
15/07/2025 : 14:50:29.239 :     DateFactureEnd   = 
15/07/2025 : 14:50:29.239 :     NO SORTING: Using default sort
15/07/2025 : 14:50:29.300 :     Items retrieved: 14
15/07/2025 : 14:50:31.447 :     Count = 8832132
15/07/2025 : 14:50:31.448 : Leaving
15/07/2025 : 14:50:31.503 : Entering
15/07/2025 : 14:50:31.504 :     Client           = 
15/07/2025 : 14:50:31.504 :     DateFactureStart = 
15/07/2025 : 14:50:31.505 :     DateFactureEnd   = 
15/07/2025 : 14:50:31.505 :     NO SORTING: Using default sort
15/07/2025 : 14:50:31.565 :     Items retrieved: 14
15/07/2025 : 14:50:33.531 :     Count = 8832132
15/07/2025 : 14:50:33.532 : Leaving
15/07/2025 : 14:50:39.230 : Entering
15/07/2025 : 14:50:39.231 :     Client           = 
15/07/2025 : 14:50:39.231 :     DateFactureStart = 
15/07/2025 : 14:50:39.233 :     DateFactureEnd   = 18/07/2025 00:00:00
15/07/2025 : 14:50:39.234 :     NO SORTING: Using default sort
15/07/2025 : 14:51:09.423 :     Items retrieved: 0
15/07/2025 : 14:51:11.492 :     Count = 0
15/07/2025 : 14:51:11.492 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 14:51:11.493 : Leaving
15/07/2025 : 14:55:46.957 : Entering
15/07/2025 : 14:55:46.959 :     Client           = 
15/07/2025 : 14:55:46.959 :     DateFactureStart = 
15/07/2025 : 14:55:46.960 :     DateFactureEnd   = 
15/07/2025 : 14:55:46.960 :     NO SORTING: Using default sort
15/07/2025 : 14:55:48.673 :     Items retrieved: 14
15/07/2025 : 14:55:49.191 :     Entering
15/07/2025 : 14:55:49.192 :         Client           = 
15/07/2025 : 14:55:49.192 :         DateFactureStart = 
15/07/2025 : 14:55:49.193 :         DateFactureEnd   = 
15/07/2025 : 14:55:49.193 :         NO SORTING: Using default sort
15/07/2025 : 14:55:49.813 :         Items retrieved: 14
15/07/2025 : 14:55:52.677 :         Count = 8832132
15/07/2025 : 14:55:52.679 :     Leaving
15/07/2025 : 14:55:53.093 :     Count = 8832132
15/07/2025 : 14:55:53.093 : Leaving
15/07/2025 : 14:55:53.298 : Entering
15/07/2025 : 14:55:53.299 :     Client           = 
15/07/2025 : 14:55:53.299 :     DateFactureStart = 
15/07/2025 : 14:55:53.300 :     DateFactureEnd   = 
15/07/2025 : 14:55:53.300 :     NO SORTING: Using default sort
15/07/2025 : 14:55:53.366 :     Items retrieved: 14
15/07/2025 : 14:55:55.227 :     Count = 8832132
15/07/2025 : 14:55:55.228 : Leaving
15/07/2025 : 14:56:07.881 : Entering
15/07/2025 : 14:56:07.882 :     Client           = 
15/07/2025 : 14:56:07.882 :     DateFactureStart = 
15/07/2025 : 14:56:07.883 :     DateFactureEnd   = 
15/07/2025 : 14:56:07.883 :     NO SORTING: Using default sort
15/07/2025 : 14:56:07.963 :     Items retrieved: 14
15/07/2025 : 14:56:10.416 :     Count = 8832132
15/07/2025 : 14:56:10.418 : Leaving
15/07/2025 : 14:56:13.630 : Entering
15/07/2025 : 14:56:13.631 :     Client           = 
15/07/2025 : 14:56:13.631 :     DateFactureStart = 
15/07/2025 : 14:56:13.632 :     DateFactureEnd   = 
15/07/2025 : 14:56:13.632 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 14:56:14.640 :     Items retrieved: 14
15/07/2025 : 14:56:16.563 :     Count = 8832132
15/07/2025 : 14:56:16.564 : Leaving
15/07/2025 : 14:56:16.651 : Entering
15/07/2025 : 14:56:16.652 :     Client           = 
15/07/2025 : 14:56:16.656 :     DateFactureStart = 
15/07/2025 : 14:56:16.656 :     DateFactureEnd   = 
15/07/2025 : 14:56:16.657 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 14:56:17.385 :     Items retrieved: 14
15/07/2025 : 14:56:19.179 :     Count = 8832132
15/07/2025 : 14:56:19.179 : Leaving
15/07/2025 : 14:56:53.162 : Entering
15/07/2025 : 14:56:53.162 :     Client           = 
15/07/2025 : 14:56:53.163 :     DateFactureStart = 
15/07/2025 : 14:56:53.163 :     DateFactureEnd   = 
15/07/2025 : 14:56:53.163 :     NO SORTING: Using default sort
15/07/2025 : 14:56:53.224 :     Items retrieved: 14
15/07/2025 : 14:56:55.393 :     Count = 8832132
15/07/2025 : 14:56:55.394 : Leaving
15/07/2025 : 15:02:03.398 : Entering
15/07/2025 : 15:02:03.400 :     Client           = 
15/07/2025 : 15:02:03.401 :     DateFactureStart = 
15/07/2025 : 15:02:03.401 :     DateFactureEnd   = 
15/07/2025 : 15:02:03.401 :     NO SORTING: Using default sort
15/07/2025 : 15:02:05.090 :     Items retrieved: 14
15/07/2025 : 15:02:07.285 :     Count = 8832132
15/07/2025 : 15:02:07.287 : Leaving
15/07/2025 : 15:02:07.618 : Entering
15/07/2025 : 15:02:07.619 :     Client           = 
15/07/2025 : 15:02:07.619 :     DateFactureStart = 
15/07/2025 : 15:02:07.619 :     DateFactureEnd   = 
15/07/2025 : 15:02:07.620 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 15:02:08.727 :     Items retrieved: 14
15/07/2025 : 15:02:10.957 :     Count = 8832132
15/07/2025 : 15:02:10.958 : Leaving
15/07/2025 : 15:04:30.662 : Entering
15/07/2025 : 15:04:30.667 :     Client           = 
15/07/2025 : 15:04:30.668 :     DateFactureStart = 
15/07/2025 : 15:04:30.668 :     DateFactureEnd   = 
15/07/2025 : 15:04:30.669 :     NO SORTING: Using default sort
15/07/2025 : 15:04:34.849 :     Items retrieved: 14
15/07/2025 : 15:04:36.967 :     Count = 8832132
15/07/2025 : 15:04:36.969 : Leaving
15/07/2025 : 15:04:37.721 : Entering
15/07/2025 : 15:04:37.722 :     Client           = 
15/07/2025 : 15:04:37.723 :     DateFactureStart = 
15/07/2025 : 15:04:37.723 :     DateFactureEnd   = 
15/07/2025 : 15:04:37.724 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 15:04:39.187 :     Items retrieved: 14
15/07/2025 : 15:04:41.185 :     Count = 8832132
15/07/2025 : 15:04:41.186 : Leaving
15/07/2025 : 15:04:42.897 : Entering
15/07/2025 : 15:04:42.898 :     Client           = 
15/07/2025 : 15:04:42.898 :     DateFactureStart = 
15/07/2025 : 15:04:42.899 :     DateFactureEnd   = 
15/07/2025 : 15:04:42.899 :     NO SORTING: Using default sort
15/07/2025 : 15:04:42.961 :     Items retrieved: 14
15/07/2025 : 15:04:44.797 :     Count = 8832132
15/07/2025 : 15:04:44.798 : Leaving
15/07/2025 : 15:04:44.988 : Entering
15/07/2025 : 15:04:44.989 :     Client           = 
15/07/2025 : 15:04:44.990 :     DateFactureStart = 
15/07/2025 : 15:04:44.991 :     DateFactureEnd   = 
15/07/2025 : 15:04:44.992 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 15:04:45.999 :     Items retrieved: 14
15/07/2025 : 15:04:48.183 :     Count = 8832132
15/07/2025 : 15:04:48.184 : Leaving
15/07/2025 : 15:05:09.806 : Entering
15/07/2025 : 15:05:09.807 :     Client           = 
15/07/2025 : 15:05:09.807 :     DateFactureStart = 
15/07/2025 : 15:05:09.808 :     DateFactureEnd   = 
15/07/2025 : 15:05:09.808 :     NO SORTING: Using default sort
15/07/2025 : 15:05:09.870 :     Items retrieved: 14
15/07/2025 : 15:05:11.639 :     Count = 8832132
15/07/2025 : 15:05:11.640 : Leaving
15/07/2025 : 15:05:19.264 : Entering
15/07/2025 : 15:05:19.265 :     Client           = 0026
15/07/2025 : 15:05:19.265 :     DateFactureStart = 
15/07/2025 : 15:05:19.266 :     DateFactureEnd   = 
15/07/2025 : 15:05:19.267 :     NO SORTING: Using default sort
15/07/2025 : 15:05:20.533 :     Items retrieved: 14
15/07/2025 : 15:05:22.350 :     Count = 55904
15/07/2025 : 15:05:22.351 : Leaving
15/07/2025 : 15:05:29.479 : Entering
15/07/2025 : 15:05:29.480 :     Client           = 0026
15/07/2025 : 15:05:29.481 :     DateFactureStart = 
15/07/2025 : 15:05:29.482 :     DateFactureEnd   = 
15/07/2025 : 15:05:29.483 :     NO SORTING: Using default sort
15/07/2025 : 15:05:31.766 :     Items retrieved: 14
15/07/2025 : 15:05:33.760 :     Count = 55904
15/07/2025 : 15:05:33.761 : Leaving
15/07/2025 : 15:06:11.727 : Entering
15/07/2025 : 15:06:11.728 :     Client           = 0026
15/07/2025 : 15:06:11.728 :     DateFactureStart = 
15/07/2025 : 15:06:11.729 :     DateFactureEnd   = 
15/07/2025 : 15:06:11.730 :     NO SORTING: Using default sort
15/07/2025 : 15:06:13.674 :     Items retrieved: 14
15/07/2025 : 15:06:15.546 :     Count = 55904
15/07/2025 : 15:06:15.547 : Leaving
15/07/2025 : 15:07:19.654 : Entering
15/07/2025 : 15:07:19.656 :     Client           = 
15/07/2025 : 15:07:19.656 :     DateFactureStart = 
15/07/2025 : 15:07:19.657 :     DateFactureEnd   = 
15/07/2025 : 15:07:19.657 :     NO SORTING: Using default sort
15/07/2025 : 15:07:21.441 :     Items retrieved: 14
15/07/2025 : 15:07:23.526 :     Count = 8832132
15/07/2025 : 15:07:23.527 : Leaving
15/07/2025 : 15:07:23.910 : Entering
15/07/2025 : 15:07:23.910 :     Client           = 
15/07/2025 : 15:07:23.911 :     DateFactureStart = 
15/07/2025 : 15:07:23.911 :     DateFactureEnd   = 
15/07/2025 : 15:07:23.911 :     NO SORTING: Using default sort
15/07/2025 : 15:07:24.039 :     Items retrieved: 14
15/07/2025 : 15:07:25.893 :     Count = 8832132
15/07/2025 : 15:07:25.894 : Leaving
15/07/2025 : 15:09:29.264 : Entering
15/07/2025 : 15:09:29.266 :     Client           = 
15/07/2025 : 15:09:29.266 :     DateFactureStart = 
15/07/2025 : 15:09:29.267 :     DateFactureEnd   = 
15/07/2025 : 15:09:29.267 :     NO SORTING: Using default sort
15/07/2025 : 15:09:31.084 :     Items retrieved: 14
15/07/2025 : 15:09:33.160 :     Count = 8832132
15/07/2025 : 15:09:33.161 : Leaving
15/07/2025 : 15:09:33.479 : Entering
15/07/2025 : 15:09:33.480 :     Client           = 
15/07/2025 : 15:09:33.480 :     DateFactureStart = 
15/07/2025 : 15:09:33.481 :     DateFactureEnd   = 
15/07/2025 : 15:09:33.481 :     NO SORTING: Using default sort
15/07/2025 : 15:09:33.614 :     Items retrieved: 14
15/07/2025 : 15:09:35.493 :     Count = 8832132
15/07/2025 : 15:09:35.494 : Leaving
15/07/2025 : 15:10:42.117 : Entering
15/07/2025 : 15:10:42.119 :     Client           = 
15/07/2025 : 15:10:42.120 :     DateFactureStart = 
15/07/2025 : 15:10:42.121 :     DateFactureEnd   = 
15/07/2025 : 15:10:42.121 :     NO SORTING: Using default sort
15/07/2025 : 15:10:44.704 :     Items retrieved: 14
15/07/2025 : 15:10:46.593 :     Count = 8832132
15/07/2025 : 15:10:46.593 : Leaving
15/07/2025 : 15:10:47.170 : Entering
15/07/2025 : 15:10:47.171 :     Client           = 
15/07/2025 : 15:10:47.172 :     DateFactureStart = 
15/07/2025 : 15:10:47.173 :     DateFactureEnd   = 
15/07/2025 : 15:10:47.174 :     NO SORTING: Using default sort
15/07/2025 : 15:10:47.390 :     Items retrieved: 14
15/07/2025 : 15:10:49.230 :     Count = 8832132
15/07/2025 : 15:10:49.231 : Leaving
15/07/2025 : 15:10:58.863 : Entering
15/07/2025 : 15:10:58.864 :     Client           = 
15/07/2025 : 15:10:58.865 :     DateFactureStart = 
15/07/2025 : 15:10:58.865 :     DateFactureEnd   = 
15/07/2025 : 15:10:58.866 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 15:11:29.115 :     Items retrieved: 0
15/07/2025 : 15:11:31.348 :     Count = 8832132
15/07/2025 : 15:11:31.349 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 15:11:31.349 : Leaving
15/07/2025 : 15:11:31.351 : Entering
15/07/2025 : 15:11:31.351 :     Client           = 
15/07/2025 : 15:11:31.352 :     DateFactureStart = 
15/07/2025 : 15:11:31.352 :     DateFactureEnd   = 
15/07/2025 : 15:11:31.353 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 15:12:01.558 :     Items retrieved: 0
15/07/2025 : 15:12:04.108 :     Count = 8832132
15/07/2025 : 15:12:04.109 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 15:12:04.110 : Leaving
15/07/2025 : 15:12:04.126 : Entering
15/07/2025 : 15:12:04.126 :     Client           = 
15/07/2025 : 15:12:04.127 :     DateFactureStart = 
15/07/2025 : 15:12:04.127 :     DateFactureEnd   = 
15/07/2025 : 15:12:04.127 :     SORTING: Column = montant, Direction = descending
15/07/2025 : 15:12:34.354 :     Items retrieved: 0
15/07/2025 : 15:12:36.299 :     Count = 8832132
15/07/2025 : 15:12:36.300 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 15:12:36.301 : Leaving
15/07/2025 : 15:12:36.303 : Entering
15/07/2025 : 15:12:36.305 :     Client           = 
15/07/2025 : 15:12:36.306 :     DateFactureStart = 
15/07/2025 : 15:12:36.308 :     DateFactureEnd   = 
15/07/2025 : 15:12:36.309 :     SORTING: Column = montant, Direction = descending
15/07/2025 : 15:13:06.899 :     Items retrieved: 0
15/07/2025 : 15:13:08.994 :     Count = 8832132
15/07/2025 : 15:13:08.996 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 15:13:08.997 : Leaving
15/07/2025 : 15:13:09.017 : Entering
15/07/2025 : 15:13:09.018 :     Client           = 
15/07/2025 : 15:13:09.020 :     DateFactureStart = 
15/07/2025 : 15:13:09.021 :     DateFactureEnd   = 
15/07/2025 : 15:13:09.022 :     NO SORTING: Using default sort
15/07/2025 : 15:13:39.119 :     Items retrieved: 0
15/07/2025 : 15:13:40.949 :     Count = 8832132
15/07/2025 : 15:13:40.950 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 15:13:40.951 : Leaving
15/07/2025 : 15:13:40.953 : Entering
15/07/2025 : 15:13:40.954 :     Client           = 
15/07/2025 : 15:13:40.955 :     DateFactureStart = 
15/07/2025 : 15:13:40.955 :     DateFactureEnd   = 
15/07/2025 : 15:13:40.956 :     NO SORTING: Using default sort
15/07/2025 : 15:14:11.040 :     Items retrieved: 0
15/07/2025 : 15:14:13.006 :     Count = 8832132
15/07/2025 : 15:14:13.007 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 15:14:13.008 : Leaving
15/07/2025 : 15:14:13.016 : Entering
15/07/2025 : 15:14:13.017 :     Client           = 
15/07/2025 : 15:14:13.017 :     DateFactureStart = 
15/07/2025 : 15:14:13.017 :     DateFactureEnd   = 
15/07/2025 : 15:14:13.018 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 15:14:43.264 :     Items retrieved: 0
15/07/2025 : 15:14:45.436 :     Count = 8832132
15/07/2025 : 15:14:45.437 :     WARNING: No items returned from Helper_GetData
15/07/2025 : 15:14:45.438 : Leaving
15/07/2025 : 15:14:45.439 : Entering
15/07/2025 : 15:14:45.440 :     Client           = 
15/07/2025 : 15:14:45.441 :     DateFactureStart = 
15/07/2025 : 15:14:45.442 :     DateFactureEnd   = 
15/07/2025 : 15:14:45.442 :     SORTING: Column = montant, Direction = ascending
15/07/2025 : 15:15:27.246 : Entering
15/07/2025 : 15:15:27.248 :     Client           = 
15/07/2025 : 15:15:27.249 :     DateFactureStart = 
15/07/2025 : 15:15:27.249 :     DateFactureEnd   = 
15/07/2025 : 15:15:27.249 :     NO SORTING: Using default sort
15/07/2025 : 15:15:28.833 :     Items retrieved: 14
15/07/2025 : 15:15:31.166 :     Count = 8832132
15/07/2025 : 15:15:31.168 : Leaving
15/07/2025 : 15:15:31.474 : Entering
15/07/2025 : 15:15:31.475 :     Client           = 
15/07/2025 : 15:15:31.475 :     DateFactureStart = 
15/07/2025 : 15:15:31.475 :     DateFactureEnd   = 
15/07/2025 : 15:15:31.476 :     NO SORTING: Using default sort
15/07/2025 : 15:15:31.624 :     Items retrieved: 14
15/07/2025 : 15:15:33.695 :     Count = 8832132
15/07/2025 : 15:15:33.696 : Leaving
15/07/2025 : 15:15:54.299 : Entering
15/07/2025 : 15:15:54.299 :     Client           = 
15/07/2025 : 15:15:54.299 :     DateFactureStart = 
15/07/2025 : 15:15:54.300 :     DateFactureEnd   = 
15/07/2025 : 15:15:54.300 :     NO SORTING: Using default sort
15/07/2025 : 15:15:54.359 :     Items retrieved: 14
15/07/2025 : 15:15:56.744 :     Count = 8832132
15/07/2025 : 15:15:56.745 : Leaving
15/07/2025 : 15:15:56.857 : Entering
15/07/2025 : 15:15:56.858 :     Client           = 
15/07/2025 : 15:15:56.859 :     DateFactureStart = 
15/07/2025 : 15:15:56.859 :     DateFactureEnd   = 
15/07/2025 : 15:15:56.859 :     NO SORTING: Using default sort
15/07/2025 : 15:15:56.952 :     Items retrieved: 14
15/07/2025 : 15:15:59.114 :     Count = 8832132
15/07/2025 : 15:15:59.115 : Leaving
15/07/2025 : 15:16:57.596 : Entering
15/07/2025 : 15:16:57.597 :     Client           = 
15/07/2025 : 15:16:57.597 :     DateFactureStart = 
15/07/2025 : 15:16:57.598 :     DateFactureEnd   = 
15/07/2025 : 15:16:57.598 :     NO SORTING: Using default sort
15/07/2025 : 15:16:57.657 :     Items retrieved: 14
15/07/2025 : 15:16:59.958 :     Count = 8832132
15/07/2025 : 15:16:59.958 : Leaving
15/07/2025 : 15:17:00.047 : Entering
15/07/2025 : 15:17:00.048 :     Client           = 
15/07/2025 : 15:17:00.048 :     DateFactureStart = 
15/07/2025 : 15:17:00.049 :     DateFactureEnd   = 
15/07/2025 : 15:17:00.049 :     NO SORTING: Using default sort
15/07/2025 : 15:17:00.233 :     Items retrieved: 14
15/07/2025 : 15:17:02.479 :     Count = 8832132
15/07/2025 : 15:17:02.481 : Leaving
15/07/2025 : 15:18:44.124 : Entering
15/07/2025 : 15:18:44.126 :     Client           = 
15/07/2025 : 15:18:44.127 :     DateFactureStart = 
15/07/2025 : 15:18:44.127 :     DateFactureEnd   = 
15/07/2025 : 15:18:44.127 :     NO SORTING: Using default sort
15/07/2025 : 15:18:45.920 :     Items retrieved: 14
15/07/2025 : 15:18:48.159 :     Count = 8832132
15/07/2025 : 15:18:48.160 : Leaving
15/07/2025 : 15:18:48.491 : Entering
15/07/2025 : 15:18:48.492 :     Client           = 
15/07/2025 : 15:18:48.492 :     DateFactureStart = 
15/07/2025 : 15:18:48.492 :     DateFactureEnd   = 
15/07/2025 : 15:18:48.493 :     NO SORTING: Using default sort
15/07/2025 : 15:18:48.616 :     Items retrieved: 14
15/07/2025 : 15:18:50.702 :     Count = 8832132
15/07/2025 : 15:18:50.703 : Leaving
15/07/2025 : 15:19:52.098 : Entering
15/07/2025 : 15:19:52.099 :     Client           = 
15/07/2025 : 15:19:52.100 :     DateFactureStart = 
15/07/2025 : 15:19:52.100 :     DateFactureEnd   = 
15/07/2025 : 15:19:52.100 :     NO SORTING: Using default sort
15/07/2025 : 15:19:53.889 :     Items retrieved: 14
15/07/2025 : 15:19:58.868 :     Count = 8832132
15/07/2025 : 15:19:58.871 : Leaving
15/07/2025 : 15:19:59.225 : Entering
15/07/2025 : 15:19:59.225 :     Client           = 
15/07/2025 : 15:19:59.226 :     DateFactureStart = 
15/07/2025 : 15:19:59.226 :     DateFactureEnd   = 
15/07/2025 : 15:19:59.226 :     NO SORTING: Using default sort
15/07/2025 : 15:19:59.367 :     Items retrieved: 14
15/07/2025 : 15:20:02.126 :     Count = 8832132
15/07/2025 : 15:20:02.126 : Leaving
15/07/2025 : 15:20:05.676 : Entering
15/07/2025 : 15:20:05.677 :     Client           = 
15/07/2025 : 15:20:05.677 :     DateFactureStart = 
15/07/2025 : 15:20:05.678 :     DateFactureEnd   = 
15/07/2025 : 15:20:05.678 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 15:20:07.048 :     Items retrieved: 14
15/07/2025 : 15:20:09.212 :     Count = 8832132
15/07/2025 : 15:20:09.213 : Leaving
15/07/2025 : 15:20:09.247 : Entering
15/07/2025 : 15:20:09.247 :     Client           = 
15/07/2025 : 15:20:09.248 :     DateFactureStart = 
15/07/2025 : 15:20:09.248 :     DateFactureEnd   = 
15/07/2025 : 15:20:09.248 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 15:20:10.333 :     Items retrieved: 14
15/07/2025 : 15:20:13.006 :     Count = 8832132
15/07/2025 : 15:20:13.007 : Leaving
15/07/2025 : 21:06:34.533 : Entering
15/07/2025 : 21:06:34.539 :     Client           = 
15/07/2025 : 21:06:34.540 :     DateFactureStart = 
15/07/2025 : 21:06:34.540 :     DateFactureEnd   = 
15/07/2025 : 21:06:34.541 :     NO SORTING: Using default sort
15/07/2025 : 21:06:36.856 :     Items retrieved: 14
15/07/2025 : 21:06:39.337 :     Count = 8832132
15/07/2025 : 21:06:39.338 : Leaving
15/07/2025 : 21:06:39.718 : Entering
15/07/2025 : 21:06:39.718 :     Client           = 
15/07/2025 : 21:06:39.719 :     DateFactureStart = 
15/07/2025 : 21:06:39.719 :     DateFactureEnd   = 
15/07/2025 : 21:06:39.720 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 21:06:40.874 :     Items retrieved: 14
15/07/2025 : 21:06:42.916 :     Count = 8832132
15/07/2025 : 21:06:42.918 : Leaving
15/07/2025 : 21:19:55.080 : Entering
15/07/2025 : 21:19:55.082 :     Client           = 
15/07/2025 : 21:19:55.083 :     DateFactureStart = 
15/07/2025 : 21:19:55.083 :     DateFactureEnd   = 
15/07/2025 : 21:19:55.084 :     NO SORTING: Using default sort
15/07/2025 : 21:19:55.433 :     Items retrieved: 14
15/07/2025 : 21:20:00.412 :     Count = 8832132
15/07/2025 : 21:20:00.413 : Leaving
15/07/2025 : 21:20:00.551 : Entering
15/07/2025 : 21:20:00.552 :     Client           = 
15/07/2025 : 21:20:00.552 :     DateFactureStart = 
15/07/2025 : 21:20:00.552 :     DateFactureEnd   = 
15/07/2025 : 21:20:00.552 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 21:20:01.827 :     Items retrieved: 14
15/07/2025 : 21:20:04.096 :     Count = 8832132
15/07/2025 : 21:20:04.096 : Leaving
15/07/2025 : 21:21:48.975 : Entering
15/07/2025 : 21:21:48.978 :     Client           = 
15/07/2025 : 21:21:48.978 :     DateFactureStart = 
15/07/2025 : 21:21:48.979 :     DateFactureEnd   = 
15/07/2025 : 21:21:48.979 :     NO SORTING: Using default sort
15/07/2025 : 21:21:50.995 :     Items retrieved: 14
15/07/2025 : 21:21:52.854 :     Count = 8832132
15/07/2025 : 21:21:52.857 : Leaving
15/07/2025 : 21:21:53.252 : Entering
15/07/2025 : 21:21:53.253 :     Client           = 
15/07/2025 : 21:21:53.253 :     DateFactureStart = 
15/07/2025 : 21:21:53.253 :     DateFactureEnd   = 
15/07/2025 : 21:21:53.254 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 21:21:54.285 :     Items retrieved: 14
15/07/2025 : 21:21:56.228 :     Count = 8832132
15/07/2025 : 21:21:56.229 : Leaving
15/07/2025 : 21:21:56.383 : Entering
15/07/2025 : 21:21:56.384 :     Client           = 
15/07/2025 : 21:21:56.384 :     DateFactureStart = 
15/07/2025 : 21:21:56.384 :     DateFactureEnd   = 
15/07/2025 : 21:21:56.385 :     NO SORTING: Using default sort
15/07/2025 : 21:21:56.453 :     Items retrieved: 14
15/07/2025 : 21:21:58.687 :     Count = 8832132
15/07/2025 : 21:21:58.689 : Leaving
15/07/2025 : 21:21:58.776 : Entering
15/07/2025 : 21:21:58.777 :     Client           = 
15/07/2025 : 21:21:58.777 :     DateFactureStart = 
15/07/2025 : 21:21:58.777 :     DateFactureEnd   = 
15/07/2025 : 21:21:58.778 :     NO SORTING: Using default sort
15/07/2025 : 21:21:58.842 :     Items retrieved: 14
15/07/2025 : 21:22:00.727 :     Count = 8832132
15/07/2025 : 21:22:00.729 : Leaving
15/07/2025 : 21:23:08.153 : Entering
15/07/2025 : 21:23:08.155 :     Client           = 
15/07/2025 : 21:23:08.156 :     DateFactureStart = 
15/07/2025 : 21:23:08.156 :     DateFactureEnd   = 
15/07/2025 : 21:23:08.157 :     NO SORTING: Using default sort
15/07/2025 : 21:23:10.422 :     Items retrieved: 14
15/07/2025 : 21:23:12.314 :     Count = 8832132
15/07/2025 : 21:23:12.316 : Leaving
15/07/2025 : 21:23:12.657 : Entering
15/07/2025 : 21:23:12.657 :     Client           = 
15/07/2025 : 21:23:12.658 :     DateFactureStart = 
15/07/2025 : 21:23:12.658 :     DateFactureEnd   = 
15/07/2025 : 21:23:12.658 :     NO SORTING: Using default sort
15/07/2025 : 21:23:12.799 :     Items retrieved: 14
15/07/2025 : 21:23:14.851 :     Count = 8832132
15/07/2025 : 21:23:14.851 : Leaving
15/07/2025 : 21:24:05.372 : Entering
15/07/2025 : 21:24:05.372 :     Client           = 
15/07/2025 : 21:24:05.373 :     DateFactureStart = 
15/07/2025 : 21:24:05.373 :     DateFactureEnd   = 
15/07/2025 : 21:24:05.373 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:24:06.448 :     Items retrieved: 14
15/07/2025 : 21:24:08.452 :     Count = 8832132
15/07/2025 : 21:24:08.453 : Leaving
15/07/2025 : 21:24:08.496 : Entering
15/07/2025 : 21:24:08.496 :     Client           = 
15/07/2025 : 21:24:08.496 :     DateFactureStart = 
15/07/2025 : 21:24:08.497 :     DateFactureEnd   = 
15/07/2025 : 21:24:08.497 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 21:24:09.247 :     Items retrieved: 14
15/07/2025 : 21:24:11.075 :     Count = 8832132
15/07/2025 : 21:24:11.076 : Leaving
15/07/2025 : 21:24:11.461 : Entering
15/07/2025 : 21:24:11.461 :     Client           = 
15/07/2025 : 21:24:11.462 :     DateFactureStart = 
15/07/2025 : 21:24:11.462 :     DateFactureEnd   = 
15/07/2025 : 21:24:11.462 :     NO SORTING: Using default sort
15/07/2025 : 21:24:11.532 :     Items retrieved: 14
15/07/2025 : 21:24:13.446 :     Count = 8832132
15/07/2025 : 21:24:13.447 : Leaving
15/07/2025 : 21:24:13.460 : Entering
15/07/2025 : 21:24:13.461 :     Client           = 
15/07/2025 : 21:24:13.461 :     DateFactureStart = 
15/07/2025 : 21:24:13.462 :     DateFactureEnd   = 
15/07/2025 : 21:24:13.462 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:24:14.269 :     Items retrieved: 14
15/07/2025 : 21:24:16.067 :     Count = 8832132
15/07/2025 : 21:24:16.068 : Leaving
15/07/2025 : 21:24:16.094 : Entering
15/07/2025 : 21:24:16.096 :     Client           = 
15/07/2025 : 21:24:16.098 :     DateFactureStart = 
15/07/2025 : 21:24:16.099 :     DateFactureEnd   = 
15/07/2025 : 21:24:16.100 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 21:24:16.930 :     Items retrieved: 14
15/07/2025 : 21:24:18.771 :     Count = 8832132
15/07/2025 : 21:24:18.772 : Leaving
15/07/2025 : 21:24:19.263 : Entering
15/07/2025 : 21:24:19.264 :     Client           = 
15/07/2025 : 21:24:19.265 :     DateFactureStart = 
15/07/2025 : 21:24:19.266 :     DateFactureEnd   = 
15/07/2025 : 21:24:19.266 :     NO SORTING: Using default sort
15/07/2025 : 21:24:19.337 :     Items retrieved: 14
15/07/2025 : 21:24:21.244 :     Count = 8832132
15/07/2025 : 21:24:21.245 : Leaving
15/07/2025 : 21:24:22.179 : Entering
15/07/2025 : 21:24:22.181 :     Client           = 
15/07/2025 : 21:24:22.181 :     DateFactureStart = 
15/07/2025 : 21:24:22.182 :     DateFactureEnd   = 
15/07/2025 : 21:24:22.183 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:24:23.074 :     Items retrieved: 14
15/07/2025 : 21:24:24.915 :     Count = 8832132
15/07/2025 : 21:24:24.915 : Leaving
15/07/2025 : 21:28:02.708 : Entering
15/07/2025 : 21:28:02.711 :     Client           = 
15/07/2025 : 21:28:02.712 :     DateFactureStart = 
15/07/2025 : 21:28:02.713 :     DateFactureEnd   = 
15/07/2025 : 21:28:02.713 :     NO SORTING: Using default sort
15/07/2025 : 21:28:04.451 :     Items retrieved: 14
15/07/2025 : 21:28:06.923 :     Count = 8832132
15/07/2025 : 21:28:06.925 : Leaving
15/07/2025 : 21:28:07.559 : Entering
15/07/2025 : 21:28:07.560 :     Client           = 
15/07/2025 : 21:28:07.560 :     DateFactureStart = 
15/07/2025 : 21:28:07.561 :     DateFactureEnd   = 
15/07/2025 : 21:28:07.562 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:28:08.766 :     Items retrieved: 14
15/07/2025 : 21:28:10.710 :     Count = 8832132
15/07/2025 : 21:28:10.711 : Leaving
15/07/2025 : 21:35:05.098 : Entering
15/07/2025 : 21:35:05.102 :     Client           = 
15/07/2025 : 21:35:05.103 :     DateFactureStart = 
15/07/2025 : 21:35:05.103 :     DateFactureEnd   = 
15/07/2025 : 21:35:05.104 :     NO SORTING: Using default sort
15/07/2025 : 21:35:10.305 :     Items retrieved: 14
15/07/2025 : 21:35:12.609 :     Count = 8832132
15/07/2025 : 21:35:12.611 : Leaving
15/07/2025 : 21:35:13.668 : Entering
15/07/2025 : 21:35:13.668 :     Client           = 
15/07/2025 : 21:35:13.669 :     DateFactureStart = 
15/07/2025 : 21:35:13.669 :     DateFactureEnd   = 
15/07/2025 : 21:35:13.670 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:35:14.962 :     Items retrieved: 14
15/07/2025 : 21:35:16.801 :     Count = 8832132
15/07/2025 : 21:35:16.801 : Leaving
15/07/2025 : 21:38:38.135 : Entering
15/07/2025 : 21:38:38.137 :     Client           = 
15/07/2025 : 21:38:38.138 :     DateFactureStart = 
15/07/2025 : 21:38:38.139 :     DateFactureEnd   = 
15/07/2025 : 21:38:38.139 :     NO SORTING: Using default sort
15/07/2025 : 21:38:39.737 :     Items retrieved: 14
15/07/2025 : 21:38:41.845 :     Count = 8832132
15/07/2025 : 21:38:41.847 : Leaving
15/07/2025 : 21:38:42.127 : Entering
15/07/2025 : 21:38:42.127 :     Client           = 
15/07/2025 : 21:38:42.128 :     DateFactureStart = 
15/07/2025 : 21:38:42.128 :     DateFactureEnd   = 
15/07/2025 : 21:38:42.128 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:38:42.965 :     Items retrieved: 14
15/07/2025 : 21:38:44.910 :     Count = 8832132
15/07/2025 : 21:38:44.911 : Leaving
15/07/2025 : 21:44:51.661 : Entering
15/07/2025 : 21:44:51.663 :     Client           = 
15/07/2025 : 21:44:51.664 :     DateFactureStart = 
15/07/2025 : 21:44:51.664 :     DateFactureEnd   = 
15/07/2025 : 21:44:51.665 :     NO SORTING: Using default sort
15/07/2025 : 21:44:53.028 :     Items retrieved: 14
15/07/2025 : 21:44:56.324 :     Count = 8832132
15/07/2025 : 21:44:56.325 : Leaving
15/07/2025 : 21:44:56.581 : Entering
15/07/2025 : 21:44:56.582 :     Client           = 
15/07/2025 : 21:44:56.582 :     DateFactureStart = 
15/07/2025 : 21:44:56.582 :     DateFactureEnd   = 
15/07/2025 : 21:44:56.583 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:44:58.502 :     Items retrieved: 14
15/07/2025 : 21:45:01.449 :     Count = 8832132
15/07/2025 : 21:45:01.450 : Leaving
15/07/2025 : 21:49:36.687 : Entering
15/07/2025 : 21:49:36.689 :     Client           = 
15/07/2025 : 21:49:36.689 :     DateFactureStart = 
15/07/2025 : 21:49:36.690 :     DateFactureEnd   = 
15/07/2025 : 21:49:36.690 :     NO SORTING: Using default sort
15/07/2025 : 21:49:38.236 :     Items retrieved: 14
15/07/2025 : 21:49:40.412 :     Count = 8832132
15/07/2025 : 21:49:40.415 : Leaving
15/07/2025 : 21:49:40.717 : Entering
15/07/2025 : 21:49:40.718 :     Client           = 
15/07/2025 : 21:49:40.718 :     DateFactureStart = 
15/07/2025 : 21:49:40.718 :     DateFactureEnd   = 
15/07/2025 : 21:49:40.718 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:49:41.650 :     Items retrieved: 14
15/07/2025 : 21:49:43.549 :     Count = 8832132
15/07/2025 : 21:49:43.549 : Leaving
15/07/2025 : 21:55:46.504 : Entering
15/07/2025 : 21:55:46.506 :     Client           = 
15/07/2025 : 21:55:46.507 :     DateFactureStart = 
15/07/2025 : 21:55:46.507 :     DateFactureEnd   = 
15/07/2025 : 21:55:46.507 :     NO SORTING: Using default sort
15/07/2025 : 21:55:48.130 :     Items retrieved: 14
15/07/2025 : 21:55:50.359 :     Count = 8832132
15/07/2025 : 21:55:50.360 : Leaving
15/07/2025 : 21:55:50.763 : Entering
15/07/2025 : 21:55:50.763 :     Client           = 
15/07/2025 : 21:55:50.764 :     DateFactureStart = 
15/07/2025 : 21:55:50.764 :     DateFactureEnd   = 
15/07/2025 : 21:55:50.764 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:55:51.688 :     Items retrieved: 14
15/07/2025 : 21:55:53.367 :     Count = 8832132
15/07/2025 : 21:55:53.368 : Leaving
15/07/2025 : 21:58:47.385 : Entering
15/07/2025 : 21:58:47.387 :     Client           = 
15/07/2025 : 21:58:47.387 :     DateFactureStart = 
15/07/2025 : 21:58:47.388 :     DateFactureEnd   = 
15/07/2025 : 21:58:47.388 :     NO SORTING: Using default sort
15/07/2025 : 21:58:48.757 :     Items retrieved: 14
15/07/2025 : 21:58:50.559 :     Count = 8832132
15/07/2025 : 21:58:50.560 : Leaving
15/07/2025 : 21:58:50.833 : Entering
15/07/2025 : 21:58:50.834 :     Client           = 
15/07/2025 : 21:58:50.834 :     DateFactureStart = 
15/07/2025 : 21:58:50.834 :     DateFactureEnd   = 
15/07/2025 : 21:58:50.834 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 21:58:51.700 :     Items retrieved: 14
15/07/2025 : 21:58:53.371 :     Count = 8832132
15/07/2025 : 21:58:53.372 : Leaving
15/07/2025 : 22:01:23.281 : Entering
15/07/2025 : 22:01:23.282 :     Client           = 
15/07/2025 : 22:01:23.282 :     DateFactureStart = 
15/07/2025 : 22:01:23.283 :     DateFactureEnd   = 
15/07/2025 : 22:01:23.283 :     NO SORTING: Using default sort
15/07/2025 : 22:01:24.645 :     Items retrieved: 14
15/07/2025 : 22:01:27.059 :     Count = 8832132
15/07/2025 : 22:01:27.060 : Leaving
15/07/2025 : 22:01:27.333 : Entering
15/07/2025 : 22:01:27.333 :     Client           = 
15/07/2025 : 22:01:27.333 :     DateFactureStart = 
15/07/2025 : 22:01:27.333 :     DateFactureEnd   = 
15/07/2025 : 22:01:27.334 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:01:28.421 :     Items retrieved: 14
15/07/2025 : 22:01:30.514 :     Count = 8832132
15/07/2025 : 22:01:30.514 : Leaving
15/07/2025 : 22:01:59.049 : Entering
15/07/2025 : 22:01:59.049 :     Client           = 
15/07/2025 : 22:01:59.050 :     DateFactureStart = 
15/07/2025 : 22:01:59.050 :     DateFactureEnd   = 
15/07/2025 : 22:01:59.050 :     NO SORTING: Using default sort
15/07/2025 : 22:01:59.142 :     Items retrieved: 14
15/07/2025 : 22:02:00.975 :     Count = 8832132
15/07/2025 : 22:02:00.977 : Leaving
15/07/2025 : 22:02:01.057 : Entering
15/07/2025 : 22:02:01.057 :     Client           = 
15/07/2025 : 22:02:01.058 :     DateFactureStart = 
15/07/2025 : 22:02:01.058 :     DateFactureEnd   = 
15/07/2025 : 22:02:01.058 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:02:02.008 :     Items retrieved: 14
15/07/2025 : 22:02:04.035 :     Count = 8832132
15/07/2025 : 22:02:04.036 : Leaving
15/07/2025 : 22:02:10.096 : Entering
15/07/2025 : 22:02:10.097 :     Client           = 
15/07/2025 : 22:02:10.097 :     DateFactureStart = 
15/07/2025 : 22:02:10.098 :     DateFactureEnd   = 
15/07/2025 : 22:02:10.098 :     NO SORTING: Using default sort
15/07/2025 : 22:02:10.171 :     Items retrieved: 14
15/07/2025 : 22:02:12.085 :     Count = 8832132
15/07/2025 : 22:02:12.086 : Leaving
15/07/2025 : 22:02:12.241 : Entering
15/07/2025 : 22:02:12.242 :     Client           = 
15/07/2025 : 22:02:12.242 :     DateFactureStart = 
15/07/2025 : 22:02:12.242 :     DateFactureEnd   = 
15/07/2025 : 22:02:12.242 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:02:13.083 :     Items retrieved: 14
15/07/2025 : 22:02:15.098 :     Count = 8832132
15/07/2025 : 22:02:15.099 : Leaving
15/07/2025 : 22:03:37.615 : Entering
15/07/2025 : 22:03:37.615 :     Client           = 
15/07/2025 : 22:03:37.615 :     DateFactureStart = 
15/07/2025 : 22:03:37.615 :     DateFactureEnd   = 
15/07/2025 : 22:03:37.616 :     NO SORTING: Using default sort
15/07/2025 : 22:03:37.686 :     Items retrieved: 14
15/07/2025 : 22:03:39.679 :     Count = 8832132
15/07/2025 : 22:03:39.679 : Leaving
15/07/2025 : 22:03:39.740 : Entering
15/07/2025 : 22:03:39.740 :     Client           = 
15/07/2025 : 22:03:39.740 :     DateFactureStart = 
15/07/2025 : 22:03:39.741 :     DateFactureEnd   = 
15/07/2025 : 22:03:39.741 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:03:40.562 :     Items retrieved: 14
15/07/2025 : 22:03:42.336 :     Count = 8832132
15/07/2025 : 22:03:42.337 : Leaving
15/07/2025 : 22:03:58.213 : Entering
15/07/2025 : 22:03:58.214 :     Client           = 
15/07/2025 : 22:03:58.215 :     DateFactureStart = 
15/07/2025 : 22:03:58.215 :     DateFactureEnd   = 
15/07/2025 : 22:03:58.216 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 22:03:59.101 :     Items retrieved: 14
15/07/2025 : 22:04:01.026 :     Count = 8832132
15/07/2025 : 22:04:01.033 : Leaving
15/07/2025 : 22:04:01.059 : Entering
15/07/2025 : 22:04:01.059 :     Client           = 
15/07/2025 : 22:04:01.059 :     DateFactureStart = 
15/07/2025 : 22:04:01.060 :     DateFactureEnd   = 
15/07/2025 : 22:04:01.060 :     NO SORTING: Using default sort
15/07/2025 : 22:04:01.127 :     Items retrieved: 14
15/07/2025 : 22:04:03.236 :     Count = 8832132
15/07/2025 : 22:04:03.250 : Leaving
15/07/2025 : 22:04:03.261 : Entering
15/07/2025 : 22:04:03.263 :     Client           = 
15/07/2025 : 22:04:03.264 :     DateFactureStart = 
15/07/2025 : 22:04:03.264 :     DateFactureEnd   = 
15/07/2025 : 22:04:03.264 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:04:04.176 :     Items retrieved: 14
15/07/2025 : 22:04:06.089 :     Count = 8832132
15/07/2025 : 22:04:06.090 : Leaving
15/07/2025 : 22:06:37.552 : Entering
15/07/2025 : 22:06:37.554 :     Client           = 
15/07/2025 : 22:06:37.555 :     DateFactureStart = 
15/07/2025 : 22:06:37.555 :     DateFactureEnd   = 
15/07/2025 : 22:06:37.555 :     NO SORTING: Using default sort
15/07/2025 : 22:06:39.051 :     Items retrieved: 14
15/07/2025 : 22:06:40.813 :     Count = 8832132
15/07/2025 : 22:06:40.816 : Leaving
15/07/2025 : 22:06:41.063 : Entering
15/07/2025 : 22:06:41.063 :     Client           = 
15/07/2025 : 22:06:41.063 :     DateFactureStart = 
15/07/2025 : 22:06:41.064 :     DateFactureEnd   = 
15/07/2025 : 22:06:41.064 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:06:41.820 :     Items retrieved: 14
15/07/2025 : 22:06:43.727 :     Count = 8832132
15/07/2025 : 22:06:43.728 : Leaving
15/07/2025 : 22:07:01.189 : Entering
15/07/2025 : 22:07:01.189 :     Client           = 
15/07/2025 : 22:07:01.189 :     DateFactureStart = 
15/07/2025 : 22:07:01.190 :     DateFactureEnd   = 
15/07/2025 : 22:07:01.190 :     NO SORTING: Using default sort
15/07/2025 : 22:07:01.255 :     Items retrieved: 14
15/07/2025 : 22:07:03.135 :     Count = 8832132
15/07/2025 : 22:07:03.136 : Leaving
15/07/2025 : 22:07:03.216 : Entering
15/07/2025 : 22:07:03.216 :     Client           = 
15/07/2025 : 22:07:03.216 :     DateFactureStart = 
15/07/2025 : 22:07:03.216 :     DateFactureEnd   = 
15/07/2025 : 22:07:03.216 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:07:03.964 :     Items retrieved: 14
15/07/2025 : 22:07:05.786 :     Count = 8832132
15/07/2025 : 22:07:05.787 : Leaving
15/07/2025 : 22:09:11.344 : Entering
15/07/2025 : 22:09:11.346 :     Client           = 
15/07/2025 : 22:09:11.346 :     DateFactureStart = 
15/07/2025 : 22:09:11.346 :     DateFactureEnd   = 
15/07/2025 : 22:09:11.347 :     NO SORTING: Using default sort
15/07/2025 : 22:09:12.784 :     Items retrieved: 14
15/07/2025 : 22:09:14.863 :     Count = 8832132
15/07/2025 : 22:09:14.864 : Leaving
15/07/2025 : 22:09:15.094 : Entering
15/07/2025 : 22:09:15.095 :     Client           = 
15/07/2025 : 22:09:15.095 :     DateFactureStart = 
15/07/2025 : 22:09:15.095 :     DateFactureEnd   = 
15/07/2025 : 22:09:15.096 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:09:16.051 :     Items retrieved: 14
15/07/2025 : 22:09:18.472 :     Count = 8832132
15/07/2025 : 22:09:18.473 : Leaving
15/07/2025 : 22:11:06.549 : Entering
15/07/2025 : 22:11:06.550 :     Client           = 
15/07/2025 : 22:11:06.551 :     DateFactureStart = 
15/07/2025 : 22:11:06.551 :     DateFactureEnd   = 
15/07/2025 : 22:11:06.551 :     NO SORTING: Using default sort
15/07/2025 : 22:11:08.050 :     Items retrieved: 14
15/07/2025 : 22:11:10.302 :     Count = 8832132
15/07/2025 : 22:11:10.303 : Leaving
15/07/2025 : 22:11:10.566 : Entering
15/07/2025 : 22:11:10.567 :     Client           = 
15/07/2025 : 22:11:10.567 :     DateFactureStart = 
15/07/2025 : 22:11:10.567 :     DateFactureEnd   = 
15/07/2025 : 22:11:10.568 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:11:11.532 :     Items retrieved: 14
15/07/2025 : 22:11:13.358 :     Count = 8832132
15/07/2025 : 22:11:13.358 : Leaving
15/07/2025 : 22:15:06.507 : Entering
15/07/2025 : 22:15:06.509 :     Client           = 
15/07/2025 : 22:15:06.509 :     DateFactureStart = 
15/07/2025 : 22:15:06.509 :     DateFactureEnd   = 
15/07/2025 : 22:15:06.510 :     NO SORTING: Using default sort
15/07/2025 : 22:15:07.851 :     Items retrieved: 14
15/07/2025 : 22:15:09.651 :     Count = 8832132
15/07/2025 : 22:15:09.653 : Leaving
15/07/2025 : 22:15:09.918 : Entering
15/07/2025 : 22:15:09.918 :     Client           = 
15/07/2025 : 22:15:09.918 :     DateFactureStart = 
15/07/2025 : 22:15:09.919 :     DateFactureEnd   = 
15/07/2025 : 22:15:09.919 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:15:10.691 :     Items retrieved: 14
15/07/2025 : 22:15:12.449 :     Count = 8832132
15/07/2025 : 22:15:12.450 : Leaving
15/07/2025 : 22:15:21.980 : Entering
15/07/2025 : 22:15:21.980 :     Client           = 
15/07/2025 : 22:15:21.981 :     DateFactureStart = 
15/07/2025 : 22:15:21.981 :     DateFactureEnd   = 
15/07/2025 : 22:15:21.981 :     NO SORTING: Using default sort
15/07/2025 : 22:15:22.045 :     Items retrieved: 14
15/07/2025 : 22:15:23.860 :     Count = 8832132
15/07/2025 : 22:15:23.861 : Leaving
15/07/2025 : 22:15:23.925 : Entering
15/07/2025 : 22:15:23.925 :     Client           = 
15/07/2025 : 22:15:23.925 :     DateFactureStart = 
15/07/2025 : 22:15:23.926 :     DateFactureEnd   = 
15/07/2025 : 22:15:23.926 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:15:24.866 :     Items retrieved: 14
15/07/2025 : 22:15:26.729 :     Count = 8832132
15/07/2025 : 22:15:26.730 : Leaving
15/07/2025 : 22:23:23.179 : Entering
15/07/2025 : 22:23:23.181 :     Client           = 
15/07/2025 : 22:23:23.181 :     DateFactureStart = 
15/07/2025 : 22:23:23.181 :     DateFactureEnd   = 
15/07/2025 : 22:23:23.182 :     NO SORTING: Using default sort
15/07/2025 : 22:23:24.603 :     Items retrieved: 14
15/07/2025 : 22:23:26.886 :     Count = 8832132
15/07/2025 : 22:23:26.888 : Leaving
15/07/2025 : 22:23:27.140 : Entering
15/07/2025 : 22:23:27.141 :     Client           = 
15/07/2025 : 22:23:27.141 :     DateFactureStart = 
15/07/2025 : 22:23:27.141 :     DateFactureEnd   = 
15/07/2025 : 22:23:27.142 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:23:28.185 :     Items retrieved: 14
15/07/2025 : 22:23:29.991 :     Count = 8832132
15/07/2025 : 22:23:29.991 : Leaving
15/07/2025 : 22:25:37.888 : Entering
15/07/2025 : 22:25:37.889 :     Client           = 
15/07/2025 : 22:25:37.890 :     DateFactureStart = 
15/07/2025 : 22:25:37.890 :     DateFactureEnd   = 
15/07/2025 : 22:25:37.890 :     NO SORTING: Using default sort
15/07/2025 : 22:25:39.311 :     Items retrieved: 14
15/07/2025 : 22:25:41.121 :     Count = 8832132
15/07/2025 : 22:25:41.122 : Leaving
15/07/2025 : 22:25:41.361 : Entering
15/07/2025 : 22:25:41.361 :     Client           = 
15/07/2025 : 22:25:41.362 :     DateFactureStart = 
15/07/2025 : 22:25:41.362 :     DateFactureEnd   = 
15/07/2025 : 22:25:41.362 :     NO SORTING: Using default sort
15/07/2025 : 22:25:41.479 :     Items retrieved: 14
15/07/2025 : 22:25:43.238 :     Count = 8832132
15/07/2025 : 22:25:43.239 : Leaving
15/07/2025 : 22:26:22.648 : Entering
15/07/2025 : 22:26:22.649 :     Client           = 
15/07/2025 : 22:26:22.649 :     DateFactureStart = 
15/07/2025 : 22:26:22.650 :     DateFactureEnd   = 
15/07/2025 : 22:26:22.650 :     NO SORTING: Using default sort
15/07/2025 : 22:26:22.725 :     Items retrieved: 14
15/07/2025 : 22:26:24.551 :     Count = 8806096
15/07/2025 : 22:26:24.552 : Leaving
15/07/2025 : 22:28:56.181 : Entering
15/07/2025 : 22:28:56.183 :     Client           = 
15/07/2025 : 22:28:56.184 :     DateFactureStart = 
15/07/2025 : 22:28:56.184 :     DateFactureEnd   = 
15/07/2025 : 22:28:56.184 :     NO SORTING: Using default sort
15/07/2025 : 22:28:57.576 :     Items retrieved: 14
15/07/2025 : 22:28:59.518 :     Count = 8832132
15/07/2025 : 22:28:59.519 : Leaving
15/07/2025 : 22:28:59.717 : Entering
15/07/2025 : 22:28:59.717 :     Client           = 
15/07/2025 : 22:28:59.717 :     DateFactureStart = 
15/07/2025 : 22:28:59.718 :     DateFactureEnd   = 
15/07/2025 : 22:28:59.718 :     NO SORTING: Using default sort
15/07/2025 : 22:28:59.834 :     Items retrieved: 14
15/07/2025 : 22:29:01.622 :     Count = 8832132
15/07/2025 : 22:29:01.625 : Leaving
15/07/2025 : 22:29:01.755 : Entering
15/07/2025 : 22:29:01.755 :     Client           = 
15/07/2025 : 22:29:01.755 :     DateFactureStart = 
15/07/2025 : 22:29:01.756 :     DateFactureEnd   = 
15/07/2025 : 22:29:01.756 :     NO SORTING: Using default sort
15/07/2025 : 22:29:01.821 :     Items retrieved: 14
15/07/2025 : 22:29:03.709 :     Count = 8832132
15/07/2025 : 22:29:03.710 : Leaving
15/07/2025 : 22:29:11.965 : Entering
15/07/2025 : 22:29:11.966 :     Client           = 
15/07/2025 : 22:29:11.967 :     DateFactureStart = 
15/07/2025 : 22:29:11.967 :     DateFactureEnd   = 
15/07/2025 : 22:29:11.968 :     SORTING: Column = typefac, Direction = ascending
15/07/2025 : 22:29:13.044 :     Items retrieved: 14
15/07/2025 : 22:29:14.859 :     Count = 8832132
15/07/2025 : 22:29:14.860 : Leaving
15/07/2025 : 22:29:19.963 : Entering
15/07/2025 : 22:29:19.964 :     Client           = 
15/07/2025 : 22:29:19.965 :     DateFactureStart = 
15/07/2025 : 22:29:19.966 :     DateFactureEnd   = 
15/07/2025 : 22:29:19.966 :     SORTING: Column = typetva, Direction = ascending
15/07/2025 : 22:29:34.968 :     Items retrieved: 14
15/07/2025 : 22:29:36.788 :     Count = 8832132
15/07/2025 : 22:29:36.789 : Leaving
15/07/2025 : 22:29:59.738 : Entering
15/07/2025 : 22:29:59.739 :     Client           = 
15/07/2025 : 22:29:59.740 :     DateFactureStart = 
15/07/2025 : 22:29:59.741 :     DateFactureEnd   = 
15/07/2025 : 22:29:59.742 :     NO SORTING: Using default sort
15/07/2025 : 22:29:59.816 :     Items retrieved: 14
15/07/2025 : 22:30:02.016 :     Count = 8832132
15/07/2025 : 22:30:02.018 : Leaving
15/07/2025 : 22:32:01.636 : Entering
15/07/2025 : 22:32:01.638 :     Client           = 
15/07/2025 : 22:32:01.638 :     DateFactureStart = 
15/07/2025 : 22:32:01.639 :     DateFactureEnd   = 
15/07/2025 : 22:32:01.639 :     NO SORTING: Using default sort
15/07/2025 : 22:32:03.047 :     Items retrieved: 14
15/07/2025 : 22:32:03.644 :     Entering
15/07/2025 : 22:32:03.644 :         Client           = 
15/07/2025 : 22:32:03.645 :         DateFactureStart = 
15/07/2025 : 22:32:03.645 :         DateFactureEnd   = 
15/07/2025 : 22:32:03.645 :         NO SORTING: Using default sort
15/07/2025 : 22:32:04.573 :         Items retrieved: 14
15/07/2025 : 22:32:05.108 :         Count = 8832132
15/07/2025 : 22:32:05.108 :     Leaving
15/07/2025 : 22:32:06.587 :     Count = 8832132
15/07/2025 : 22:32:06.588 : Leaving
15/07/2025 : 22:32:06.679 : Entering
15/07/2025 : 22:32:06.679 :     Client           = 
15/07/2025 : 22:32:06.680 :     DateFactureStart = 
15/07/2025 : 22:32:06.680 :     DateFactureEnd   = 
15/07/2025 : 22:32:06.680 :     SORTING: Column = typetva, Direction = ascending
15/07/2025 : 22:32:19.999 :     Items retrieved: 14
15/07/2025 : 22:32:21.911 :     Count = 8832132
15/07/2025 : 22:32:21.912 : Leaving
15/07/2025 : 22:33:05.273 : Entering
15/07/2025 : 22:33:05.274 :     Client           = 
15/07/2025 : 22:33:05.275 :     DateFactureStart = 
15/07/2025 : 22:33:05.275 :     DateFactureEnd   = 
15/07/2025 : 22:33:05.276 :     NO SORTING: Using default sort
15/07/2025 : 22:33:05.343 :     Items retrieved: 14
15/07/2025 : 22:33:07.188 :     Count = 8832132
15/07/2025 : 22:33:07.189 : Leaving
15/07/2025 : 22:33:21.449 : Entering
15/07/2025 : 22:33:21.450 :     Client           = 
15/07/2025 : 22:33:21.450 :     DateFactureStart = 
15/07/2025 : 22:33:21.450 :     DateFactureEnd   = 
15/07/2025 : 22:33:21.451 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:33:22.144 :     Items retrieved: 14
15/07/2025 : 22:33:23.979 :     Count = 8832132
15/07/2025 : 22:33:23.980 : Leaving
15/07/2025 : 22:33:23.995 : Entering
15/07/2025 : 22:33:23.995 :     Client           = 
15/07/2025 : 22:33:23.996 :     DateFactureStart = 
15/07/2025 : 22:33:23.996 :     DateFactureEnd   = 
15/07/2025 : 22:33:23.996 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 22:33:24.729 :     Items retrieved: 14
15/07/2025 : 22:33:26.477 :     Count = 8832132
15/07/2025 : 22:33:26.478 : Leaving
15/07/2025 : 22:33:26.503 : Entering
15/07/2025 : 22:33:26.503 :     Client           = 
15/07/2025 : 22:33:26.504 :     DateFactureStart = 
15/07/2025 : 22:33:26.504 :     DateFactureEnd   = 
15/07/2025 : 22:33:26.504 :     NO SORTING: Using default sort
15/07/2025 : 22:33:26.567 :     Items retrieved: 14
15/07/2025 : 22:33:28.486 :     Count = 8832132
15/07/2025 : 22:33:28.487 : Leaving
15/07/2025 : 22:33:32.198 : Entering
15/07/2025 : 22:33:32.199 :     Client           = 
15/07/2025 : 22:33:32.199 :     DateFactureStart = 
15/07/2025 : 22:33:32.199 :     DateFactureEnd   = 
15/07/2025 : 22:33:32.200 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:33:32.994 :     Items retrieved: 14
15/07/2025 : 22:33:34.676 :     Count = 8832132
15/07/2025 : 22:33:34.677 : Leaving
15/07/2025 : 22:35:24.855 : Entering
15/07/2025 : 22:35:24.856 :     Client           = 
15/07/2025 : 22:35:24.856 :     DateFactureStart = 
15/07/2025 : 22:35:24.857 :     DateFactureEnd   = 
15/07/2025 : 22:35:24.857 :     SORTING: Column = datecrea, Direction = descending
15/07/2025 : 22:35:25.857 :     Items retrieved: 14
15/07/2025 : 22:35:27.875 :     Count = 8832132
15/07/2025 : 22:35:27.876 : Leaving
15/07/2025 : 22:35:27.886 : Entering
15/07/2025 : 22:35:27.889 :     Client           = 
15/07/2025 : 22:35:27.892 :     DateFactureStart = 
15/07/2025 : 22:35:27.901 :     DateFactureEnd   = 
15/07/2025 : 22:35:27.902 :     NO SORTING: Using default sort
15/07/2025 : 22:35:28.083 :     Items retrieved: 14
15/07/2025 : 22:35:30.026 :     Count = 8832132
15/07/2025 : 22:35:30.027 : Leaving
15/07/2025 : 22:37:15.982 : Entering
15/07/2025 : 22:37:15.983 :     Client           = 
15/07/2025 : 22:37:15.984 :     DateFactureStart = 
15/07/2025 : 22:37:15.985 :     DateFactureEnd   = 
15/07/2025 : 22:37:15.986 :     NO SORTING: Using default sort
15/07/2025 : 22:37:16.070 :     Items retrieved: 14
15/07/2025 : 22:37:17.826 :     Count = 8832132
15/07/2025 : 22:37:17.827 : Leaving
15/07/2025 : 22:38:35.055 : Entering
15/07/2025 : 22:38:35.058 :     Client           = 
15/07/2025 : 22:38:35.058 :     DateFactureStart = 
15/07/2025 : 22:38:35.059 :     DateFactureEnd   = 
15/07/2025 : 22:38:35.059 :     NO SORTING: Using default sort
15/07/2025 : 22:38:38.232 :     Items retrieved: 14
15/07/2025 : 22:38:40.095 :     Count = 8832132
15/07/2025 : 22:38:40.095 : Leaving
15/07/2025 : 22:38:40.766 : Entering
15/07/2025 : 22:38:40.766 :     Client           = 
15/07/2025 : 22:38:40.767 :     DateFactureStart = 
15/07/2025 : 22:38:40.767 :     DateFactureEnd   = 
15/07/2025 : 22:38:40.767 :     SORTING: Column = datecrea, Direction = ascending
15/07/2025 : 22:38:41.826 :     Items retrieved: 14
15/07/2025 : 22:38:43.936 :     Count = 8832132
15/07/2025 : 22:38:43.936 : Leaving
