24/07/2025 : 00:45:22.515 : dateFactureStart = 
24/07/2025 : 00:45:22.517 : dateFactureEnd = 
24/07/2025 : 00:45:22.518 : sortcolumn = codeparc
24/07/2025 : 00:45:22.518 : sortdir = ascending
24/07/2025 : 00:45:22.524 : Formatted dateStartParam = 
24/07/2025 : 00:45:22.525 : Formatted dateEndParam = 
24/07/2025 : 00:45:25.149 : Retrieved 14 items
24/07/2025 : 00:45:32.064 : dateFactureStart = 
24/07/2025 : 00:45:32.065 : dateFactureEnd = 
24/07/2025 : 00:45:32.065 : sortcolumn = codeparc
24/07/2025 : 00:45:32.065 : sortdir = ascending
24/07/2025 : 00:45:32.065 : Formatted dateStartParam = 
24/07/2025 : 00:45:32.066 : Formatted dateEndParam = 
24/07/2025 : 00:45:32.173 : Retrieved 14 items
24/07/2025 : 00:45:54.010 : dateFactureStart = 
24/07/2025 : 00:45:54.011 : dateFactureEnd = 
24/07/2025 : 00:45:54.011 : sortcolumn = datefac2
24/07/2025 : 00:45:54.012 : sortdir = ascending
24/07/2025 : 00:45:54.012 : Formatted dateStartParam = 
24/07/2025 : 00:45:54.012 : Formatted dateEndParam = 
24/07/2025 : 00:46:24.694 : dateFactureStart = 
24/07/2025 : 00:46:24.694 : dateFactureEnd = 
24/07/2025 : 00:46:24.695 : sortcolumn = datefac2
24/07/2025 : 00:46:24.696 : sortdir = descending
24/07/2025 : 00:46:24.696 : Formatted dateStartParam = 
24/07/2025 : 00:46:24.696 : Formatted dateEndParam = 
24/07/2025 : 00:46:40.641 : Retrieved 14 items
24/07/2025 : 00:47:25.246 : dateFactureStart = 
24/07/2025 : 00:47:25.246 : dateFactureEnd = 
24/07/2025 : 00:47:25.246 : sortcolumn = montant
24/07/2025 : 00:47:25.246 : sortdir = ascending
24/07/2025 : 00:47:25.247 : Formatted dateStartParam = 
24/07/2025 : 00:47:25.247 : Formatted dateEndParam = 
24/07/2025 : 00:47:43.610 : Retrieved 14 items
24/07/2025 : 00:47:46.775 : dateFactureStart = 
24/07/2025 : 00:47:46.776 : dateFactureEnd = 
24/07/2025 : 00:47:46.777 : sortcolumn = montant
24/07/2025 : 00:47:46.779 : sortdir = descending
24/07/2025 : 00:47:46.782 : Formatted dateStartParam = 
24/07/2025 : 00:47:46.783 : Formatted dateEndParam = 
24/07/2025 : 00:48:02.563 : Retrieved 14 items
24/07/2025 : 00:51:26.686 : dateFactureStart = 
24/07/2025 : 00:51:26.687 : dateFactureEnd = 
24/07/2025 : 00:51:26.688 : sortcolumn = codeparc
24/07/2025 : 00:51:26.688 : sortdir = ascending
24/07/2025 : 00:51:26.693 : Formatted dateStartParam = 
24/07/2025 : 00:51:26.693 : Formatted dateEndParam = 
24/07/2025 : 00:51:30.267 : Retrieved 14 items
24/07/2025 : 00:51:34.226 : dateFactureStart = 
24/07/2025 : 00:51:34.227 : dateFactureEnd = 
24/07/2025 : 00:51:34.227 : sortcolumn = montant
24/07/2025 : 00:51:34.227 : sortdir = descending
24/07/2025 : 00:51:34.228 : Formatted dateStartParam = 
24/07/2025 : 00:51:34.228 : Formatted dateEndParam = 
24/07/2025 : 00:51:50.891 : Retrieved 14 items
24/07/2025 : 00:52:28.306 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:52:28.306 : dateFactureEnd = 
24/07/2025 : 00:52:28.307 : sortcolumn = montant
24/07/2025 : 00:52:28.307 : sortdir = descending
24/07/2025 : 00:52:28.307 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:52:28.307 : Formatted dateEndParam = 
24/07/2025 : 00:52:36.662 : Retrieved 14 items
24/07/2025 : 00:52:50.459 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:52:50.459 : dateFactureEnd = 
24/07/2025 : 00:52:50.459 : sortcolumn = datefac
24/07/2025 : 00:52:50.460 : sortdir = ascending
24/07/2025 : 00:52:50.460 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:52:50.462 : Formatted dateEndParam = 
24/07/2025 : 00:53:08.543 : Retrieved 14 items
24/07/2025 : 00:53:10.810 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:53:10.810 : dateFactureEnd = 
24/07/2025 : 00:53:10.811 : sortcolumn = datefac
24/07/2025 : 00:53:10.811 : sortdir = descending
24/07/2025 : 00:53:10.811 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:53:10.812 : Formatted dateEndParam = 
24/07/2025 : 00:53:23.918 : Retrieved 14 items
24/07/2025 : 00:53:26.651 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:53:26.652 : dateFactureEnd = 
24/07/2025 : 00:53:26.653 : sortcolumn = codeparc
24/07/2025 : 00:53:26.653 : sortdir = ascending
24/07/2025 : 00:53:26.654 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:53:26.655 : Formatted dateEndParam = 
24/07/2025 : 00:53:26.898 : Retrieved 14 items
24/07/2025 : 00:53:39.377 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:53:39.382 : dateFactureEnd = 
24/07/2025 : 00:53:39.383 : sortcolumn = codeparc
24/07/2025 : 00:53:39.383 : sortdir = ascending
24/07/2025 : 00:53:39.384 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:53:39.384 : Formatted dateEndParam = 
24/07/2025 : 00:53:40.024 : Retrieved 14 items
24/07/2025 : 00:54:01.798 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:54:01.800 : dateFactureEnd = 
24/07/2025 : 00:54:01.800 : sortcolumn = codeparc
24/07/2025 : 00:54:01.801 : sortdir = ascending
24/07/2025 : 00:54:01.801 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:54:01.802 : Formatted dateEndParam = 
24/07/2025 : 00:54:01.905 : Retrieved 0 items
24/07/2025 : 00:54:22.124 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:54:22.124 : dateFactureEnd = 
24/07/2025 : 00:54:22.125 : sortcolumn = codeparc
24/07/2025 : 00:54:22.126 : sortdir = ascending
24/07/2025 : 00:54:22.126 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:54:22.127 : Formatted dateEndParam = 
24/07/2025 : 00:54:22.187 : Retrieved 0 items
24/07/2025 : 00:54:23.508 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:54:23.509 : dateFactureEnd = 
24/07/2025 : 00:54:23.509 : sortcolumn = codeparc
24/07/2025 : 00:54:23.510 : sortdir = ascending
24/07/2025 : 00:54:23.511 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:54:23.511 : Formatted dateEndParam = 
24/07/2025 : 00:54:23.573 : Retrieved 0 items
24/07/2025 : 00:54:39.754 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 00:54:39.755 : dateFactureEnd = 
24/07/2025 : 00:54:39.756 : sortcolumn = codeparc
24/07/2025 : 00:54:39.757 : sortdir = ascending
24/07/2025 : 00:54:39.758 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 00:54:39.760 : Formatted dateEndParam = 
24/07/2025 : 00:54:49.546 : Retrieved 0 items
24/07/2025 : 01:03:21.355 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:03:21.356 : dateFactureEnd = 
24/07/2025 : 01:03:21.356 : sortcolumn = codeparc
24/07/2025 : 01:03:21.356 : sortdir = ascending
24/07/2025 : 01:03:21.357 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:03:21.357 : Formatted dateEndParam = 
24/07/2025 : 01:04:37.352 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:04:37.353 : dateFactureEnd = 
24/07/2025 : 01:04:37.354 : sortcolumn = codeparc
24/07/2025 : 01:04:37.355 : sortdir = ascending
24/07/2025 : 01:04:37.355 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:04:37.355 : Formatted dateEndParam = 
24/07/2025 : 01:04:51.155 : Retrieved 14 items
24/07/2025 : 01:05:48.275 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:05:48.275 : dateFactureEnd = 
24/07/2025 : 01:05:48.276 : sortcolumn = codeparc
24/07/2025 : 01:05:48.276 : sortdir = ascending
24/07/2025 : 01:05:48.276 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:05:48.277 : Formatted dateEndParam = 
24/07/2025 : 01:05:53.030 : Retrieved 0 items
24/07/2025 : 01:05:59.582 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:05:59.583 : dateFactureEnd = 
24/07/2025 : 01:05:59.584 : sortcolumn = codeparc
24/07/2025 : 01:05:59.588 : sortdir = ascending
24/07/2025 : 01:05:59.589 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:05:59.590 : Formatted dateEndParam = 
24/07/2025 : 01:06:03.818 : Retrieved 14 items
24/07/2025 : 01:06:45.748 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:06:45.748 : dateFactureEnd = 
24/07/2025 : 01:06:45.749 : sortcolumn = codeparc
24/07/2025 : 01:06:45.749 : sortdir = ascending
24/07/2025 : 01:06:45.750 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:06:45.750 : Formatted dateEndParam = 
24/07/2025 : 01:06:49.805 : Retrieved 14 items
24/07/2025 : 01:07:26.262 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:07:26.263 : dateFactureEnd = 
24/07/2025 : 01:07:26.263 : sortcolumn = codeparc
24/07/2025 : 01:07:26.263 : sortdir = ascending
24/07/2025 : 01:07:26.264 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:07:26.266 : Formatted dateEndParam = 
24/07/2025 : 01:07:30.537 : Retrieved 14 items
24/07/2025 : 01:07:50.333 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:07:50.333 : dateFactureEnd = 
24/07/2025 : 01:07:50.333 : sortcolumn = codeparc
24/07/2025 : 01:07:50.333 : sortdir = ascending
24/07/2025 : 01:07:50.333 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:07:50.334 : Formatted dateEndParam = 
24/07/2025 : 01:07:55.118 : Retrieved 14 items
24/07/2025 : 01:08:11.047 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:08:11.047 : dateFactureEnd = 
24/07/2025 : 01:08:11.047 : sortcolumn = codeparc
24/07/2025 : 01:08:11.048 : sortdir = ascending
24/07/2025 : 01:08:11.048 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:08:11.048 : Formatted dateEndParam = 
24/07/2025 : 01:08:15.761 : Retrieved 14 items
24/07/2025 : 01:08:29.639 : dateFactureStart = 13/06/2019 00:00:00
24/07/2025 : 01:08:29.639 : dateFactureEnd = 
24/07/2025 : 01:08:29.640 : sortcolumn = codeparc
24/07/2025 : 01:08:29.640 : sortdir = ascending
24/07/2025 : 01:08:29.640 : Formatted dateStartParam = 2019-06-13
24/07/2025 : 01:08:29.641 : Formatted dateEndParam = 
24/07/2025 : 01:08:33.366 : Retrieved 14 items
24/07/2025 : 01:10:20.835 : dateFactureStart = 
24/07/2025 : 01:10:20.835 : dateFactureEnd = 
24/07/2025 : 01:10:20.835 : client = 
24/07/2025 : 01:10:20.835 : parc = 
24/07/2025 : 01:10:20.836 : sortcolumn = codeparc
24/07/2025 : 01:10:20.836 : sortdir = ascending
24/07/2025 : 01:10:20.836 : Formatted dateStartParam = 
24/07/2025 : 01:10:20.836 : Formatted dateEndParam = 
24/07/2025 : 01:10:21.020 : Retrieved 14 items
24/07/2025 : 01:10:26.296 : dateFactureStart = 
24/07/2025 : 01:10:26.297 : dateFactureEnd = 
24/07/2025 : 01:10:26.298 : client = 
24/07/2025 : 01:10:26.298 : parc = 
24/07/2025 : 01:10:26.299 : sortcolumn = codeparc
24/07/2025 : 01:10:26.299 : sortdir = ascending
24/07/2025 : 01:10:26.299 : Formatted dateStartParam = 
24/07/2025 : 01:10:26.300 : Formatted dateEndParam = 
24/07/2025 : 01:10:26.357 : Retrieved 14 items
24/07/2025 : 03:03:34.302 : dateFactureStart = 
24/07/2025 : 03:03:34.306 : dateFactureEnd = 
24/07/2025 : 03:03:34.306 : client = 
24/07/2025 : 03:03:34.307 : parc = 
24/07/2025 : 03:03:34.307 : sortcolumn = codeparc
24/07/2025 : 03:03:34.308 : sortdir = ascending
24/07/2025 : 03:03:34.308 : Formatted dateStartParam = 
24/07/2025 : 03:03:34.308 : Formatted dateEndParam = 
24/07/2025 : 03:03:34.425 : Retrieved 14 items
24/07/2025 : 03:03:36.594 : dateFactureStart = 
24/07/2025 : 03:03:36.594 : dateFactureEnd = 
24/07/2025 : 03:03:36.594 : client = 
24/07/2025 : 03:03:36.594 : parc = 
24/07/2025 : 03:03:36.594 : sortcolumn = dateachat
24/07/2025 : 03:03:36.595 : sortdir = descending
24/07/2025 : 03:03:36.595 : Formatted dateStartParam = 
24/07/2025 : 03:03:36.595 : Formatted dateEndParam = 
24/07/2025 : 03:03:36.682 : Retrieved 14 items
24/07/2025 : 13:08:20.161 : dateFactureStart = 
24/07/2025 : 13:08:20.165 : dateFactureEnd = 
24/07/2025 : 13:08:20.165 : client = 
24/07/2025 : 13:08:20.165 : parc = 
24/07/2025 : 13:08:20.165 : sortcolumn = codeparc
24/07/2025 : 13:08:20.165 : sortdir = ascending
24/07/2025 : 13:08:20.169 : Formatted dateStartParam = 
24/07/2025 : 13:08:20.169 : Formatted dateEndParam = 
24/07/2025 : 13:08:23.937 : Retrieved 14 items
24/07/2025 : 13:08:27.167 : dateFactureStart = 
24/07/2025 : 13:08:27.168 : dateFactureEnd = 
24/07/2025 : 13:08:27.168 : client = 
24/07/2025 : 13:08:27.169 : parc = 
24/07/2025 : 13:08:27.169 : sortcolumn = codeparc
24/07/2025 : 13:08:27.169 : sortdir = ascending
24/07/2025 : 13:08:27.170 : Formatted dateStartParam = 
24/07/2025 : 13:08:27.170 : Formatted dateEndParam = 
24/07/2025 : 13:08:27.320 : Retrieved 14 items
24/07/2025 : 13:08:56.068 : dateFactureStart = 
24/07/2025 : 13:08:56.070 : dateFactureEnd = 
24/07/2025 : 13:08:56.071 : client = 0026
24/07/2025 : 13:08:56.071 : parc = 190
24/07/2025 : 13:08:56.072 : sortcolumn = codeparc
24/07/2025 : 13:08:56.072 : sortdir = ascending
24/07/2025 : 13:08:56.072 : Formatted dateStartParam = 
24/07/2025 : 13:08:56.073 : Formatted dateEndParam = 
24/07/2025 : 13:09:03.938 : Retrieved 14 items
24/07/2025 : 13:09:19.547 : dateFactureStart = 
24/07/2025 : 13:09:19.548 : dateFactureEnd = 
24/07/2025 : 13:09:19.549 : client = 0026
24/07/2025 : 13:09:19.549 : parc = 190
24/07/2025 : 13:09:19.550 : sortcolumn = code_societe
24/07/2025 : 13:09:19.550 : sortdir = ascending
24/07/2025 : 13:09:19.551 : Formatted dateStartParam = 
24/07/2025 : 13:09:19.551 : Formatted dateEndParam = 
24/07/2025 : 13:09:23.075 : Retrieved 53 items
24/07/2025 : 13:54:33.917 : dateFactureStart = 
24/07/2025 : 13:54:33.919 : dateFactureEnd = 
24/07/2025 : 13:54:33.919 : client = 
24/07/2025 : 13:54:33.919 : parc = 
24/07/2025 : 13:54:33.920 : sortcolumn = codeparc
24/07/2025 : 13:54:33.920 : sortdir = ascending
24/07/2025 : 13:54:33.920 : Formatted dateStartParam = 
24/07/2025 : 13:54:33.921 : Formatted dateEndParam = 
24/07/2025 : 13:54:34.156 : Retrieved 14 items
24/07/2025 : 13:54:40.936 : dateFactureStart = 
24/07/2025 : 13:54:40.937 : dateFactureEnd = 
24/07/2025 : 13:54:40.937 : client = 
24/07/2025 : 13:54:40.937 : parc = 
24/07/2025 : 13:54:40.937 : sortcolumn = codeparc
24/07/2025 : 13:54:40.937 : sortdir = ascending
24/07/2025 : 13:54:40.938 : Formatted dateStartParam = 
24/07/2025 : 13:54:40.938 : Formatted dateEndParam = 
24/07/2025 : 13:54:40.997 : Retrieved 14 items
24/07/2025 : 14:10:36.430 : dateFactureStart = 
24/07/2025 : 14:10:36.431 : dateFactureEnd = 
24/07/2025 : 14:10:36.431 : client = 
24/07/2025 : 14:10:36.431 : parc = 
24/07/2025 : 14:10:36.432 : sortcolumn = codeparc
24/07/2025 : 14:10:36.432 : sortdir = ascending
24/07/2025 : 14:10:36.432 : Formatted dateStartParam = 
24/07/2025 : 14:10:36.433 : Formatted dateEndParam = 
24/07/2025 : 14:10:36.560 : Retrieved 14 items
24/07/2025 : 14:10:39.107 : dateFactureStart = 
24/07/2025 : 14:10:39.108 : dateFactureEnd = 
24/07/2025 : 14:10:39.108 : client = 
24/07/2025 : 14:10:39.108 : parc = 
24/07/2025 : 14:10:39.109 : sortcolumn = codeparc
24/07/2025 : 14:10:39.109 : sortdir = ascending
24/07/2025 : 14:10:39.109 : Formatted dateStartParam = 
24/07/2025 : 14:10:39.110 : Formatted dateEndParam = 
24/07/2025 : 14:10:39.169 : Retrieved 14 items
