﻿@page "/ListParcGlobal"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using CarSystemsWebApp.Services
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection
@using Syncfusion.Drawing

@inject IJSRuntime      _jsRuntime
@inject SfDialogService _dialogService
@inject PageTitleService PageTitleService



<!-- ============== -->
<!-- PROFESSIONAL FILTER BAR -->
<!-- ============== -->

<div class="professional-filter-container">
    <div class="filter-header">
        <div class="filter-title">Filtres</div>
        <button class="btn-clear-all" @onclick="ClearAllFilters">Effacer tout</button>
    </div>

    <div class="row filter-row align-items-center">
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedCodeParc" ShowClearButton="true" Placeholder="Code parc..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date d'entrée..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateEntrée"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date mise en oeuvre..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateMiseService"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfButton Type="button" @onclick="Helper_Refresh" CssClass="btn-inline">
                Appliquer
            </SfButton>
        </div>
        <div class="col-auto">
            <SfButton OnClick="OnButtonExport" CssClass="btn-inline-export">
                Export
            </SfButton>
        </div>
    </div>
</div>

<!-- ======= -->
<!-- RESULTS -->
<!-- ======= -->

@* <SfToolbar>
    <ToolbarItems>

        <ToolbarItem Id=HRRefreshGrid
        PrefixIcon="e-icons e-refresh"
        Tooltiptext="Actualiser"
        OnClick="OnToolbarButtonRefresh" />

        <ToolbarItem Type=ItemType.Separator />

    </ToolbarItems>
</SfToolbar> *@

@* style="height: calc(100vh - 205px) !important;" *@

<div>
    <SfGrid ID="MyGrid" 
    TValue="CS_PARC_ITEM"
    @ref="@MyGrid"
    Query="@MyQuery"
    AllowPaging="true"
    AllowFiltering="false"
    AllowSorting="true"
    AllowResizing="true" EnablePersistence="true"
    ShowColumnChooser="true" Toolbar=@ToolbarItems
    EnableInfiniteScrolling="false"
    Height="100%">
        <GridEvents TValue="CS_PARC_ITEM"
        RowSelected="OnRowSelected" 
        OnDataBound="DataBoundHandler"
        OnActionBegin="OnActionBegin"
        OnActionComplete="OnActionComplete"/>
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="22" />
        <GridColumns>
            <GridColumn Field=@nameof(CS_PARC_ITEM.codeparc) HeaderText="codeparc" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.immat) HeaderText="immat" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.dateimmat) HeaderText="dateimmat" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codemodele) HeaderText="codemodele" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.libparc) HeaderText="libparc" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cattarif) HeaderText="cattarif" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codemarque) HeaderText="codemarque" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.typeparc) HeaderText="typeparc" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.puisscv) Visible="false" ShowInColumnChooser="true" HeaderText="puisscv" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.numserie) HeaderText="numserie" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cleserie) Visible="false" ShowInColumnChooser="true" HeaderText="cleserie" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.couleur) HeaderText="couleur" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.date1mes) Visible="false" ShowInColumnChooser="true" HeaderText="date1mes" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.anneemodele) HeaderText="anneemodele" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.dateentree) HeaderText="dateentree" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.datesortie) HeaderText="datesortie" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.datemiseserv) HeaderText="dateMiseServ" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.intext) Visible="false" ShowInColumnChooser="true" HeaderText="intext" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codesociete) HeaderText="codesociete" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codeparcfranchise) Visible="false" ShowInColumnChooser="true" HeaderText="codeparcfranchise" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.achatloc) Visible="false" ShowInColumnChooser="true" HeaderText="achatloc" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.revente)  Visible="false" ShowInColumnChooser="true" HeaderText="revente" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.daterevente) HeaderText="daterevente" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.gage) Visible="false" ShowInColumnChooser="true" HeaderText="gage" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.finance) Visible="false" ShowInColumnChooser="true" HeaderText="finance" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.neufoccas) Visible="false" ShowInColumnChooser="true" HeaderText="neufoccas" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cptentree) Visible="false" ShowInColumnChooser="true" HeaderText="cptentree" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.totcptentree) Visible="false" ShowInColumnChooser="true" HeaderText="totcptentree" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.numfactach) Visible="false" ShowInColumnChooser="true" HeaderText="numfactach" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codefourni) Visible="false" ShowInColumnChooser="true" HeaderText="codefourni" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.dateachat) HeaderText="dateachat" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.pxachat) Visible="false" ShowInColumnChooser="true" HeaderText="pxachat" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.prixcatalogue) HeaderText="prixcatalogue" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.datefingar) Visible="false" ShowInColumnChooser="true" HeaderText="datefingar" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.remplacement) HeaderText="remplacement" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codeparcrempl) HeaderText="codeparcrempl" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.sousparc) Visible="false" ShowInColumnChooser="true" HeaderText="sousparc" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codeagenceaffec) Visible="false" ShowInColumnChooser="true" HeaderText="codeagenceaffec" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.situadmin) Visible="false" ShowInColumnChooser="true" HeaderText="situadmin" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.situation) Visible="false" ShowInColumnChooser="true" HeaderText="situation" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.nummvtder) Visible="false" ShowInColumnChooser="true" HeaderText="nummvtder" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codeagencedispo) HeaderText="codeagencedispo" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codeclient) HeaderText="codeclient" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.codeconduct) HeaderText="codeconduct" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cpt) Visible="false" ShowInColumnChooser="true" HeaderText="cpt" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.datecpt) HeaderText="datecpt" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.numreleve) HeaderText="numreleve" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.reldebfin) HeaderText="reldebfin" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.prepare) Visible="false" ShowInColumnChooser="true" HeaderText="prepare" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.sscattarif) Visible="false" ShowInColumnChooser="true" HeaderText="sscattarif" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.equipaediter) Visible="false" ShowInColumnChooser="true" HeaderText="equipaediter" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cveditee)  Visible="false" ShowInColumnChooser="true" HeaderText="cveditee" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgemetteur) Visible="false" ShowInColumnChooser="true" HeaderText="cgemetteur" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgdate) Visible="false" ShowInColumnChooser="true" HeaderText="cgdate" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgnomproprie) Visible="false" ShowInColumnChooser="true" HeaderText="cgnomproprie" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgnomusage) Visible="false" ShowInColumnChooser="true" HeaderText="cgnomusage" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgdomicile) Visible="false" ShowInColumnChooser="true" HeaderText="cgdomicile" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgville) Visible="false" ShowInColumnChooser="true" HeaderText="cgville" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cggenre) Visible="false" ShowInColumnChooser="true" HeaderText="cggenre" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgtype) Visible="false" ShowInColumnChooser="true" HeaderText="cgtype" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgcarross) Visible="false" ShowInColumnChooser="true" HeaderText="cgcarross" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.placeassise) Visible="false" ShowInColumnChooser="true" HeaderText="placeassise" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.placedebout) Visible="false" ShowInColumnChooser="true" HeaderText="placedebout" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cglargeur) Visible="false" ShowInColumnChooser="true" HeaderText="cglargeur" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgsurface) Visible="false" ShowInColumnChooser="true" HeaderText="cgsurface" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgptc) Visible="false" ShowInColumnChooser="true" HeaderText="cgptc" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgpv) Visible="false" ShowInColumnChooser="true" HeaderText="cgpv" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgptr) Visible="false" ShowInColumnChooser="true" HeaderText="cgptr" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgbruit) Visible="false" ShowInColumnChooser="true" HeaderText="cgbruit" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgregime) Visible="false" ShowInColumnChooser="true" HeaderText="cgregime" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgdateprec) Visible="false" ShowInColumnChooser="true" HeaderText="cgdateprec" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgnumprec) Visible="false" ShowInColumnChooser="true" HeaderText="cgnumprec" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.cgnumimprime) Visible="false" ShowInColumnChooser="true" HeaderText="cgnumimprime" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40alp1) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp1" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40alp2) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp2" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40alp3)  Visible="false" ShowInColumnChooser="true"HeaderText="z40alp3" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40alp4) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp4" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40alp5) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp5" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40num1) Visible="false" ShowInColumnChooser="true" HeaderText="z40num1" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40num2) Visible="false" ShowInColumnChooser="true" HeaderText="z40num2" />
            <GridColumn Field=@nameof(CS_PARC_ITEM.z40num3) Visible="false" ShowInColumnChooser="true" HeaderText="z40num3" />

        </GridColumns>
    </SfGrid>
</div>

<script type="text/javascript">

    function saveAsFile(filename, bytesBase64) {
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++) {
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_PARC_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;


    private string    SelectedCodeParc;
    private DateTime? SelectedDateEntrée;
    private DateTime? SelectedDateMiseService;



    protected override void OnInitialized()
    {
        // Set page title
        PageTitleService.SetTitle("Liste Parc Global");

        this.MyQuery = new Query()
            .AddParams("CodeParc", null)
            .AddParams("DateEntree", null)
            .AddParams("DateMiseService", null);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_PARC_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_PARC_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_PARC_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_PARC_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["CodeParc"] = this.SelectedCodeParc;
        MyQuery.Queries.Params["DateEntree"] = this.SelectedDateEntrée;
        MyQuery.Queries.Params["DateMiseService"] = this.SelectedDateMiseService;

        // Reset pagination to first page when applying new filters
        await MyGrid.GoToPageAsync(1);
        await MyGrid.Refresh();
    }

    private async Task ClearAllFilters()
    {
        SelectedCodeParc = null;
        SelectedDateEntrée = null;
        SelectedDateMiseService = null;
        await Helper_Refresh();
    }

    private async void OnButtonExport()
    {
        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // Create a workbook with 1 worksheet
            IWorkbook workbook = application.Workbooks.Create(1);
            IWorksheet worksheet = workbook.Worksheets[0];
            worksheet.Name = "PARC_GLOBAL";

            // Get data for export (large limit to get all data)
            string sortcolumn = "code_societe";
            string sortdir = "ascending";

            var items = MyCustomAdaptor.Helper_GetData(
                this.SelectedCodeParc,
                this.SelectedDateEntrée,
                this.SelectedDateMiseService,
                0, 100000,
                sortcolumn, sortdir, 900); // 15 minute timeout for export

            // Create a professional header design with WafaLLD orange branding
            // Main title with orange background
            worksheet["A1"].Text = "EXPORT LISTE PARC GLOBAL";
            worksheet["A1"].CellStyle.Font.Bold = true;
            worksheet["A1"].CellStyle.Font.Size = 16;
            worksheet["A1"].CellStyle.Font.Color = ExcelKnownColors.White;
            worksheet["A1"].CellStyle.Color = Color.FromArgb(255, 140, 0); // WafaLLD Orange background
            worksheet["A1"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            worksheet["A1"].CellStyle.VerticalAlignment = ExcelVAlign.VAlignCenter;
            worksheet.Range["A1:R1"].Merge();
            worksheet.Range["A1:R1"].RowHeight = 25;

            // Export info with light orange background
            worksheet["A2"].Text = $"Exporté le: {DateTime.Now:dd/MM/yyyy HH:mm}";
            worksheet["A2"].CellStyle.Font.Size = 10;
            worksheet["A2"].CellStyle.Font.Italic = true;
            worksheet["A2"].CellStyle.Color = Color.FromArgb(255, 228, 196); // Light orange background
            worksheet["A2"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A2:R2"].Merge();

            // Add filter criteria info
            string filterInfo = "Filtres appliqués: ";
            if (!string.IsNullOrEmpty(SelectedCodeParc))
            {
                filterInfo += $"Code Parc: {SelectedCodeParc} | ";
            }
            if (SelectedDateEntrée.HasValue)
            {
                filterInfo += $"Date Entrée: {SelectedDateEntrée?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (SelectedDateMiseService.HasValue)
            {
                filterInfo += $"Date Mise en Service: {SelectedDateMiseService?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (filterInfo.EndsWith(" | "))
            {
                filterInfo = filterInfo.Substring(0, filterInfo.Length - 3);
            }
            else if (filterInfo == "Filtres appliqués: ")
            {
                filterInfo = "Aucun filtre appliqué";
            }

            worksheet["A3"].Text = filterInfo;
            worksheet["A3"].CellStyle.Font.Size = 9;
            worksheet["A3"].CellStyle.Color = Color.FromArgb(255, 248, 240); // Very light orange/cream
            worksheet["A3"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A3:R3"].Merge();

            // Add a separator line with orange accent
            worksheet.Range["A4:R4"].CellStyle.Color = Color.FromArgb(255, 165, 0); // Orange accent line
            worksheet.Range["A4:R4"].RowHeight = 3;

            // Convert to DataTable for Excel export
            DataTable table = new DataTable();
            Type type = typeof(CS_PARC_ITEM);
            PropertyInfo[] properties = type.GetProperties();

            // Create columns
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);

            // Add data rows
            foreach (var item in items)
            {
                DataRow row = table.NewRow();
                int k = 0;
                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }
                table.Rows.Add(row);
            }

            // Export data to Excel worksheet starting from row 6 to leave space for header design
            worksheet.ImportDataTable(table, true, 6, 1);

            // Style the data header row (column names) with orange theme
            IRange headerRange = worksheet.Range[6, 1, 6, properties.Length];
            headerRange.CellStyle.Font.Bold = true;
            headerRange.CellStyle.Font.Color = ExcelKnownColors.White;
            headerRange.CellStyle.Color = Color.FromArgb(255, 165, 0); // Medium orange background
            headerRange.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
            headerRange.RowHeight = 20;

            // Add alternating row colors and borders to all data
            if (items.Count > 0)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    int rowIndex = 7 + i; // Start from row 7 (after header)
                    IRange rowRange = worksheet.Range[rowIndex, 1, rowIndex, properties.Length];

                    // Alternating row colors with orange theme
                    if (i % 2 == 0)
                    {
                        rowRange.CellStyle.Color = Color.FromArgb(255, 250, 240); // Very light orange/cream
                    }
                    else
                    {
                        rowRange.CellStyle.Color = Color.White;
                    }

                    // Add borders
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
                }
            }

            worksheet.UsedRange.AutofitColumns();

            // Save and download
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                string filename = $"ExportParcGlobal_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "LISTPARCGLOBAL-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                // Extract parameters (null values are allowed for no filtering)
                string codeParc = null;
                DateTime? dateEntree = null;
                DateTime? dateMiseService = null;

                if (dm.Params != null && dm.Params.Count > 0)
                {
                    codeParc = dm.Params["CodeParc"] as string;
                    dateEntree = dm.Params["DateEntree"] as DateTime?;
                    dateMiseService = dm.Params["DateMiseService"] as DateTime?;
                }

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"CodeParc       = {codeParc}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateEntree      = {dateEntree}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateMiseService = {dateMiseService}", true);

                // string sortcolumn   = "code_societe";  // OLD: Hardcoded sorting
                // string sortdir      = "ascending";     // OLD: Hardcoded sorting

                // NEW: Dynamic sorting from DataManagerRequest
                string sortcolumn   = "codeparc";  // Default sort column
                string sortdir      = "ascending";  // Default sort direction

                // Extract sorting information from DataManagerRequest
                if (dm.Sorted != null && dm.Sorted.Count > 0)
                {
                    var sortInfo = dm.Sorted[0]; // Get the first sort column
                    sortcolumn = sortInfo.Name;
                    sortdir = sortInfo.Direction;
                }

                CarSystemsContext model;
                model = new CarSystemsContext();

                var items = Helper_GetData(codeParc, dateEntree, dateMiseService, dm.Skip, dm.Take, sortcolumn, sortdir);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetListParcGlobalItemsCount_V2 {0}, {1}, {2}",
                        codeParc,
                        dateEntree,
                        dateMiseService)
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);
                return null;
            }
        }

        public static List<CS_PARC_ITEM> Helper_GetData(string codeParc, DateTime? dateStart, DateTime? dateFin,  int skip, int take, string sortcolumn, string sortdir, int timeoutSeconds = 30)
        {
            var logKey = "LISTPARCGLOBAL-HELPER_GETDATA";
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateStart = {dateStart}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateFin = {dateFin}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"codeParc = {codeParc}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortcolumn = {sortcolumn}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortdir = {sortdir}", true);

            try
            {
                CarSystemsContext model;
                model = new CarSystemsContext();

                // Set command timeout for long-running operations
                model.Database.SetCommandTimeout(timeoutSeconds);

                // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
                object dateStartParam = dateStart.HasValue ? (object)dateStart.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateFinParam = dateFin.HasValue ? (object)dateFin.Value.ToString("yyyy-MM-dd") : DBNull.Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateStartParam = {dateStartParam}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateFinParam = {dateFinParam}", true);

                var items = model.CS_PARC_ITEMS.FromSqlRaw("EXECUTE dbo.GetListParcGlobalItems_V2 {0}, {1}, {2}, {3}, {4}, {5}, {6}",
                    codeParc,
                    dateStartParam,
                    dateFinParam,
                    skip,
                    take,
                    sortcolumn,
                    sortdir).ToList();

                CarSystemsWebApp.Utils.Helper_Trace("HELPER_GETDATA_PAGE08", $"SUCCESS: Retrieved {items.Count} items", true);
                return items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace("HELPER_GETDATA_PAGE08", $"ERROR: {ex.Message}", true);
                return new List<CS_PARC_ITEM>();
            }
        }
    }
}
