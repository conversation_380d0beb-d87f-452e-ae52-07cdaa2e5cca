15/07/2025 : 08:04:09.597 : Calling <PERSON> with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 08:04:22.666 : Calling <PERSON> with sortcolumn=facavoir, sortdir=ascending
15/07/2025 : 08:05:21.645 : Calling <PERSON> with sortcolumn=montant, sortdir=ascending
15/07/2025 : 08:05:24.461 : Calling <PERSON> with sortcolumn=montant, sortdir=descending
15/07/2025 : 08:05:26.317 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 08:05:30.803 : Calling SP with sortcolumn=montant, sortdir=ascending
15/07/2025 : 08:05:32.710 : Calling SP with sortcolumn=montant, sortdir=descending
15/07/2025 : 08:05:44.400 : Calling SP with sortcolumn=libelle, sortdir=ascending
15/07/2025 : 08:05:46.746 : Calling SP with sortcolumn=libelle, sortdir=descending
15/07/2025 : 08:05:51.606 : Calling <PERSON> with sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 08:05:53.622 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 08:05:59.924 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 08:06:01.825 : Calling SP with sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 08:06:03.750 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 08:06:05.654 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 08:12:58.726 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 08:13:04.494 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 09:17:05.014 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 09:17:16.738 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:35:33.200 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:35:38.599 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:35:51.151 : Calling SP with sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 10:35:55.273 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:35:57.717 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:36:00.313 : Calling SP with sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 10:36:05.028 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:36:07.234 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:36:09.364 : Calling SP with sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 10:36:11.706 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:37:05.451 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:42:18.672 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:42:21.075 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:43:28.529 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:43:37.104 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:43:42.036 : Calling SP with sortcolumn=codetiers, sortdir=ascending
15/07/2025 : 10:43:42.698 : Calling SP with sortcolumn=codetiers, sortdir=descending
15/07/2025 : 10:48:13.679 : Calling SP with sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:48:16.631 : Calling SP with sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:54:28.615 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:54:32.150 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 10:57:47.420 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 10:58:05.321 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 10:58:21.609 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:58:21.843 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 10:58:38.921 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 10:58:52.187 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:58:52.320 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 10:59:08.406 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 10:59:21.403 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:59:21.557 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 10:59:36.202 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 10:59:48.778 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 10:59:48.896 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:00:08.720 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=etat, sortdir=ascending
15/07/2025 : 11:00:10.209 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=etat, sortdir=descending
15/07/2025 : 11:00:12.298 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:00:12.427 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=etat, sortdir=ascending
15/07/2025 : 11:00:13.906 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 11:00:15.139 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 11:00:16.226 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:00:17.222 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:00:36.292 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 11:00:51.529 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:00:51.751 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:01:09.298 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 11:01:23.694 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:01:23.838 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:01:37.660 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 11:01:50.479 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:01:50.582 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:02:04.951 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:02:12.183 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:02:19.712 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:02:27.176 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:05:12.040 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 11:09:55.995 : ORIGINAL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:09:55.996 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:09:55.998 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:09:58.651 : ERROR: Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:09:58.691 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 488
15/07/2025 : 11:10:02.221 : ORIGINAL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:10:02.222 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:10:02.222 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:10:02.398 : ERROR: Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:10:02.399 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 488
15/07/2025 : 11:10:05.976 : ORIGINAL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:10:05.976 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:10:05.977 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:10:06.102 : ERROR: Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:10:06.102 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 488
15/07/2025 : 11:11:36.470 : ORIGINAL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:11:36.471 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:11:36.473 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:11:36.653 : ERROR: Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:11:36.654 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 488
15/07/2025 : 11:23:24.624 : ORIGINAL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:23:24.625 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:23:24.626 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:23:24.936 : ERROR: Unable to cast object of type 'System.DateTime' to type 'System.String'.
15/07/2025 : 11:23:24.937 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 488
15/07/2025 : 11:27:12.076 : ORIGINAL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:27:12.077 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:27:12.081 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:27:14.941 : SUCCESS: Retrieved 100 items with STATIC test
15/07/2025 : 11:27:17.949 : ORIGINAL Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:27:17.949 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:27:17.952 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:27:18.088 : SUCCESS: Retrieved 100 items with STATIC test
15/07/2025 : 11:29:02.953 : ORIGINAL Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:29:02.953 : STATIC TEST Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:29:02.954 : About to execute stored procedure with STATIC values...
15/07/2025 : 11:29:03.049 : SUCCESS: Retrieved 100 items with STATIC test
15/07/2025 : 11:33:28.370 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:33:31.383 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:33:36.303 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:33:37.309 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:33:56.026 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:33:56.583 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:34:05.530 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:34:07.055 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:34:09.045 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 11:34:09.263 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:46:46.856 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:46:50.533 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:46:55.804 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:46:55.915 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:47:45.966 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:47:47.201 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:47:49.995 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 11:47:50.097 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:49:15.487 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 11:49:16.803 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:49:18.902 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:49:20.433 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:59:23.517 : Parameters: client=, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 11:59:32.242 : SUCCESS: Retrieved 14 items
15/07/2025 : 11:59:52.440 : Parameters: client=, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=84, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 12:00:04.164 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:00:30.463 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=84, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 12:00:30.737 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:00:43.965 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 12:00:44.236 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:00:57.143 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=libelle, sortdir=ascending
15/07/2025 : 12:00:57.471 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:01:01.093 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=libelle, sortdir=descending
15/07/2025 : 12:01:01.431 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:01:04.656 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 12:01:34.728 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 12:01:34.750 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 473
15/07/2025 : 12:01:38.380 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=14616, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 12:02:00.627 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 12:02:00.893 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:02:05.695 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 12:02:06.919 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:02:08.452 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 12:02:08.454 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 473
15/07/2025 : 12:02:13.306 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=14616, take=14, sortcolumn=libelle, sortdir=ascending
15/07/2025 : 12:02:13.643 : SUCCESS: Retrieved 2 items
15/07/2025 : 12:02:17.453 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=14616, take=14, sortcolumn=tottvaprestht, sortdir=ascending
15/07/2025 : 12:02:18.040 : SUCCESS: Retrieved 2 items
15/07/2025 : 12:02:21.581 : Parameters: client=0028, dateStart=10/07/2009 00:00:00, dateEnd=17/07/2020 00:00:00, etat=, typeFacture=, skip=14616, take=14, sortcolumn=tottvaprestht, sortdir=descending
15/07/2025 : 12:02:21.959 : SUCCESS: Retrieved 2 items
15/07/2025 : 12:10:48.594 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 12:10:48.718 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:10:53.537 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 12:10:54.574 : SUCCESS: Retrieved 14 items
15/07/2025 : 12:11:03.271 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 12:11:04.104 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:19:27.707 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:19:31.713 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:19:35.274 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 13:19:36.661 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:19:38.977 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100000, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:19:45.585 : SUCCESS: Retrieved 100000 items
15/07/2025 : 13:19:45.767 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=100000, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:19:51.647 : SUCCESS: Retrieved 100000 items
15/07/2025 : 13:19:56.599 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:19:56.694 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:20:00.183 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 13:20:01.540 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:30:35.081 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:30:36.690 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:30:40.206 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 13:30:41.596 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:30:47.162 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 13:30:48.294 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:31:08.475 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 13:31:09.350 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:42:59.285 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:43:01.365 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:43:04.048 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 13:43:05.537 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:43:21.035 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=integre, sortdir=ascending
15/07/2025 : 13:43:21.287 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:43:36.612 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:43:36.667 : SUCCESS: Retrieved 14 items
15/07/2025 : 13:43:40.999 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 13:43:41.051 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:03:22.666 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:03:24.366 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:03:28.562 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:03:28.676 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:04:34.750 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 14:04:35.791 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:04:37.652 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 14:04:38.623 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:04:40.952 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:04:41.126 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:10:13.166 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:10:14.877 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:10:18.939 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:10:19.174 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:10:28.618 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:10:30.591 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:10:48.423 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 14:10:58.187 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:11:00.110 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 14:11:08.275 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:11:10.193 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:11:11.097 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:11:12.935 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 14:11:20.366 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:14:12.072 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:14:13.521 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:14:18.521 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 14:14:48.985 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 14:14:49.017 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 503
15/07/2025 : 14:14:54.296 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:14:54.841 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:14:59.836 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 14:15:30.103 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 14:15:30.105 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 503
15/07/2025 : 14:19:49.483 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:19:51.140 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:19:53.823 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 14:20:24.046 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 14:20:24.066 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 503
15/07/2025 : 14:20:24.252 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:20:25.304 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:20:28.963 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:20:29.024 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:22:52.338 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:22:54.091 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:22:57.731 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:22:57.849 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:24:02.380 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:24:02.440 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:24:04.674 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:24:04.731 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:29:22.462 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:29:24.137 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:29:26.653 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:29:26.766 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:30:21.783 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:30:21.838 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:30:23.981 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:30:24.033 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:32:49.598 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:32:51.933 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:32:54.710 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:32:54.869 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:14.278 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:14.336 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:16.579 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:16.635 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:29.521 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:29.578 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:33.485 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:33.544 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:35.647 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:35.702 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:37.572 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:37.634 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:43.529 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:43.586 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:45.572 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:45.629 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:50.053 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:50.118 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:54.031 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:54.090 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:56.220 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:56.279 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:33:58.390 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:33:58.451 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:34:01.095 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:34:01.162 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:34:04.257 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:34:04.313 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:34:06.255 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:34:06.317 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:42:07.136 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:42:08.840 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:42:11.309 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:42:11.425 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:42:25.630 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:42:25.700 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:42:27.766 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:42:27.824 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:49:54.486 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:49:56.132 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:49:59.745 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:49:59.876 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:50:19.663 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:50:19.733 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:50:21.894 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:50:21.953 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:50:25.190 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:50:25.251 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:50:27.283 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:50:27.342 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:50:29.240 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:50:29.300 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:50:31.505 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:50:31.564 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:50:39.235 : Parameters: client=, dateStart=, dateEnd=18/07/2025 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:51:09.346 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 14:51:09.408 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 502
15/07/2025 : 14:55:47.016 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:55:48.672 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:55:49.193 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:55:49.813 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:55:53.301 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:55:53.365 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:56:07.884 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:56:07.962 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:56:13.633 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 14:56:14.639 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:56:16.658 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 14:56:17.384 : SUCCESS: Retrieved 14 items
15/07/2025 : 14:56:53.163 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 14:56:53.223 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:02:03.456 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:02:05.089 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:02:07.620 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 15:02:08.726 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:04:30.784 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:04:34.848 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:04:37.724 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 15:04:39.187 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:04:42.900 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:04:42.961 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:04:44.993 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 15:04:45.998 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:05:09.809 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:05:09.868 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:05:19.268 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:05:20.532 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:05:29.484 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:05:31.765 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:06:11.730 : Parameters: client=0026, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:06:13.673 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:07:19.705 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:07:21.440 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:07:23.911 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:07:24.039 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:09:29.324 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:09:31.083 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:09:33.481 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:09:33.612 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:10:42.171 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:10:44.704 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:10:47.174 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:10:47.389 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:10:58.867 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 15:11:29.072 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 15:11:29.106 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 491
15/07/2025 : 15:11:31.353 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=8832124, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 15:12:01.547 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 15:12:01.549 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 491
15/07/2025 : 15:12:04.127 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=8832124, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 15:12:34.339 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 15:12:34.341 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 491
15/07/2025 : 15:12:36.310 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=8832124, take=14, sortcolumn=montant, sortdir=descending
15/07/2025 : 15:13:06.890 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 15:13:06.891 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 491
15/07/2025 : 15:13:09.023 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=8832124, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:13:39.084 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 15:13:39.085 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 491
15/07/2025 : 15:13:40.957 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=8832124, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:14:11.029 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 15:14:11.030 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 491
15/07/2025 : 15:14:13.019 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=8832124, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 15:14:43.260 : ERROR: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
15/07/2025 : 15:14:43.260 : STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel_.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\Users\<USER>\Desktop\Mabchour\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel .razor:line 491
15/07/2025 : 15:14:45.443 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=8832124, take=14, sortcolumn=montant, sortdir=ascending
15/07/2025 : 15:15:27.304 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:15:28.832 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:15:31.476 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:15:31.623 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:15:54.300 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:15:54.358 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:15:56.860 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:15:56.951 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:16:57.598 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:16:57.656 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:17:00.050 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:17:00.233 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:18:44.186 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:18:45.920 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:18:48.493 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:18:48.615 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:19:52.148 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:19:53.888 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:19:59.227 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 15:19:59.366 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:20:05.679 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 15:20:07.047 : SUCCESS: Retrieved 14 items
15/07/2025 : 15:20:09.249 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 15:20:10.332 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:06:34.620 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:06:36.855 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:06:39.720 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 21:06:40.874 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:19:55.085 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:19:55.432 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:20:00.553 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 21:20:01.826 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:21:49.033 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:21:50.995 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:21:53.255 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 21:21:54.284 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:21:56.385 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:21:56.451 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:21:58.778 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:21:58.841 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:23:08.219 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:23:10.421 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:23:12.659 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:23:12.798 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:24:05.374 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:24:06.447 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:24:08.497 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 21:24:09.247 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:24:11.463 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:24:11.532 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:24:13.463 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:24:14.268 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:24:16.102 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 21:24:16.930 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:24:19.267 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:24:19.336 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:24:22.184 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:24:23.073 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:28:02.768 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:28:04.450 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:28:07.562 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:28:08.765 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:35:05.273 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:35:10.304 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:35:13.671 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:35:14.961 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:38:38.185 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:38:39.736 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:38:42.129 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:38:42.964 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:44:51.701 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:44:53.028 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:44:56.583 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:44:58.499 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:49:36.732 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:49:38.236 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:49:40.719 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:49:41.649 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:55:46.551 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:55:48.130 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:55:50.765 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:55:51.688 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:58:47.432 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 21:58:48.756 : SUCCESS: Retrieved 14 items
15/07/2025 : 21:58:50.835 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 21:58:51.700 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:01:23.323 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:01:24.645 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:01:27.334 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:01:28.421 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:01:59.050 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:01:59.141 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:02:01.059 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:02:02.006 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:02:10.098 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:02:10.170 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:02:12.242 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:02:13.083 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:03:37.616 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:03:37.686 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:03:39.741 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:03:40.561 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:03:58.218 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 22:03:59.101 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:04:01.060 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:04:01.127 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:04:03.264 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:04:04.175 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:06:37.596 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:06:39.050 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:06:41.064 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:06:41.820 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:07:01.190 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:07:01.255 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:07:03.217 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:07:03.963 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:09:11.381 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:09:12.784 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:09:15.096 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:09:16.051 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:11:06.585 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:11:08.050 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:11:10.568 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:11:11.531 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:15:06.544 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:15:07.851 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:15:09.919 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:15:10.691 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:15:21.982 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:15:22.045 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:15:23.926 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:15:24.865 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:23:23.223 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:23:24.602 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:23:27.142 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:23:28.185 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:25:37.931 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:25:39.311 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:25:41.362 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:25:41.479 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:26:22.651 : Parameters: client=, dateStart=, dateEnd=, etat=V, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:26:22.725 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:28:56.223 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:28:57.575 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:28:59.718 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:28:59.833 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:29:01.756 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:29:01.821 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:29:11.969 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=typefac, sortdir=ascending
15/07/2025 : 22:29:13.043 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:29:19.967 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=typetva, sortdir=ascending
15/07/2025 : 22:29:34.967 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:29:59.743 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:29:59.814 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:32:01.681 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:32:03.047 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:32:03.645 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:32:04.573 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:32:06.681 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=typetva, sortdir=ascending
15/07/2025 : 22:32:19.998 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:33:05.277 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:33:05.343 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:33:21.451 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:33:22.143 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:33:23.996 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 22:33:24.728 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:33:26.505 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:33:26.567 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:33:32.200 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:33:32.993 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:35:24.858 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=descending
15/07/2025 : 22:35:25.828 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:35:27.903 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:35:28.082 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:37:15.987 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=98, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:37:16.069 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:38:35.162 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=codeparc, sortdir=ascending
15/07/2025 : 22:38:38.232 : SUCCESS: Retrieved 14 items
15/07/2025 : 22:38:40.768 : Parameters: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=datecrea, sortdir=ascending
15/07/2025 : 22:38:41.826 : SUCCESS: Retrieved 14 items
