﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;

namespace CarSystemsWebApp.Models;

public partial class CarSystemsContext : DbContext
{
    public virtual DbSet<CS_ECR_ITEM>CS_ECR_ITEMS {get;set;}
    public virtual DbSet<CS_IMMO_ITEM>CS_IMMO_ITEMS {get;set;}

    public virtual DbSet<CS_MVT_ITEM>CS_MVT_ITEMS {get;set;}
    public virtual DbSet<CS_MVTG_ITEM> CS_MVTG_ITEMS { get; set; }

    public virtual DbSet<CS_LDANX_ITEM>CS_LDANX_ITEMS {get;set;}

    public virtual DbSet<CS_PARC_ITEM> CS_PARC_ITEMS { get; set; }

    public virtual DbSet<CS_FAC_AI_JOINED_TEMP_ITEM>CS_FAC_AI_JOINED_TEMP_ITEMS { get; set; }

    public virtual DbSet<CS_FAC_AI_JOINED_TEMP2_ITEM>CS_FAC_AI_JOINED_TEMP2_ITEMS { get; set; }
    

    private void OnModelCreatingEx(ModelBuilder modelBuilder)
    {
        /*
        modelBuilder.HasDbFunction(typeof(VOICEDESKContext).GetMethod(
            nameof(GetGridTicketsDatasourceCount),
            new[] { typeof(int), typeof(string), typeof(int?), typeof(string), typeof(string) }));
        */

        modelBuilder.Entity<IntReturn>().HasNoKey();

        // Configure CS_FAC_AI_JOINED_TEMP_ITEM entity for FromSqlRaw operations
        modelBuilder.Entity<CS_FAC_AI_JOINED_TEMP_ITEM>(entity =>
        {
            entity.HasKey(e => e.UniqueID);
            entity.ToTable("CS_FAC_AI_JOINED_TEMP_ITEM"); // Virtual table for stored procedure results
        });
    }
}

public class CS_IMMO_ITEM
{
    [Key]
    public long UniqueID { get; set; }
    public string numimmo { get; set; }
    public string codeparc { get; set; }
    public DateTime? dateachat { get; set; }  // Changed from dateachat2 to dateachat
    public string libelle { get; set; }
    public string baseamort { get; set; }
    public DateTime? datedepamort { get; set; }  // Changed from datedepamort2 to datedepamort
    public string amortexe { get; set; }
    public string valresdercalcul { get; set; }
}

public class CS_ECR_ITEM
{
    [Key]
    public long UniqueID { get; set; }
    public string code_societe { get; set; }
    public string num_ex { get; set; }
    public string code_jrn { get; set; }
    public string num_ecr{ get; set; } 
    public string numlig{ get; set; }
    public string numlig_let{ get; set; } 
    public string numlig_tva{ get; set; } 
    public string code_etablissement{ get; set; } 
    // public string datecompta{ get; set; }
    public string type{ get; set; } 
    public string etat{ get; set; } 
    public string typpiece{ get; set; } 
    public string numpiece{ get; set; } 
    public string datepiece{ get; set; } 
    public string num_compte_gen{ get; set; } 
    public string num_compte_aux{ get; set; } 
    public string libelle{ get; set; } 
    public string debit{ get; set; } 
    public string credit{ get; set; } 
    public string dateech{ get; set; }
    public string code_reg{ get; set; } 
    public string reflettrage{ get; set; } 
    public string let{ get; set; } 
    public string letpartiel{ get; set; } 
    public string datelettrage{ get; set; }
    public string letsuivant{ get; set; } 
    public string pointe{ get; set; } 
    public string code_tva{ get; set; } 
    public string typassutva{ get; set; } 
    public string litige{ get; set; } 
    public string central{ get; set; }
    public string annul{ get; set; } 
    public string transfer{ get; set; } 
    public string dateheuretransfer{ get; set; } 
    public string code_parc{ get; set; } 
    public string code_prest{ get; set; }
    public string code_agence{ get; set; } 
    public string datecrea{ get; set; } 
    public string datemodi{ get; set; } 
    public string code_utilis_crea{ get; set; } 
    public string code_utilis_modi{ get; set; } 
    public string zalp1{ get; set; } 
    public string zalp2{ get; set; } 
    public string zalp3{ get; set; } 
    public string zdat1{ get; set; } 
    public string zdat2{ get; set; } 
    public string zdat3{ get; set; }
    public string znum1{ get; set; } 
    public string znum2{ get; set; } 
    public string znum3{ get; set; } 
    public string zonecle{ get; set; } 
    public string saisie{ get; set; } 
    public string debiteuro{ get; set; }
    public string crediteuro{ get; set; } 
    public string debitpays{ get; set; } 
    public string creditpays{ get; set; } 
    public string deviserefeuro{ get; set; } 
    public string moistva{ get; set; }
    public string datevaleur{ get; set; } 
    public string num_reg{ get; set; } 
    public string contrepartie{ get; set; } 
    public string moyenpaie{ get; set; } 
    public string numfacfisc{ get; set; }
    public string extractDW{ get; set; } 
    public string z40alp1{ get; set; } 
    public string z40alp2{ get; set; } 
    public string z40alp3{ get; set; } 
    public string z40alp4{ get; set; } 
    public string z40alp5 { get; set; }

    public DateTime? datecompta2 { get; set; }
}

public class IntReturn
{
    public int Value { get; set; }
}

public class CS_MVT_ITEM
{
    [Key]
    public long UniqueID { get; set; }
    public string nummvt { get; set; }
    public string codeparc { get; set; }
    public string etat { get; set; }
    public string typee { get; set; }
    public string refmvt { get; set; }
    public string numldct { get; set; }
    public string codeclient { get; set; }
    public string numldanx { get; set; }
    public string numcdct { get; set; }
    public string numcdanx { get; set; }
    public string typecmdent { get; set; }
    public string numcmdent { get; set; }
    public DateTime? datedeb { get; set; }  // Changed from datedeb2 to datedeb to match stored procedure
    public string heuredeb { get; set; }
    public string dateheuredeb { get; set; }
    public string codeagencedep { get; set; }
    public string lieudep { get; set; }
    public string relevecptdeb { get; set; }
    public string motifdeb { get; set; }
    public string niveaudeb { get; set; }
    public DateTime? datefin { get; set; }  // Changed from datefin2 to datefin to match stored procedure
    public string heurefin { get; set; }
    public string codeagenceret { get; set; }
    public string lieuret { get; set; }
    public string relevecptfin { get; set; }
    public string motiffin { get; set; }
    public string niveaufin { get; set; }
    public DateTime? datecrea { get; set; }  // Added to match stored procedure output
    public DateTime? datemodi { get; set; }  // Added to match stored procedure output
    public string codeutiliscrea { get; set; }  // Added to match stored procedure output
    public string codeutilismodi { get; set; }  // Added to match stored procedure output
}

public class CS_MVTG_ITEM
{
    [Key]
    public long UniqueID { get; set; }
    public string codeparc { get; set; }
    public string immat { get; set; }
    public string dateimmat { get; set; }
    public string codemodele { get; set; }
    public string libparc { get; set; }
    public string cattarif { get; set; }
    public string codemarque { get; set; }
    public string typeparc { get; set; }
    public string puisscv { get; set; }
    public string numserie { get; set; }
    public string cleserie { get; set; }
    public string couleur { get; set; }
    public string date1mes { get; set; }
    public string anneemodele { get; set; }
    public string dateentree { get; set; }
    public string datesortie { get; set; }
    public string intext { get; set; }
    public string codesociete { get; set; }
    public string codeparcfranchise { get; set; }
    public string achatloc { get; set; }
    public string revente { get; set; }
    public string daterevente { get; set; }
    public string gage { get; set; }
    public string finance { get; set; }
    public string neufoccas { get; set; }
    public string cptentree { get; set; }
    public string totcptentree { get; set; }
    public string numfactach { get; set; }
    public string codefourni { get; set; }
    public string dateachat { get; set; }
    public string pxachat { get; set; }
    public string prixcatalogue { get; set; }
    public string datefingar { get; set; }
    public string remplacement { get; set; }
    public string codeparcrempl { get; set; }
    public string sousparc { get; set; }
    public string codeagenceaffec { get; set; }
    public string situadmin { get; set; }
    public string situation { get; set; }
    public string nummvtder { get; set; }
    public string codeagencedispo { get; set; }
    public string codeclient { get; set; }
    public string codeconduct { get; set; }
    public string cpt { get; set; }
    public string datecpt { get; set; }
    public string numreleve { get; set; }
    public string reldebfin { get; set; }
    public string prepare { get; set; }
    public string sscattarif { get; set; }
    public string equipaediter { get; set; }
    public string cveditee { get; set; }
    public string cgemetteur { get; set; }
    public string cgdate { get; set; }
    public string cgnomproprie { get; set; }
    public string cgnomusage { get; set; }
    public string cgdomicile { get; set; }
    public string cgville { get; set; }
    public string cggenre { get; set; }
    public string cgtype { get; set; }
    public string cgcarross { get; set; }
    public string placeassise { get; set; }
    public string placedebout { get; set; }
    public string cglargeur { get; set; }
    public string cgsurface { get; set; }
    public string cgptc { get; set; }
    public string cgpv { get; set; }
    public string cgptr { get; set; }
    public string cgbruit { get; set; }
    public string cgregime { get; set; }
    public string cgdateprec { get; set; }
    public string cgnumprec { get; set; }
    public string cgnumimprime { get; set; }
    public string cgduplicata { get; set; }
    public string cgdatedupli { get; set; }
    public string cgtitsignat { get; set; }
    public string cgsignat { get; set; }
    public string cgtexte { get; set; }
    public string dateblocage { get; set; }
    public string transfere { get; set; }
    public string datecrea { get; set; }
    public string datemodi { get; set; }
    public string codeutiliscrea { get; set; }
    public string codeutilismodi { get; set; }
    public string zalp1 { get; set; }
    public string zalp2 { get; set; }
    public string zalp3 { get; set; }
    public string zdat1 { get; set; }
    public string zdat2 { get; set; }
    public string zdat3 { get; set; }
    public string znum1 { get; set; }
    public string znum2 { get; set; }
    public string znum3 { get; set; }
    public string zonecle { get; set; }
    public string immatprov { get; set; }
    public string codemodelecarrosserie { get; set; }
    public string codefournient { get; set; }
    public string policeassu { get; set; }
    public string dureeprev { get; set; }
    public string cptprev { get; set; }
    public string motifsorti { get; set; }
    public string vignette { get; set; }
    public string vignettegratuite { get; set; }
    public string classevign { get; set; }
    public string achatvignpourclient { get; set; }
    public string refacvignclient { get; set; }
    public string taxessieu { get; set; }
    public string ctaxessieu { get; set; }
    public string nbpneus { get; set; }
    public string dimpneus { get; set; }
    public string codefournientpneus { get; set; }
    public string codetarifentpneus { get; set; }
    public string basetaxepro { get; set; }
    public string pxachateuro { get; set; }
    public string prixcatalogueeuro { get; set; }
    public string basetaxeproeuro { get; set; }
    public string pxachatpays { get; set; }
    public string prixcataloguepays { get; set; }
    public string basetaxepropays { get; set; }
    public string deviserefeuro { get; set; }
    public string codesocietecmdprc { get; set; }
    public string numcmdprc { get; set; }
    public string datelivprevue { get; set; }
    public string z30alp1 { get; set; }
    public string z30alp2 { get; set; }
    public string z30alp3 { get; set; }
    public string z30alp4 { get; set; }
    public string z30alp5 { get; set; }
    public string z30dat1 { get; set; }
    public string z30dat2 { get; set; }
    public string z30dat3 { get; set; }
    public string z30mont1 { get; set; }
    public string z30mont1euro { get; set; }
    public string z30mont1pays { get; set; }
    public string z30mont2 { get; set; }
    public string z30mont2euro { get; set; }
    public string z30mont2pays { get; set; }
    public string z30mont3 { get; set; }
    public string z30mont3euro { get; set; }
    public string z30mont3pays { get; set; }
    public string z30num1 { get; set; }
    public string z30num2 { get; set; }
    public string z30num3 { get; set; }
    public string zone30cle1 { get; set; }
    public string zone30cle2 { get; set; }
    public string activite { get; set; }
    public string ptc { get; set; }
    public string ptr { get; set; }
    public string longutile { get; set; }
    public string longhorstt { get; set; }
    public string largutile { get; set; }
    public string larghorstt { get; set; }
    public string haututile { get; set; }
    public string hauthorstt { get; set; }
    public string surfutile { get; set; }
    public string surfhorstt { get; set; }
    public string voluutile { get; set; }
    public string voluhorstt { get; set; }
    public string acquisprov { get; set; }
    public string datemiseserv { get; set; }
    public string poidsvide { get; set; }
    public string contratpneus { get; set; }
    public string clecontratpneus { get; set; }
    public string capacitetot { get; set; }
    public string numfacvtefinan { get; set; }
    public string datefacvtefinan { get; set; }
    public string numfacvtepneus { get; set; }
    public string datefacvtepneus { get; set; }
    public string grpsoc { get; set; }
    public string codemarqueprotocole { get; set; }
    public string anneeprotocole { get; set; }
    public string numprotocole { get; set; }
    public string numversionprotocole { get; set; }
    public string codeprotamt { get; set; }
    public string dureeProt { get; set; }
    public string flotte { get; set; }
    public string codeadrfouprepa { get; set; }
    public string codeagenceliv { get; set; }
    public string numordreliv { get; set; }
    public string numfactvtestock { get; set; }
    public string datefacvtestock { get; set; }
    public string codeadrfouentpneus { get; set; }
    public string vptransvu { get; set; }
    public string typeparking { get; set; }
    public string codeagczoneparking { get; set; }
    public string codeparking { get; set; }
    public string numplaceparking { get; set; }
    public string emissionco2 { get; set; }
    public string z40alp1 { get; set; }
    public string z40alp2 { get; set; }
    public string z40alp3 { get; set; }
    public string z40alp4 { get; set; }
    public string z40alp5 { get; set; }
    public string z40num1 { get; set; }
    public string z40num2 { get; set; }
    public string z40num3 { get; set; }
    public string relevecpt2deb { get; set; }
    public string relevecpt2fin { get; set; }
    public string datederrelsaisi { get; set; }
    public string derrelsaisi { get; set; }
    public string derrel2saisi { get; set; }
    public string derrel2traite { get; set; }
    public string datederreltraitepk { get; set; }
    public string derreltraitepk { get; set; }
    public string derrel2traitepk { get; set; }
    public string nummvt { get; set; }
    public string etat { get; set; }
    public string typee { get; set; }
    public string refmvt { get; set; }
    public string catmodparc { get; set; }
    public string numldct { get; set; }
    public string numldanx { get; set; }
    public string numcdct { get; set; }
    public string numcdanx { get; set; }
    public string numar { get; set; }
    public string ordreparc { get; set; }
    public string typecmdent { get; set; }
    public string numcmdent { get; set; }
    public string etatintent { get; set; }
    public DateTime? datedeb2 { get; set; }
    public string heuredeb { get; set; }
    public string dateheuredeb { get; set; }
    public string codeagencedep { get; set; }
    public string lieudep { get; set; }
    public string relevecptdeb { get; set; }
    public string motifdeb { get; set; }
    public string niveaudeb { get; set; }
    public DateTime? datefin2 { get; set; }
    public string heurefin { get; set; }
    public string codeagenceret { get; set; }
    public string lieuret { get; set; }
    public string relevecptfin { get; set; }
    public string motiffin { get; set; }
    public string niveaufin { get; set; }
    public string derreltraite { get; set; }
    public string datederreltraite { get; set; }
    public string nbjourprev { get; set; }
    public string nbheureprev { get; set; }
    public string deplac { get; set; }
    public string datetransfert { get; set; }
    public string agentouv { get; set; }
    public string agentfer { get; set; }
    public string saisiedifferee { get; set; }
    public string numsinis { get; set; }
    public string nomclient { get; set; }
    public string kmdeduit { get; set; }
    public string cumdedtraites { get; set; }
    public string codesocieteclient { get; set; }
    public string codeatelier { get; set; }
    public string derrelprec { get; set; }
    public string datederrelprec { get; set; }
    public string codeadressecli { get; set; }
    public string datedertransfert { get; set; }
    public string datedertransfertrel { get; set; }
    public string derreltransfert { get; set; }
    public string typetransfert { get; set; }
    public string extractDW { get; set; }



}

public class CS_LDANX_ITEM
{
    [Key]
    public long UniqueID { get; set; }
    public string codesociete { get; set; }
    public string numldct { get; set; }
    public string codeclient { get; set; }
    public string numldanx { get; set; }
    public string codeparc { get; set; }
    public string parcattente { get; set; }
    public string codegenre { get; set; }
    public string codeldcfac { get; set; }
    public DateTime? datedeb2 { get; set; }
    public string datefin { get; set; }
    public string duree { get; set; }
    public string mutation { get; set; }
    public string codeadresse { get; set; }
    public string codelieu { get; set; }
    public string codeagence { get; set; }
    public string commercial { get; set; }
    public string codefourniinter { get; set; }
    public string codeconduct { get; set; }
    public string numdevis { get; set; }
    public string depotgarantie { get; set; }
    public string rachatcli { get; set; }
    public string relais { get; set; }
    public string typerelais { get; set; }
    public string nbjrelais { get; set; }
    public string datelivraison { get; set; }
    public string relcptdeb { get; set; }
    public string daterelcptdeb { get; set; }
    public string datearretfac { get; set; }
    public DateTime? daterestitution2 { get; set; }
    public string nummvt { get; set; }
    public string typefaccpt { get; set; }
    public string pervalcpt { get; set; }
    public string moisreffaccpt { get; set; }
    public string mois1faccpt { get; set; }
    public string tranchecpt1 { get; set; }
    public string tranchecpt2 { get; set; }
    public string codegroupecompens { get; set; }
    public string codeetat { get; set; }
    public string datefinderperfac { get; set; }
    public string derreltraite { get; set; }
    public string datederreltraite { get; set; }
    public string derreltraiteestime { get; set; }
    public string derrelmini { get; set; }
    public string datederrelmini { get; set; }
    public string derrelpk { get; set; }
    public string datederrelpk { get; set; }
    public string grpanx { get; set; }
    public string tri { get; set; }
    public string classement { get; set; }
    public string cumulcptfacauto { get; set; }
    public string cumulcptfacman { get; set; }
    public string cumulrelaismini { get; set; }
    public string cumulcptrelais { get; set; }
    public string numfacbase { get; set; }
    public string numfacregul { get; set; }
    public string numfacexcep { get; set; }
    public string numfaccompens { get; set; }
    public string moisencours { get; set; }
    public string bloquee { get; set; }
    public string datefinblocage { get; set; }
    public string deblocauto { get; set; }
    public string datecrea { get; set; }
    public string datemodi { get; set; }
    public string codeutiliscrea { get; set; }
    public string codeutilismodi { get; set; }
    public string zalp1 { get; set; }
    public string zalp2 { get; set; }
    public string zalp3 { get; set; }
    public string zdat1 { get; set; }
    public string zdat2 { get; set; }
    public string zdat3 { get; set; }
    public string znum1 { get; set; }
    public string znum2 { get; set; }
    public string znum3 { get; set; }
    public string zonecle { get; set; }
    public string indexee { get; set; }
    public string clifou { get; set; }
    public string daterelestime { get; set; }
    public string relestime { get; set; }
    public string dureeorigine { get; set; }
    public string codeadresseexploit { get; set; }
    public string depotgarantieeuro { get; set; }
    public string depotgarantiepays { get; set; }
    public string deviserefeuro { get; set; }
    public string devisesignature { get; set; }
    public string z30alp1 { get; set; }
    public string z30alp2 { get; set; }
    public string z30alp3 { get; set; }
    public string z30alp4 { get; set; }
    public string z30alp5 { get; set; }
    public string z30dat1 { get; set; }
    public string z30dat2 { get; set; }
    public string z30dat3 { get; set; }
    public string z30mont1 { get; set; }
    public string z30mont1euro { get; set; }
    public string z30mont1pays { get; set; }
    public string z30mont2 { get; set; }
    public string z30mont2euro { get; set; }
    public string z30mont2pays { get; set; }
    public string z30mont3 { get; set; }
    public string z30mont3euro { get; set; }
    public string z30mont3pays { get; set; }
    public string z30num1 { get; set; }
    public string z30num2 { get; set; }
    public string z30num3 { get; set; }
    public string zone30cle1 { get; set; }
    public string zone30cle2 { get; set; }
    public string codeldtype { get; set; }
    public string typeldanx { get; set; }
    public string codemodele { get; set; }
    public string grpmodele { get; set; }
    public string parcencmd { get; set; }
    public string exclusif { get; set; }
    public string permanent { get; set; }
    public string calensem { get; set; }
    public string tranchecpt3 { get; set; }
    public string chauffeur { get; set; }
    public string kmcontrat { get; set; }
    public string supplementaire { get; set; }
    public string recondauto { get; set; }
    public string dureerecond { get; set; }
    public string cautiondepot { get; set; }
    public string dateechcaution { get; set; }
    public string refcaution { get; set; }
    public string recondcaution { get; set; }
    public string codeagenceaffec { get; set; }
    public string assurecli { get; set; }
    public string achatvign { get; set; }
    public string nbserv { get; set; }
    public string carburant { get; set; }
    public string reference { get; set; }
    public string codeparcprevu { get; set; }
    public string preavis { get; set; }
    public string codesocietefacbase { get; set; }
    public string codesocietefacregul { get; set; }
    public string codesocietefaccompens { get; set; }
    public string codesocietefacexcep { get; set; }
    public string moisfacbase { get; set; }
    public string moisfacregul { get; set; }
    public string moisfacexcep { get; set; }
    public string moisfaccompens { get; set; }
    public string cumuldeduittraite { get; set; }
    public string transfexploit { get; set; }
    public string typetiers { get; set; }
    public string nature { get; set; }
    public string derrelsaisi { get; set; }
    public string datederrelsaisi { get; set; }
    public string heuredeb { get; set; }
    public string heurefin { get; set; }
    public string codesocietefackmfdm { get; set; }
    public string numfackmfdm { get; set; }
    public string moisfackmfdm { get; set; }
    public string datederrelsaisianx { get; set; }
    public string datederreltraiteanx { get; set; }
    public string derrelprec { get; set; }
    public string datederrelprec { get; set; }
    public string dureeanxorig { get; set; }
    public string codeclientgrpcompens { get; set; }
    public string assurld { get; set; }
    public string assurldanx { get; set; }
    public string datedebassclisoc { get; set; }
    public string dateechassur { get; set; }
    public string datederchgassurld { get; set; }
    public string montfranchise { get; set; }
    public string montfranchiseeuro { get; set; }
    public string montfranchisepays { get; set; }
    public string nbfranchgratuite { get; set; }
    public string trtautorefacfranchise { get; set; }
    public string reportunitforf { get; set; }
    public string grpsoc { get; set; }
    public string derreltraiteestim { get; set; }
    public string refanxorig { get; set; }
    public string refassur { get; set; }
    public string numldctv { get; set; }
    public string extractDW { get; set; }
    public string z40alp1 { get; set; }
    public string z40alp2 { get; set; }
    public string z40alp3 { get; set; }
    public string z40alp4 { get; set; }
    public string z40alp5 { get; set; }
}

public class CS_PARC_ITEM
{
    [Key]
    public long UniqueID { get; set; }

    public string codeparc { get; set; }
    public string immat { get; set; }
    public string dateimmat { get; set; }
    public string codemodele { get; set; }
    public string libparc { get; set; }
    public string cattarif { get; set; }
    public string codemarque { get; set; }
    public string typeparc { get; set; }
    public string puisscv { get; set; }
    public string numserie { get; set; }
    public string cleserie { get; set; }
    public string couleur { get; set; }
    public string date1mes { get; set; }
    public string anneemodele { get; set; }
    public string dateentree { get; set; }
    public string datesortie { get; set; }
    public string intext { get; set; }
    public string codesociete { get; set; }
    public string codeparcfranchise { get; set; }
    public string achatloc { get; set; }
    public string revente { get; set; }
    public string daterevente { get; set; }
    public string gage { get; set; }
    public string finance { get; set; }
    public string neufoccas { get; set; }
    public string cptentree { get; set; }
    public string totcptentree { get; set; }
    public string numfactach { get; set; }
    public string codefourni { get; set; }
    public string dateachat { get; set; }
    public string pxachat { get; set; }
    public string prixcatalogue { get; set; }
    public string datefingar { get; set; }
    public string remplacement { get; set; }
    public string codeparcrempl { get; set; }
    public string sousparc { get; set; }
    public string codeagenceaffec { get; set; }
    public string situadmin { get; set; }
    public string situation { get; set; }
    public string nummvtder { get; set; }
    public string codeagencedispo { get; set; }
    public string codeclient { get; set; }
    public string codeconduct { get; set; }
    public string cpt { get; set; }
    public string datecpt { get; set; }
    public string numreleve { get; set; }
    public string reldebfin { get; set; }
    public string prepare { get; set; }
    public string sscattarif { get; set; }
    public string equipaediter { get; set; }
    public string cveditee { get; set; }
    public string cgemetteur { get; set; }
    public string cgdate { get; set; }
    public string cgnomproprie { get; set; }
    public string cgnomusage { get; set; }
    public string cgdomicile { get; set; }
    public string cgville { get; set; }
    public string cggenre { get; set; }
    public string cgtype { get; set; }
    public string cgcarross { get; set; }
    public string placeassise { get; set; }
    public string placedebout { get; set; }
    public string cglargeur { get; set; }
    public string cgsurface { get; set; }
    public string cgptc { get; set; }
    public string cgpv { get; set; }
    public string cgptr { get; set; }
    public string cgbruit { get; set; }
    public string cgregime { get; set; }
    public string cgdateprec { get; set; }
    public string cgnumprec { get; set; }
    public string cgnumimprime { get; set; }
    public string cgduplicata { get; set; }
    public string cgdatedupli { get; set; }
    public string cgtitsignat { get; set; }
    public string cgsignat { get; set; }
    public string cgtexte { get; set; }
    public string dateblocage { get; set; }
    public string transfere { get; set; }
    public string datecrea { get; set; }
    public string datemodi { get; set; }
    public string codeutiliscrea { get; set; }
    public string codeutilismodi { get; set; }
    public string zalp1 { get; set; }
    public string zalp2 { get; set; }
    public string zalp3 { get; set; }
    public string zdat1 { get; set; }
    public string zdat2 { get; set; }
    public string zdat3 { get; set; }
    public string znum1 { get; set; }
    public string znum2 { get; set; }
    public string znum3 { get; set; }
    public string zonecle { get; set; }
    public string immatprov { get; set; }
    public string codemodelecarrosserie { get; set; }
    public string codefournient { get; set; }
    public string policeassu { get; set; }
    public string dureeprev { get; set; }
    public string cptprev { get; set; }
    public string motifsorti { get; set; }
    public string vignette { get; set; }
    public string vignettegratuite { get; set; }
    public string classevign { get; set; }
    public string achatvignpourclient { get; set; }
    public string refacvignclient { get; set; }
    public string taxessieu { get; set; }
    public string ctaxessieu { get; set; }
    public string nbpneus { get; set; }
    public string dimpneus { get; set; }
    public string codefournientpneus { get; set; }
    public string codetarifentpneus { get; set; }
    public string basetaxepro { get; set; }
    public string pxachateuro { get; set; }
    public string prixcatalogueeuro { get; set; }
    public string basetaxeproeuro { get; set; }
    public string pxachatpays { get; set; }
    public string prixcataloguepays { get; set; }
    public string basetaxepropays { get; set; }
    public string deviserefeuro { get; set; }
    public string codesocietecmdprc { get; set; }
    public string numcmdprc { get; set; }
    public string datelivprevue { get; set; }
    public string z30alp1 { get; set; }
    public string z30alp2 { get; set; }
    public string z30alp3 { get; set; }
    public string z30alp4 { get; set; }
    public string z30alp5 { get; set; }
    public string z30dat1 { get; set; }
    public string z30dat2 { get; set; }
    public string z30dat3 { get; set; }
    public string z30mont1 { get; set; }
    public string z30mont1euro { get; set; }
    public string z30mont1pays { get; set; }
    public string z30mont2 { get; set; }
    public string z30mont2euro { get; set; }
    public string z30mont2pays { get; set; }
    public string z30mont3 { get; set; }
    public string z30mont3euro { get; set; }
    public string z30mont3pays { get; set; }
    public string z30num1 { get; set; }
    public string z30num2 { get; set; }
    public string z30num3 { get; set; }
    public string zone30cle1 { get; set; }
    public string zone30cle2 { get; set; }
    public string activite { get; set; }
    public string ptc { get; set; }
    public string ptr { get; set; }
    public string longutile { get; set; }
    public string longhorstt { get; set; }
    public string largutile { get; set; }
    public string larghorstt { get; set; }
    public string haututile { get; set; }
    public string hauthorstt { get; set; }
    public string surfutile { get; set; }
    public string surfhorstt { get; set; }
    public string voluutile { get; set; }
    public string voluhorstt { get; set; }
    public string acquisprov { get; set; }
    public string datemiseserv { get; set; }
    public string poidsvide { get; set; }
    public string contratpneus { get; set; }
    public string clecontratpneus { get; set; }
    public string capacitetot { get; set; }
    public string numfacvtefinan { get; set; }
    public string datefacvtefinan { get; set; }
    public string numfacvtepneus { get; set; }
    public string datefacvtepneus { get; set; }
    public string grpsoc { get; set; }
    public string codemarqueprotocole { get; set; }
    public string anneeprotocole { get; set; }
    public string numprotocole { get; set; }
    public string numversionprotocole { get; set; }
    public string codeprotamt { get; set; }
    public string dureeProt { get; set; }
    public string flotte { get; set; }
    public string codeadrfouprepa { get; set; }
    public string codeagenceliv { get; set; }
    public string numordreliv { get; set; }
    public string numfactvtestock { get; set; }
    public string datefacvtestock { get; set; }
    public string codeadrfouentpneus { get; set; }
    public string vptransvu { get; set; }
    public string typeparking { get; set; }
    public string codeagczoneparking { get; set; }
    public string codeparking { get; set; }
    public string numplaceparking { get; set; }
    public string emissionco2 { get; set; }
    public string z40alp1 { get; set; }
    public string z40alp2 { get; set; }
    public string z40alp3 { get; set; }
    public string z40alp4 { get; set; }
    public string z40alp5 { get; set; }
    public string z40num1 { get; set; }
    public string z40num2 { get; set; }
    public string z40num3 { get; set; }
}

public class CS_FAC_AI_JOINED_TEMP_ITEM
{
    [Key]
    public long UniqueID { get; set; }

    public string codesociete { get; set; }
    public string typefac { get; set; }
    public string numfac { get; set; }
    public string facavoir { get; set; }
    public string facturation { get; set; }
    public string libelle { get; set; }
    public string montant { get; set; }
    public DateTime? datefac { get; set; }  // Changed to DateTime - stored procedure returns TRY_CONVERT(datetime, b.datefac, 103)
    public string codeparc { get; set; }
    public string integre { get; set; }
    public string etat { get; set; }
    public string codeprest { get; set; }
    public string codetiers { get; set; }
    public string tottvaprestht { get; set; }
    public string typetva { get; set; }
    public string codeutiliscrea { get; set; }
    public string codeutilismodi { get; set; }
    public DateTime? datecrea { get; set; }  // Changed to DateTime - stored procedure returns TRY_CONVERT(datetime, a.datecrea, 103)

}

public class CS_FAC_AI_JOINED_TEMP2_ITEM
{
    [Key]

    public string typefac { get; set; }
    public string numfac { get; set;}
    public string facavoir { get; set; }
    public DateTime? datefac { get; set; }  // Changed from datefac2 to datefac to match stored procedure
    public string codetiers { get; set; }
    public string facturation { get; set; }
    public string montant { get; set; }
    public string libelle { get; set; }
    public string codeparc { get; set; }
    public string codeprest { get; set; }
    public string integre { get; set; }
    public string etat { get; set; }
    public string numldct { get; set; }

}
