.page[b-anwey2l9dk] {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main-content[b-anwey2l9dk] {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.content[b-anwey2l9dk] {
    flex: 1;
    padding: 1.5rem;
    background-color: #f8f9fa;
    min-height: calc(100vh - 80px);
}

#blazor-error-ui[b-anwey2l9dk] {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss[b-anwey2l9dk] {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
