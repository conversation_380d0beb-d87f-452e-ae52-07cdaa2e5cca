﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace CarSystemsWebApp.Models;

public partial class CarSystemsContext : DbContext
{
    public CarSystemsContext()
    {
    }

    public CarSystemsContext(DbContextOptions<CarSystemsContext> options)
        : base(options)
    {
    }

    public virtual DbSet<CS_ECR> CS_ECRs { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("data source=vmi401426.contaboserver.net;initial catalog=CARSYSTEMS;persist security info=True;user id=sa;password=**********;TrustServerCertificate=True");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.UseCollation("French_CI_AS");

        modelBuilder.Entity<CS_ECR>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("CS_ECR");

            entity.Property(e => e.annul)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.central)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_agence)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_etablissement)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_jrn)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_parc)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_prest)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_reg)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_societe)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_tva)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_utilis_crea)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.code_utilis_modi)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.contrepartie)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.credit)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.crediteuro)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.creditpays)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.datecompta)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.datecompta2).HasColumnType("datetime");
            entity.Property(e => e.datecrea)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.dateech)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.dateheuretransfer)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.datelettrage)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.datemodi)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.datepiece)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.datevaleur)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.debit)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.debiteuro)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.debitpays)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.deviserefeuro)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.etat)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.extractDW)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.let)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.letpartiel)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.letsuivant)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.libelle)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.litige)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.moistva)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.moyenpaie)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.num_compte_aux)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.num_compte_gen)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.num_ecr)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.num_ex)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.num_reg)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.numfacfisc)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.numlig)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.numlig_let)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.numlig_tva)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.numpiece)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.pointe)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.reflettrage)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.saisie)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.transfer)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.typassutva)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.type)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.typpiece)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.z40alp1)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.z40alp2)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.z40alp3)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.z40alp4)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.z40alp5)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.zalp1)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.zalp2)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.zalp3)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.zdat1)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.zdat2)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.zdat3)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.znum1)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.znum2)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.znum3)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.zonecle)
                .HasMaxLength(255)
                .IsUnicode(false);
        });

        OnModelCreatingPartial(modelBuilder);

        OnModelCreatingEx(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
