@page "/PageFactureAiAnnuel"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using CarSystemsWebApp.Services
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection
@using Syncfusion.Drawing

@inject IJSRuntime _jsRuntime
@inject SfDialogService _dialogService
@inject PageTitleService PageTitleService







<!-- ============== -->
<!-- COMPACT SINGLE ROW FILTERS -->
<!-- ============== -->

<div class="professional-filter-container">
    <div class="filter-header">
        <div class="filter-title">
            Filtres
        </div>
        <button class="clear-all-btn" @onclick="ClearFilters">
            <i class="fas fa-eraser"></i> Effacer tout
        </button>
    </div>

    <div class="row filter-row align-items-center">
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedClient" ShowClearButton="true" Placeholder="Rechercher un client..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedEtat" ShowClearButton="true" Placeholder="Filtrer par état..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedTypeFacture" Placeholder="Sélectionner le type..." ShowClearButton="true" CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date de début..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateFactureStart"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date de fin..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateFactureEnd"
                         Width="240px"></SfDatePicker>
        </div>
      
    </div>

    <div class="row filter-row align-items-center" style="margin-top: 12px;">
        
        <div class="col-auto">
            <SfButton Type="button" @onclick="Helper_Refresh" CssClass="btn-inline">
                Appliquer
            </SfButton>
        </div>
        <div class="col-auto">
            <SfButton @onclick="OnButtonExport" CssClass="btn-inline-export">
                Export
            </SfButton>
        </div>
    </div>
</div>

<!-- ======= -->
<!-- MODERN GRID RESULTS -->
<!-- ======= -->

<div class="grid-container">
    <SfGrid ID="FacturesAIAnnuelGrid"
            TValue="CS_FAC_AI_JOINED_TEMP_ITEM"
            @ref="@MyGrid"
            Query="@MyQuery"
            AllowPaging="true"
            AllowFiltering="false"
            AllowSorting="true"
            AllowResizing="true"
            EnablePersistence="false"
            ShowColumnChooser="true"
            EnableInfiniteScrolling="false"
            Height="calc(100vh - 180px)"
            Toolbar="@(new List<string>() { "ColumnChooser" })">
        <GridEvents TValue="CS_FAC_AI_JOINED_TEMP_ITEM"
                    RowSelected="OnRowSelected"
                    OnDataBound="DataBoundHandler"
                    OnActionBegin="OnActionBegin"
                    OnActionComplete="OnActionComplete"/>
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="22" EnableQueryString="false"/>
        <GridColumns>
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.numfac) HeaderText="N° Facture" Width="90" IsPrimaryKey="true" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.codesociete) HeaderText="Code Société" Width="80" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.typefac) HeaderText="Type" Width="60" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.codetiers) HeaderText="Client" Width="70" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.libelle) HeaderText="Libellé" Width="150" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.montant) HeaderText="Montant" Width="80" TextAlign="TextAlign.Right" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.etat) HeaderText="État" Width="50" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.datefac) HeaderText="Date Fact." Width="80" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.datecrea) HeaderText="Créé le" Width="80" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.facturation) HeaderText="Facturation" Width="70" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.integre) HeaderText="Intégré" Width="60" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.facavoir) HeaderText="Fac/Avoir" Width="70" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.tottvaprestht) HeaderText="TVA HT" Width="80" TextAlign="TextAlign.Right" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.typetva) HeaderText="Type TVA" Width="70" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.codeparc) HeaderText="Code Parc" Width="80" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.codeprest) HeaderText="Code Prestation" Width="100" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.codeutiliscrea) HeaderText="Créé par" Width="80" />
            <GridColumn Field=@nameof(CS_FAC_AI_JOINED_TEMP_ITEM.codeutilismodi) HeaderText="Modifié par" Width="80" />
        </GridColumns>
    </SfGrid>
</div>

<script type="text/javascript">

    function saveAsFile(filename, bytesBase64) {
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++) {
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_FAC_AI_JOINED_TEMP_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;

    private string      SelectedClient;
    private DateTime?   SelectedDateFactureStart;
    private DateTime?   SelectedDateFactureEnd;
    private string      SelectedEtat;
    private string      SelectedTypeFacture;
   


    protected override void OnInitialized()
    {
        // Set page title
        PageTitleService.SetTitle("Factures AI Annuel");

        this.MyQuery = new Query()
            .AddParams("Client", null)
            .AddParams("DateMinFacture", null)
            .AddParams("DateMaxFacture", null)
            .AddParams("Etat", null)
            .AddParams("TypeFacture", null);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_FAC_AI_JOINED_TEMP_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_FAC_AI_JOINED_TEMP_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_FAC_AI_JOINED_TEMP_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_FAC_AI_JOINED_TEMP_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["Client"] = this.SelectedClient;
        MyQuery.Queries.Params["DateMinFacture"] = this.SelectedDateFactureStart;
        MyQuery.Queries.Params["DateMaxFacture"] = this.SelectedDateFactureEnd;
        MyQuery.Queries.Params["Etat"] = this.SelectedEtat;
        MyQuery.Queries.Params["TypeFacture"] = this.SelectedTypeFacture;

        await MyGrid.Refresh();
    }

    private async Task ClearFilters()
    {
        // Clear all filter values
        SelectedClient = null;
        SelectedDateFactureStart = null;
        SelectedDateFactureEnd = null;
        SelectedEtat = null;
        SelectedTypeFacture = null;

        // Update query parameters
        MyQuery.Queries.Params["Client"] = null;
        MyQuery.Queries.Params["DateMinFacture"] = null;
        MyQuery.Queries.Params["DateMaxFacture"] = null;
        MyQuery.Queries.Params["Etat"] = null;
        MyQuery.Queries.Params["TypeFacture"] = null;

        // Refresh the grid
        await MyGrid.Refresh();

        // Force UI update
        StateHasChanged();
    }

    private async void OnButtonExport()
    {
        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // Create a workbook with 1 worksheet
            IWorkbook workbook = application.Workbooks.Create(1);
            IWorksheet worksheet = workbook.Worksheets[0];
            worksheet.Name = "FACTURES_AI";

            // Get data for export (large limit to get all data)
            string sortcolumn = "numfacture";
            string sortdir = "ascending";

            var items = MyCustomAdaptor.Helper_GetData(
                this.SelectedDateFactureStart,
                this.SelectedDateFactureEnd,
                this.SelectedClient,
                this.SelectedEtat,
                this.SelectedTypeFacture,
                0, 100000,
                sortcolumn, sortdir, 900); // 15 minute timeout for export

            // Create a professional header design with WafaLLD orange branding
            // Main title with orange background
            worksheet["A1"].Text = "EXPORT FACTURES AI ANNUEL";
            worksheet["A1"].CellStyle.Font.Bold = true;
            worksheet["A1"].CellStyle.Font.Size = 16;
            worksheet["A1"].CellStyle.Font.Color = ExcelKnownColors.White;
            worksheet["A1"].CellStyle.Color = Color.FromArgb(255, 140, 0); // WafaLLD Orange background
            worksheet["A1"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            worksheet["A1"].CellStyle.VerticalAlignment = ExcelVAlign.VAlignCenter;
            worksheet.Range["A1:R1"].Merge();
            worksheet.Range["A1:R1"].RowHeight = 25;

            // Export info with light orange background
            worksheet["A2"].Text = $"Exporté le: {DateTime.Now:dd/MM/yyyy HH:mm}";
            worksheet["A2"].CellStyle.Font.Size = 10;
            worksheet["A2"].CellStyle.Font.Italic = true;
            worksheet["A2"].CellStyle.Color = Color.FromArgb(255, 228, 196); // Light orange background
            worksheet["A2"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A2:R2"].Merge();

            // Add filter criteria info
            string filterInfo = "Filtres appliqués: ";
            if (SelectedDateFactureStart.HasValue || SelectedDateFactureEnd.HasValue)
            {
                filterInfo += $"Période: {SelectedDateFactureStart?.ToString("dd/MM/yyyy") ?? "..."} - {SelectedDateFactureEnd?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (!string.IsNullOrEmpty(SelectedClient))
            {
                filterInfo += $"Client: {SelectedClient} | ";
            }
            if (!string.IsNullOrEmpty(SelectedEtat))
            {
                filterInfo += $"État: {SelectedEtat} | ";
            }
            if (!string.IsNullOrEmpty(SelectedTypeFacture))
            {
                filterInfo += $"Type: {SelectedTypeFacture} | ";
            }
            if (filterInfo.EndsWith(" | "))
            {
                filterInfo = filterInfo.Substring(0, filterInfo.Length - 3);
            }
            else if (filterInfo == "Filtres appliqués: ")
            {
                filterInfo = "Aucun filtre appliqué";
            }

            worksheet["A3"].Text = filterInfo;
            worksheet["A3"].CellStyle.Font.Size = 9;
            worksheet["A3"].CellStyle.Color = Color.FromArgb(255, 248, 240); // Very light orange/cream
            worksheet["A3"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A3:R3"].Merge();

            // Add a separator line with orange accent
            worksheet.Range["A4:R4"].CellStyle.Color = Color.FromArgb(255, 165, 0); // Orange accent line
            worksheet.Range["A4:R4"].RowHeight = 3;

            // Convert to DataTable for Excel export
            DataTable table = new DataTable();
            Type type = typeof(CS_FAC_AI_JOINED_TEMP_ITEM);
            PropertyInfo[] properties = type.GetProperties();

            // Create columns
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);

            // Add data rows
            foreach (var item in items)
            {
                DataRow row = table.NewRow();
                int k = 0;
                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }
                table.Rows.Add(row);
            }

            // Export data to Excel worksheet starting from row 6 to leave space for header design
            worksheet.ImportDataTable(table, true, 6, 1);

            // Style the data header row (column names) with orange theme
            IRange headerRange = worksheet.Range[6, 1, 6, properties.Length];
            headerRange.CellStyle.Font.Bold = true;
            headerRange.CellStyle.Font.Color = ExcelKnownColors.White;
            headerRange.CellStyle.Color = Color.FromArgb(255, 165, 0); // Medium orange background
            headerRange.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
            headerRange.RowHeight = 20;

            // Add alternating row colors and borders to all data
            if (items.Count > 0)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    int rowIndex = 7 + i; // Start from row 7 (after header)
                    IRange rowRange = worksheet.Range[rowIndex, 1, rowIndex, properties.Length];

                    // Alternating row colors with orange theme
                    if (i % 2 == 0)
                    {
                        rowRange.CellStyle.Color = Color.FromArgb(255, 250, 240); // Very light orange/cream
                    }
                    else
                    {
                        rowRange.CellStyle.Color = Color.White;
                    }

                    // Add borders
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
                }
            }

            worksheet.UsedRange.AutofitColumns();

            // Save and download
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                string filename = "ExportFacturesAI.xlsx";
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "FACTURES-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                List<CS_FAC_AI_JOINED_TEMP_ITEM> empty;
                empty = Enumerable.Empty<CS_FAC_AI_JOINED_TEMP_ITEM>().ToList();

                if (dm.Params is null || dm.Params.Count == 0)
                {
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Nothing to do", true);
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                    return dm.RequiresCounts ? new DataResult() { Result = empty, Count = 0 } : (object)empty;
                }

                var dateMinFacture      = dm.Params["DateMinFacture"] as DateTime?;
                var dateMaxFacture      = dm.Params["DateMaxFacture"] as DateTime?;
                var client              = dm.Params["Client"] as string;
                var etat                = dm.Params["Etat"] as string;
                var typeFacture         = dm.Params["TypeFacture"] as string;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMinFacture = {dateMinFacture}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMaxFacture = {dateMaxFacture}", true);

                // Dynamic sorting from DataManagerRequest
                string sortcolumn   = "numfac";  // Default sort column
                string sortdir      = "ascending";  // Default sort direction

                // Extract sorting information from DataManagerRequest
                if (dm.Sorted != null && dm.Sorted.Count > 0)
                {
                    var sortInfo = dm.Sorted[0]; // Get the first sort column
                    sortcolumn = sortInfo.Name;
                    sortdir = sortInfo.Direction;
                }

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortcolumn = {sortcolumn}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortdir = {sortdir}", true);

                CarSystemsContext model;
                model = new CarSystemsContext();

                // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
                object dateMinParam = dateMinFacture.HasValue ? (object)dateMinFacture.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateMaxParam = dateMaxFacture.HasValue ? (object)dateMaxFacture.Value.ToString("yyyy-MM-dd") : DBNull.Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dm.Skip = {dm.Skip}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dm.Take = {dm.Take}", true);

                var items = Helper_GetData(dateMinFacture, dateMaxFacture, client, etat, typeFacture, dm.Skip, dm.Take, sortcolumn, sortdir);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetPagesAiFacturesItemsCount_V2 {0}, {1}, {2}, {3}, {4}",
                        client,
                        dateMinParam,
                        dateMaxParam,
                        etat,
                        typeFacture)
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);

                return null;
            }
        }

        public static List<CS_FAC_AI_JOINED_TEMP_ITEM> Helper_GetData(DateTime? dateMinFacture, DateTime? dateMaxFacture, string client, string etat, string typeFacture, int skip, int take, string sortcolumn, string sortdir, int timeoutSeconds = 30)
        {
            var logKey = "FACTURES-HELPER_GETDATA";
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMinFacture = {dateMinFacture}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMaxFacture = {dateMaxFacture}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"skip = {skip}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"take = {take}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortcolumn = {sortcolumn}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortdir = {sortdir}", true);

            CarSystemsContext model;
            model = new CarSystemsContext();

            // Set command timeout for long-running operations
            model.Database.SetCommandTimeout(timeoutSeconds);

            // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
            object dateMinParam = dateMinFacture.HasValue ? (object)dateMinFacture.Value.ToString("yyyy-MM-dd") : DBNull.Value;
            object dateMaxParam = dateMaxFacture.HasValue ? (object)dateMaxFacture.Value.ToString("yyyy-MM-dd") : DBNull.Value;

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateMinParam = {dateMinParam}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateMaxParam = {dateMaxParam}", true);

            var items = model.CS_FAC_AI_JOINED_TEMP_ITEMS.FromSqlRaw("EXECUTE dbo.GetPagesAiFacturesItems_V2 {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}",
                client,
                dateMinParam,
                dateMaxParam,
                etat,
                typeFacture,
                skip,
                take,
                sortcolumn,
                sortdir).ToList();

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Retrieved {items.Count} items", true);
            return items;
        }

    }



}
