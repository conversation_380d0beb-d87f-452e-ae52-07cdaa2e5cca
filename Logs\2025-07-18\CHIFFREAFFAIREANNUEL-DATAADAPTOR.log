18/07/2025 : 08:35:42.273 : Entering
18/07/2025 : 08:35:42.275 :     DateFactureStart = 
18/07/2025 : 08:35:42.275 :     DateFactureEnd = 
18/07/2025 : 08:35:42.275 :     Client= 
18/07/2025 : 08:36:00.584 :     Count = 8832132
18/07/2025 : 08:36:00.584 : Leaving
18/07/2025 : 08:36:02.922 : Entering
18/07/2025 : 08:36:02.923 :     DateFactureStart = 
18/07/2025 : 08:36:02.923 :     DateFactureEnd = 
18/07/2025 : 08:36:02.924 :     Client= 
18/07/2025 : 08:36:14.717 :     Count = 8832132
18/07/2025 : 08:36:14.718 : Leaving
18/07/2025 : 08:37:32.071 : Entering
18/07/2025 : 08:37:32.072 :     DateFactureStart = 
18/07/2025 : 08:37:32.072 :     DateFactureEnd = 
18/07/2025 : 08:37:32.073 :     Client= 
18/07/2025 : 08:38:02.894 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
18/07/2025 : 08:38:02.929 :     Entering
18/07/2025 : 08:38:02.930 :         DateFactureStart = 
18/07/2025 : 08:38:02.931 :         DateFactureEnd = 
18/07/2025 : 08:38:02.932 :         Client= 
18/07/2025 : 08:38:33.540 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
18/07/2025 : 08:41:18.053 :         Entering
18/07/2025 : 08:41:18.054 :             DateFactureStart = 
18/07/2025 : 08:41:18.055 :             DateFactureEnd = 
18/07/2025 : 08:41:18.057 :             Client= 
18/07/2025 : 08:41:28.268 :             Count = 8832132
18/07/2025 : 08:41:40.202 :         Leaving
18/07/2025 : 08:41:40.247 :         Entering
18/07/2025 : 08:41:40.255 :             DateFactureStart = 
18/07/2025 : 08:41:40.262 :             DateFactureEnd = 
18/07/2025 : 08:41:40.262 :             Client= 
18/07/2025 : 08:42:06.669 :             Count = 8832132
18/07/2025 : 08:42:06.670 :         Leaving
18/07/2025 : 13:40:34.527 : Entering
18/07/2025 : 13:40:34.532 :     DateFactureStart = 
18/07/2025 : 13:40:34.532 :     DateFactureEnd = 
18/07/2025 : 13:40:34.533 :     Client= 
18/07/2025 : 13:40:53.766 :     Count = 8832132
18/07/2025 : 13:40:53.766 : Leaving
18/07/2025 : 13:40:54.013 : Entering
18/07/2025 : 13:40:54.014 :     DateFactureStart = 
18/07/2025 : 13:40:54.015 :     DateFactureEnd = 
18/07/2025 : 13:40:54.015 :     Client= 
18/07/2025 : 13:41:08.637 :     Count = 8832132
18/07/2025 : 13:41:08.640 : Leaving
18/07/2025 : 14:07:51.333 : Entering
18/07/2025 : 14:07:51.335 :     DateFactureStart = 
18/07/2025 : 14:07:51.335 :     DateFactureEnd = 
18/07/2025 : 14:07:51.335 :     Client= 
18/07/2025 : 14:07:51.336 :     NO SORTING: Using default sort
18/07/2025 : 14:08:07.499 :     Count = 8832132
18/07/2025 : 14:08:07.500 : Leaving
18/07/2025 : 14:08:10.515 : Entering
18/07/2025 : 14:08:10.516 :     DateFactureStart = 
18/07/2025 : 14:08:10.516 :     DateFactureEnd = 
18/07/2025 : 14:08:10.516 :     Client= 
18/07/2025 : 14:08:10.516 :     NO SORTING: Using default sort
18/07/2025 : 14:08:27.145 :     Count = 8832132
18/07/2025 : 14:08:27.156 : Leaving
18/07/2025 : 14:09:23.222 : Entering
18/07/2025 : 14:09:23.222 :     DateFactureStart = 
18/07/2025 : 14:09:23.223 :     DateFactureEnd = 
18/07/2025 : 14:09:23.223 :     Client= 
18/07/2025 : 14:09:23.223 :     NO SORTING: Using default sort
18/07/2025 : 14:09:37.519 :     Count = 8832132
18/07/2025 : 14:09:37.519 : Leaving
18/07/2025 : 14:09:37.596 : Entering
18/07/2025 : 14:09:37.597 :     DateFactureStart = 
18/07/2025 : 14:09:37.597 :     DateFactureEnd = 
18/07/2025 : 14:09:37.597 :     Client= 
18/07/2025 : 14:09:37.598 :     NO SORTING: Using default sort
18/07/2025 : 14:09:52.971 :     Count = 8832132
18/07/2025 : 14:09:52.972 : Leaving
18/07/2025 : 14:15:10.084 : Entering
18/07/2025 : 14:15:10.086 :     DateFactureStart = 
18/07/2025 : 14:15:10.087 :     DateFactureEnd = 
18/07/2025 : 14:15:10.087 :     Client= 
18/07/2025 : 14:15:10.087 :     NO SORTING: Using default sort
18/07/2025 : 14:15:35.201 :     Count = 8832132
18/07/2025 : 14:15:35.204 : Leaving
18/07/2025 : 14:15:35.615 : Entering
18/07/2025 : 14:15:35.616 :     DateFactureStart = 
18/07/2025 : 14:15:35.616 :     DateFactureEnd = 
18/07/2025 : 14:15:35.616 :     Client= 
18/07/2025 : 14:15:35.617 :     NO SORTING: Using default sort
18/07/2025 : 14:15:55.443 :     Count = 8832132
18/07/2025 : 14:15:55.444 : Leaving
