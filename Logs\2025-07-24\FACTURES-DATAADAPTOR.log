24/07/2025 : 00:01:43.766 : Entering
24/07/2025 : 00:01:43.770 :     dateMinFacture = 
24/07/2025 : 00:01:43.770 :     dateMaxFacture = 
24/07/2025 : 00:01:43.771 :     sortcolumn = numfac
24/07/2025 : 00:01:43.771 :     sortdir = ascending
24/07/2025 : 00:01:50.567 :     Count = 8832132
24/07/2025 : 00:01:50.568 : Leaving
24/07/2025 : 00:01:51.004 : Entering
24/07/2025 : 00:01:51.005 :     dateMinFacture = 
24/07/2025 : 00:01:51.005 :     dateMaxFacture = 
24/07/2025 : 00:01:51.005 :     sortcolumn = datefac
24/07/2025 : 00:01:51.006 :     sortdir = descending
24/07/2025 : 00:02:07.025 :     Count = 8832132
24/07/2025 : 00:02:07.025 : Leaving
24/07/2025 : 00:02:27.874 : Entering
24/07/2025 : 00:02:27.875 :     dateMinFacture = 06/06/2019 00:00:00
24/07/2025 : 00:02:27.876 :     dateMaxFacture = 06/02/2020 00:00:00
24/07/2025 : 00:02:27.876 :     sortcolumn = datefac
24/07/2025 : 00:02:27.876 :     sortdir = descending
24/07/2025 : 00:02:36.294 :     Count = 385217
24/07/2025 : 00:02:36.295 : Leaving
24/07/2025 : 00:08:57.958 : Entering
24/07/2025 : 00:08:57.958 :     dateMinFacture = 06/06/2019 00:00:00
24/07/2025 : 00:08:57.959 :     dateMaxFacture = 06/02/2020 00:00:00
24/07/2025 : 00:08:57.959 :     sortcolumn = datefac
24/07/2025 : 00:08:57.965 :     sortdir = descending
24/07/2025 : 00:09:08.610 :     Count = 385217
24/07/2025 : 00:09:08.611 : Leaving
24/07/2025 : 00:10:34.071 : Entering
24/07/2025 : 00:10:34.071 :     dateMinFacture = 
24/07/2025 : 00:10:34.072 :     dateMaxFacture = 
24/07/2025 : 00:10:34.073 :     sortcolumn = numfac
24/07/2025 : 00:10:34.073 :     sortdir = ascending
24/07/2025 : 00:10:38.325 :     Count = 8832132
24/07/2025 : 00:10:38.325 : Leaving
24/07/2025 : 00:10:38.374 : Entering
24/07/2025 : 00:10:38.374 :     dateMinFacture = 
24/07/2025 : 00:10:38.375 :     dateMaxFacture = 
24/07/2025 : 00:10:38.376 :     sortcolumn = datefac
24/07/2025 : 00:10:38.376 :     sortdir = descending
24/07/2025 : 00:11:09.309 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
24/07/2025 : 00:11:24.390 :     Entering
24/07/2025 : 00:11:24.391 :         dateMinFacture = 
24/07/2025 : 00:11:24.391 :         dateMaxFacture = 
24/07/2025 : 00:11:24.392 :         sortcolumn = datefac
24/07/2025 : 00:11:24.392 :         sortdir = descending
24/07/2025 : 00:11:27.668 :         Count = 55904
24/07/2025 : 00:11:27.669 :     Leaving
24/07/2025 : 00:11:38.719 :     Entering
24/07/2025 : 00:11:38.720 :         dateMinFacture = 05/07/2025 00:00:00
24/07/2025 : 00:11:38.721 :         dateMaxFacture = 
24/07/2025 : 00:11:38.721 :         sortcolumn = datefac
24/07/2025 : 00:11:38.721 :         sortdir = descending
24/07/2025 : 00:11:41.481 :         Count = 0
24/07/2025 : 00:11:41.482 :     Leaving
24/07/2025 : 00:15:53.961 :     Entering
24/07/2025 : 00:15:53.961 :         dateMinFacture = 
24/07/2025 : 00:15:53.962 :         dateMaxFacture = 
24/07/2025 : 00:15:53.962 :         sortcolumn = numfac
24/07/2025 : 00:15:53.963 :         sortdir = ascending
24/07/2025 : 00:15:57.475 :         Count = 8832132
24/07/2025 : 00:15:57.476 :     Leaving
24/07/2025 : 00:16:17.284 :     Entering
24/07/2025 : 00:16:17.284 :         dateMinFacture = 
24/07/2025 : 00:16:17.285 :         dateMaxFacture = 
24/07/2025 : 00:16:17.285 :         sortcolumn = numfac
24/07/2025 : 00:16:17.286 :         sortdir = ascending
24/07/2025 : 01:10:14.901 : Entering
24/07/2025 : 01:10:14.904 :     dateMinFacture = 
24/07/2025 : 01:10:14.904 :     dateMaxFacture = 
24/07/2025 : 01:10:14.905 :     sortcolumn = numfac
24/07/2025 : 01:10:14.905 :     sortdir = ascending
24/07/2025 : 01:10:20.488 :     Count = 8832132
24/07/2025 : 01:10:20.489 : Leaving
24/07/2025 : 01:10:23.445 : Entering
24/07/2025 : 01:10:23.445 :     dateMinFacture = 
24/07/2025 : 01:10:23.446 :     dateMaxFacture = 
24/07/2025 : 01:10:23.447 :     sortcolumn = numfac
24/07/2025 : 01:10:23.447 :     sortdir = ascending
24/07/2025 : 01:10:26.250 :     Count = 8832132
24/07/2025 : 01:10:26.251 : Leaving
24/07/2025 : 03:01:19.258 : Entering
24/07/2025 : 03:01:19.263 :     dateMinFacture = 
24/07/2025 : 03:01:19.263 :     dateMaxFacture = 
24/07/2025 : 03:01:19.264 :     sortcolumn = numfac
24/07/2025 : 03:01:19.264 :     sortdir = ascending
24/07/2025 : 03:01:26.017 :     Count = 8832132
24/07/2025 : 03:01:26.019 : Leaving
24/07/2025 : 03:01:26.122 : Entering
24/07/2025 : 03:01:26.123 :     dateMinFacture = 
24/07/2025 : 03:01:26.124 :     dateMaxFacture = 
24/07/2025 : 03:01:26.124 :     sortcolumn = dateachat
24/07/2025 : 03:01:26.124 :     sortdir = descending
24/07/2025 : 03:01:28.673 :     Count = 8832132
24/07/2025 : 03:01:28.673 : Leaving
24/07/2025 : 03:01:32.245 : Entering
24/07/2025 : 03:01:32.245 :     dateMinFacture = 
24/07/2025 : 03:01:32.247 :     dateMaxFacture = 
24/07/2025 : 03:01:32.247 :     sortcolumn = dateachat
24/07/2025 : 03:01:32.248 :     sortdir = descending
24/07/2025 : 03:01:42.478 :     Count = 55904
24/07/2025 : 03:01:42.479 : Leaving
24/07/2025 : 03:01:51.690 : Entering
24/07/2025 : 03:01:51.691 :     dateMinFacture = 
24/07/2025 : 03:01:51.692 :     dateMaxFacture = 
24/07/2025 : 03:01:51.693 :     sortcolumn = dateachat
24/07/2025 : 03:01:51.694 :     sortdir = descending
24/07/2025 : 03:01:56.742 :     Count = 55667
24/07/2025 : 03:01:56.742 : Leaving
24/07/2025 : 03:02:08.740 : Entering
24/07/2025 : 03:02:08.741 :     dateMinFacture = 
24/07/2025 : 03:02:08.745 :     dateMaxFacture = 
24/07/2025 : 03:02:08.748 :     sortcolumn = dateachat
24/07/2025 : 03:02:08.749 :     sortdir = descending
24/07/2025 : 03:02:13.712 :     Count = 50665
24/07/2025 : 03:02:13.713 : Leaving
24/07/2025 : 03:02:33.218 : Entering
24/07/2025 : 03:02:33.218 :     dateMinFacture = 16/06/2023 00:00:00
24/07/2025 : 03:02:33.220 :     dateMaxFacture = 
24/07/2025 : 03:02:33.220 :     sortcolumn = dateachat
24/07/2025 : 03:02:33.221 :     sortdir = descending
24/07/2025 : 03:02:38.146 :     Count = 1860
24/07/2025 : 03:02:38.147 : Leaving
24/07/2025 : 03:11:09.425 : Entering
24/07/2025 : 03:11:09.426 :     dateMinFacture = 
24/07/2025 : 03:11:09.427 :     dateMaxFacture = 
24/07/2025 : 03:11:09.427 :     sortcolumn = numfac
24/07/2025 : 03:11:09.427 :     sortdir = ascending
24/07/2025 : 03:11:15.160 :     Count = 8832132
24/07/2025 : 03:11:15.161 : Leaving
24/07/2025 : 03:11:19.031 : Entering
24/07/2025 : 03:11:19.032 :     dateMinFacture = 
24/07/2025 : 03:11:19.032 :     dateMaxFacture = 
24/07/2025 : 03:11:19.032 :     sortcolumn = numfac
24/07/2025 : 03:11:19.033 :     sortdir = ascending
24/07/2025 : 03:11:21.781 :     Count = 8832132
24/07/2025 : 03:11:21.782 : Leaving
24/07/2025 : 03:47:51.244 : Entering
24/07/2025 : 03:47:51.247 :     dateMinFacture = 
24/07/2025 : 03:47:51.247 :     dateMaxFacture = 
24/07/2025 : 03:47:51.248 :     sortcolumn = numfac
24/07/2025 : 03:47:51.248 :     sortdir = ascending
24/07/2025 : 03:47:58.549 :     Count = 8832132
24/07/2025 : 03:47:58.550 : Leaving
24/07/2025 : 03:48:58.905 : Entering
24/07/2025 : 03:48:58.907 :     dateMinFacture = 
24/07/2025 : 03:48:58.908 :     dateMaxFacture = 
24/07/2025 : 03:48:58.908 :     sortcolumn = numfac
24/07/2025 : 03:48:58.909 :     sortdir = ascending
24/07/2025 : 03:49:02.002 :     Count = 8832132
24/07/2025 : 03:49:02.002 : Leaving
24/07/2025 : 11:58:28.209 : Entering
24/07/2025 : 11:58:28.213 :     dateMinFacture = 
24/07/2025 : 11:58:28.213 :     dateMaxFacture = 
24/07/2025 : 11:58:28.213 :     sortcolumn = numfac
24/07/2025 : 11:58:28.214 :     sortdir = ascending
24/07/2025 : 11:58:39.115 :     Count = 8832132
24/07/2025 : 11:58:39.115 : Leaving
24/07/2025 : 11:58:57.762 : Entering
24/07/2025 : 11:58:57.763 :     dateMinFacture = 
24/07/2025 : 11:58:57.763 :     dateMaxFacture = 
24/07/2025 : 11:58:57.763 :     sortcolumn = numfac
24/07/2025 : 11:58:57.763 :     sortdir = ascending
24/07/2025 : 11:58:58.101 :     Count = 0
24/07/2025 : 11:58:58.101 : Leaving
24/07/2025 : 11:59:09.335 : Entering
24/07/2025 : 11:59:09.336 :     dateMinFacture = 
24/07/2025 : 11:59:09.338 :     dateMaxFacture = 
24/07/2025 : 11:59:09.339 :     sortcolumn = numfac
24/07/2025 : 11:59:09.340 :     sortdir = ascending
24/07/2025 : 11:59:11.652 :     Count = 55904
24/07/2025 : 11:59:11.653 : Leaving
24/07/2025 : 11:59:20.687 : Entering
24/07/2025 : 11:59:20.689 :     dateMinFacture = 
24/07/2025 : 11:59:20.695 :     dateMaxFacture = 
24/07/2025 : 11:59:20.696 :     sortcolumn = numfac
24/07/2025 : 11:59:20.698 :     sortdir = ascending
24/07/2025 : 11:59:22.681 :     Count = 13771
24/07/2025 : 11:59:22.682 : Leaving
24/07/2025 : 11:59:34.335 : Entering
24/07/2025 : 11:59:34.335 :     dateMinFacture = 
24/07/2025 : 11:59:34.337 :     dateMaxFacture = 
24/07/2025 : 11:59:34.337 :     sortcolumn = numfac
24/07/2025 : 11:59:34.338 :     sortdir = ascending
24/07/2025 : 11:59:34.665 :     Count = 1550
24/07/2025 : 11:59:34.665 : Leaving
24/07/2025 : 11:59:39.515 : Entering
24/07/2025 : 11:59:39.516 :     dateMinFacture = 
24/07/2025 : 11:59:39.520 :     dateMaxFacture = 
24/07/2025 : 11:59:39.527 :     sortcolumn = numfac
24/07/2025 : 11:59:39.529 :     sortdir = ascending
24/07/2025 : 11:59:39.712 :     Count = 1550
24/07/2025 : 11:59:39.712 : Leaving
24/07/2025 : 11:59:49.471 : Entering
24/07/2025 : 11:59:49.471 :     dateMinFacture = 
24/07/2025 : 11:59:49.473 :     dateMaxFacture = 
24/07/2025 : 11:59:49.474 :     sortcolumn = numfac
24/07/2025 : 11:59:49.475 :     sortdir = ascending
24/07/2025 : 11:59:51.497 :     Count = 1550
24/07/2025 : 11:59:51.497 : Leaving
24/07/2025 : 12:07:58.665 : Entering
24/07/2025 : 12:07:58.667 :     dateMinFacture = 
24/07/2025 : 12:07:58.667 :     dateMaxFacture = 
24/07/2025 : 12:07:58.668 :     sortcolumn = numfac
24/07/2025 : 12:07:58.668 :     sortdir = ascending
24/07/2025 : 12:08:04.783 :     Count = 8832132
24/07/2025 : 12:08:04.784 : Leaving
24/07/2025 : 12:08:05.388 : Entering
24/07/2025 : 12:08:05.388 :     dateMinFacture = 
24/07/2025 : 12:08:05.388 :     dateMaxFacture = 
24/07/2025 : 12:08:05.389 :     sortcolumn = numfac
24/07/2025 : 12:08:05.389 :     sortdir = ascending
24/07/2025 : 12:08:07.708 :     Count = 8832132
24/07/2025 : 12:08:07.709 : Leaving
24/07/2025 : 12:08:30.570 : Entering
24/07/2025 : 12:08:30.570 :     dateMinFacture = 
24/07/2025 : 12:08:30.571 :     dateMaxFacture = 
24/07/2025 : 12:08:30.571 :     sortcolumn = numfac
24/07/2025 : 12:08:30.574 :     sortdir = ascending
24/07/2025 : 12:08:38.699 :     Count = 8832132
24/07/2025 : 12:08:38.700 : Leaving
24/07/2025 : 12:08:38.817 : Entering
24/07/2025 : 12:08:38.817 :     dateMinFacture = 
24/07/2025 : 12:08:38.818 :     dateMaxFacture = 
24/07/2025 : 12:08:38.819 :     sortcolumn = numfac
24/07/2025 : 12:08:38.819 :     sortdir = ascending
24/07/2025 : 12:08:46.145 :     Count = 8832132
24/07/2025 : 12:08:46.146 : Leaving
24/07/2025 : 12:10:00.437 : Entering
24/07/2025 : 12:10:00.437 :     dateMinFacture = 
24/07/2025 : 12:10:00.438 :     dateMaxFacture = 
24/07/2025 : 12:10:00.438 :     sortcolumn = numfac
24/07/2025 : 12:10:00.438 :     sortdir = ascending
24/07/2025 : 12:10:03.244 :     Count = 8832132
24/07/2025 : 12:10:03.245 : Leaving
24/07/2025 : 12:10:03.371 : Entering
24/07/2025 : 12:10:03.371 :     dateMinFacture = 
24/07/2025 : 12:10:03.372 :     dateMaxFacture = 
24/07/2025 : 12:10:03.372 :     sortcolumn = numfac
24/07/2025 : 12:10:03.373 :     sortdir = ascending
24/07/2025 : 12:10:05.726 :     Count = 8832132
24/07/2025 : 12:10:05.727 : Leaving
24/07/2025 : 12:10:28.623 : Entering
24/07/2025 : 12:10:28.624 :     dateMinFacture = 
24/07/2025 : 12:10:28.625 :     dateMaxFacture = 
24/07/2025 : 12:10:28.626 :     sortcolumn = numfac
24/07/2025 : 12:10:28.627 :     sortdir = ascending
24/07/2025 : 12:10:31.162 :     Count = 12165
24/07/2025 : 12:10:31.162 : Leaving
24/07/2025 : 12:13:47.075 : Entering
24/07/2025 : 12:13:47.075 :     dateMinFacture = 
24/07/2025 : 12:13:47.077 :     dateMaxFacture = 
24/07/2025 : 12:13:47.077 :     sortcolumn = numfac
24/07/2025 : 12:13:47.077 :     sortdir = ascending
24/07/2025 : 12:13:47.405 :     Count = 0
24/07/2025 : 12:13:47.406 : Leaving
24/07/2025 : 12:13:52.124 : Entering
24/07/2025 : 12:13:52.125 :     dateMinFacture = 
24/07/2025 : 12:13:52.130 :     dateMaxFacture = 
24/07/2025 : 12:13:52.131 :     sortcolumn = numfac
24/07/2025 : 12:13:52.134 :     sortdir = ascending
24/07/2025 : 12:13:55.049 :     Count = 12165
24/07/2025 : 12:13:55.050 : Leaving
24/07/2025 : 12:14:06.514 : Entering
24/07/2025 : 12:14:06.515 :     dateMinFacture = 
24/07/2025 : 12:14:06.519 :     dateMaxFacture = 
24/07/2025 : 12:14:06.520 :     sortcolumn = numfac
24/07/2025 : 12:14:06.521 :     sortdir = ascending
24/07/2025 : 12:14:06.783 :     Count = 0
24/07/2025 : 12:14:06.785 : Leaving
24/07/2025 : 12:14:18.599 : Entering
24/07/2025 : 12:14:18.600 :     dateMinFacture = 
24/07/2025 : 12:14:18.602 :     dateMaxFacture = 
24/07/2025 : 12:14:18.603 :     sortcolumn = numfac
24/07/2025 : 12:14:18.605 :     sortdir = ascending
24/07/2025 : 12:14:18.785 :     Count = 0
24/07/2025 : 12:14:18.786 : Leaving
24/07/2025 : 12:14:24.811 : Entering
24/07/2025 : 12:14:24.811 :     dateMinFacture = 
24/07/2025 : 12:14:24.813 :     dateMaxFacture = 
24/07/2025 : 12:14:24.813 :     sortcolumn = numfac
24/07/2025 : 12:14:24.814 :     sortdir = ascending
24/07/2025 : 12:14:25.007 :     Count = 0
24/07/2025 : 12:14:25.007 : Leaving
24/07/2025 : 12:14:26.218 : Entering
24/07/2025 : 12:14:26.218 :     dateMinFacture = 
24/07/2025 : 12:14:26.221 :     dateMaxFacture = 
24/07/2025 : 12:14:26.222 :     sortcolumn = numfac
24/07/2025 : 12:14:26.223 :     sortdir = ascending
24/07/2025 : 12:14:26.427 :     Count = 0
24/07/2025 : 12:14:26.428 : Leaving
24/07/2025 : 12:14:42.376 : Entering
24/07/2025 : 12:14:42.377 :     dateMinFacture = 
24/07/2025 : 12:14:42.378 :     dateMaxFacture = 
24/07/2025 : 12:14:42.378 :     sortcolumn = numfac
24/07/2025 : 12:14:42.379 :     sortdir = ascending
24/07/2025 : 12:14:44.719 :     Count = 0
24/07/2025 : 12:14:44.720 : Leaving
24/07/2025 : 12:14:54.377 : Entering
24/07/2025 : 12:14:54.378 :     dateMinFacture = 
24/07/2025 : 12:14:54.379 :     dateMaxFacture = 
24/07/2025 : 12:14:54.380 :     sortcolumn = numfac
24/07/2025 : 12:14:54.380 :     sortdir = ascending
24/07/2025 : 12:14:56.403 :     Count = 12165
24/07/2025 : 12:14:56.403 : Leaving
24/07/2025 : 12:15:10.739 : Entering
24/07/2025 : 12:15:10.740 :     dateMinFacture = 
24/07/2025 : 12:15:10.744 :     dateMaxFacture = 
24/07/2025 : 12:15:10.744 :     sortcolumn = numfac
24/07/2025 : 12:15:10.745 :     sortdir = ascending
24/07/2025 : 12:15:11.010 :     Count = 0
24/07/2025 : 12:15:11.010 : Leaving
24/07/2025 : 12:25:38.359 : Entering
24/07/2025 : 12:25:38.398 :     dateMinFacture = 
24/07/2025 : 12:25:38.399 :     dateMaxFacture = 
24/07/2025 : 12:25:38.400 :     sortcolumn = numfac
24/07/2025 : 12:25:38.401 :     sortdir = ascending
24/07/2025 : 12:25:39.015 :     Count = 0
24/07/2025 : 12:25:39.015 : Leaving
24/07/2025 : 12:25:40.413 : Entering
24/07/2025 : 12:25:40.414 :     dateMinFacture = 
24/07/2025 : 12:25:40.416 :     dateMaxFacture = 
24/07/2025 : 12:25:40.416 :     sortcolumn = numfac
24/07/2025 : 12:25:40.417 :     sortdir = ascending
24/07/2025 : 12:25:40.618 :     Count = 0
24/07/2025 : 12:25:40.618 : Leaving
24/07/2025 : 12:25:41.811 : Entering
24/07/2025 : 12:25:41.812 :     dateMinFacture = 
24/07/2025 : 12:25:41.813 :     dateMaxFacture = 
24/07/2025 : 12:25:41.813 :     sortcolumn = numfac
24/07/2025 : 12:25:41.814 :     sortdir = ascending
24/07/2025 : 12:25:42.025 :     Count = 0
24/07/2025 : 12:25:42.026 : Leaving
24/07/2025 : 12:25:43.079 : Entering
24/07/2025 : 12:25:43.080 :     dateMinFacture = 
24/07/2025 : 12:25:43.081 :     dateMaxFacture = 
24/07/2025 : 12:25:43.082 :     sortcolumn = numfac
24/07/2025 : 12:25:43.082 :     sortdir = ascending
24/07/2025 : 12:25:43.305 :     Count = 0
24/07/2025 : 12:25:43.305 : Leaving
24/07/2025 : 12:26:04.644 : Entering
24/07/2025 : 12:26:04.645 :     dateMinFacture = 
24/07/2025 : 12:26:04.648 :     dateMaxFacture = 
24/07/2025 : 12:26:04.648 :     sortcolumn = numfac
24/07/2025 : 12:26:04.649 :     sortdir = ascending
24/07/2025 : 12:26:08.415 :     Count = 12165
24/07/2025 : 12:26:08.416 : Leaving
24/07/2025 : 12:26:18.781 : Entering
24/07/2025 : 12:26:18.782 :     dateMinFacture = 
24/07/2025 : 12:26:18.786 :     dateMaxFacture = 
24/07/2025 : 12:26:18.788 :     sortcolumn = numfac
24/07/2025 : 12:26:18.790 :     sortdir = ascending
24/07/2025 : 12:26:18.967 :     Count = 0
24/07/2025 : 12:26:18.968 : Leaving
24/07/2025 : 12:32:31.044 : Entering
24/07/2025 : 12:32:31.047 :     dateMinFacture = 
24/07/2025 : 12:32:31.047 :     dateMaxFacture = 
24/07/2025 : 12:32:31.048 :     sortcolumn = numfac
24/07/2025 : 12:32:31.048 :     sortdir = ascending
24/07/2025 : 12:32:40.033 :     Count = 8832132
24/07/2025 : 12:32:40.033 : Leaving
24/07/2025 : 12:32:40.541 : Entering
24/07/2025 : 12:32:40.541 :     dateMinFacture = 
24/07/2025 : 12:32:40.542 :     dateMaxFacture = 
24/07/2025 : 12:32:40.542 :     sortcolumn = numfac
24/07/2025 : 12:32:40.542 :     sortdir = ascending
24/07/2025 : 12:32:43.748 :     Count = 8832132
24/07/2025 : 12:32:43.749 : Leaving
24/07/2025 : 12:33:11.110 : Entering
24/07/2025 : 12:33:11.110 :     dateMinFacture = 
24/07/2025 : 12:33:11.112 :     dateMaxFacture = 
24/07/2025 : 12:33:11.112 :     sortcolumn = numfac
24/07/2025 : 12:33:11.113 :     sortdir = ascending
24/07/2025 : 12:33:13.579 :     Count = 55904
24/07/2025 : 12:33:13.581 : Leaving
24/07/2025 : 12:33:18.471 : Entering
24/07/2025 : 12:33:18.472 :     dateMinFacture = 
24/07/2025 : 12:33:18.476 :     dateMaxFacture = 
24/07/2025 : 12:33:18.476 :     sortcolumn = numfac
24/07/2025 : 12:33:18.477 :     sortdir = ascending
24/07/2025 : 12:33:20.855 :     Count = 55667
24/07/2025 : 12:33:20.856 : Leaving
24/07/2025 : 12:33:26.117 : Entering
24/07/2025 : 12:33:26.118 :     dateMinFacture = 
24/07/2025 : 12:33:26.120 :     dateMaxFacture = 
24/07/2025 : 12:33:26.120 :     sortcolumn = numfac
24/07/2025 : 12:33:26.121 :     sortdir = ascending
24/07/2025 : 12:33:28.280 :     Count = 50665
24/07/2025 : 12:33:28.281 : Leaving
24/07/2025 : 12:33:41.548 : Entering
24/07/2025 : 12:33:41.548 :     dateMinFacture = 
24/07/2025 : 12:33:41.550 :     dateMaxFacture = 
24/07/2025 : 12:33:41.551 :     sortcolumn = numfac
24/07/2025 : 12:33:41.552 :     sortdir = ascending
24/07/2025 : 12:33:43.774 :     Count = 50665
24/07/2025 : 12:33:43.775 : Leaving
24/07/2025 : 12:33:45.937 : Entering
24/07/2025 : 12:33:45.938 :     dateMinFacture = 
24/07/2025 : 12:33:45.940 :     dateMaxFacture = 
24/07/2025 : 12:33:45.941 :     sortcolumn = numfac
24/07/2025 : 12:33:45.942 :     sortdir = ascending
24/07/2025 : 12:33:53.980 :     Count = 50665
24/07/2025 : 12:33:53.981 : Leaving
24/07/2025 : 12:34:15.993 : Entering
24/07/2025 : 12:34:15.995 :     dateMinFacture = 10/06/2020 00:00:00
24/07/2025 : 12:34:15.996 :     dateMaxFacture = 
24/07/2025 : 12:34:15.996 :     sortcolumn = numfac
24/07/2025 : 12:34:15.997 :     sortdir = ascending
24/07/2025 : 12:34:19.223 :     Count = 9004
24/07/2025 : 12:34:19.224 : Leaving
24/07/2025 : 12:34:19.251 : Entering
24/07/2025 : 12:34:19.252 :     dateMinFacture = 10/06/2020 00:00:00
24/07/2025 : 12:34:19.255 :     dateMaxFacture = 
24/07/2025 : 12:34:19.260 :     sortcolumn = numfac
24/07/2025 : 12:34:19.264 :     sortdir = ascending
24/07/2025 : 12:34:22.576 :     Count = 9004
24/07/2025 : 12:34:22.577 : Leaving
24/07/2025 : 12:38:52.235 : Entering
24/07/2025 : 12:38:52.239 :     dateMinFacture = 
24/07/2025 : 12:38:52.239 :     dateMaxFacture = 
24/07/2025 : 12:38:52.239 :     sortcolumn = numfac
24/07/2025 : 12:38:52.239 :     sortdir = ascending
24/07/2025 : 12:38:58.871 :     Count = 8832132
24/07/2025 : 12:38:58.871 : Leaving
24/07/2025 : 12:38:59.259 : Entering
24/07/2025 : 12:38:59.259 :     dateMinFacture = 
24/07/2025 : 12:38:59.259 :     dateMaxFacture = 
24/07/2025 : 12:38:59.259 :     sortcolumn = numfac
24/07/2025 : 12:38:59.260 :     sortdir = ascending
24/07/2025 : 12:39:02.841 :     Count = 8832132
24/07/2025 : 12:39:02.842 : Leaving
24/07/2025 : 12:39:17.794 : Entering
24/07/2025 : 12:39:17.796 :     dateMinFacture = 23/07/2022 00:00:00
24/07/2025 : 12:39:17.796 :     dateMaxFacture = 
24/07/2025 : 12:39:17.797 :     sortcolumn = numfac
24/07/2025 : 12:39:17.797 :     sortdir = ascending
24/07/2025 : 12:39:21.859 :     Count = 5518
24/07/2025 : 12:39:21.861 : Leaving
24/07/2025 : 12:39:21.889 : Entering
24/07/2025 : 12:39:21.890 :     dateMinFacture = 23/07/2022 00:00:00
24/07/2025 : 12:39:21.894 :     dateMaxFacture = 
24/07/2025 : 12:39:21.896 :     sortcolumn = numfac
24/07/2025 : 12:39:21.900 :     sortdir = ascending
24/07/2025 : 12:39:25.161 :     Count = 5518
24/07/2025 : 12:39:25.162 : Leaving
24/07/2025 : 12:49:07.852 : Entering
24/07/2025 : 12:49:07.856 :     dateMinFacture = 
24/07/2025 : 12:49:07.856 :     dateMaxFacture = 
24/07/2025 : 12:49:07.856 :     sortcolumn = numfac
24/07/2025 : 12:49:07.857 :     sortdir = ascending
24/07/2025 : 12:49:13.947 :     Count = 8832132
24/07/2025 : 12:49:13.947 : Leaving
24/07/2025 : 12:49:14.276 : Entering
24/07/2025 : 12:49:14.276 :     dateMinFacture = 
24/07/2025 : 12:49:14.276 :     dateMaxFacture = 
24/07/2025 : 12:49:14.276 :     sortcolumn = numfac
24/07/2025 : 12:49:14.276 :     sortdir = ascending
24/07/2025 : 12:49:17.215 :     Count = 8832132
24/07/2025 : 12:49:17.217 : Leaving
24/07/2025 : 12:49:22.904 : Entering
24/07/2025 : 12:49:22.905 :     dateMinFacture = 
24/07/2025 : 12:49:22.906 :     dateMaxFacture = 
24/07/2025 : 12:49:22.908 :     sortcolumn = numfac
24/07/2025 : 12:49:22.908 :     sortdir = ascending
24/07/2025 : 12:49:25.074 :     Count = 55904
24/07/2025 : 12:49:25.075 : Leaving
24/07/2025 : 12:49:25.182 : Entering
24/07/2025 : 12:49:25.182 :     dateMinFacture = 
24/07/2025 : 12:49:25.183 :     dateMaxFacture = 
24/07/2025 : 12:49:25.184 :     sortcolumn = numfac
24/07/2025 : 12:49:25.184 :     sortdir = ascending
24/07/2025 : 12:49:27.344 :     Count = 55904
24/07/2025 : 12:49:27.345 : Leaving
24/07/2025 : 12:49:44.434 : Entering
24/07/2025 : 12:49:44.435 :     dateMinFacture = 
24/07/2025 : 12:49:44.438 :     dateMaxFacture = 
24/07/2025 : 12:49:44.440 :     sortcolumn = numfac
24/07/2025 : 12:49:44.441 :     sortdir = ascending
24/07/2025 : 12:49:46.708 :     Count = 55904
24/07/2025 : 12:49:46.709 : Leaving
24/07/2025 : 12:49:48.593 : Entering
24/07/2025 : 12:49:48.593 :     dateMinFacture = 
24/07/2025 : 12:49:48.596 :     dateMaxFacture = 
24/07/2025 : 12:49:48.598 :     sortcolumn = numfac
24/07/2025 : 12:49:48.601 :     sortdir = ascending
24/07/2025 : 12:49:50.951 :     Count = 55667
24/07/2025 : 12:49:50.952 : Leaving
24/07/2025 : 12:49:51.031 : Entering
24/07/2025 : 12:49:51.031 :     dateMinFacture = 
24/07/2025 : 12:49:51.033 :     dateMaxFacture = 
24/07/2025 : 12:49:51.036 :     sortcolumn = numfac
24/07/2025 : 12:49:51.037 :     sortdir = ascending
24/07/2025 : 12:49:53.042 :     Count = 55667
24/07/2025 : 12:49:53.042 : Leaving
24/07/2025 : 12:57:27.867 : Entering
24/07/2025 : 12:57:27.870 :     dateMinFacture = 
24/07/2025 : 12:57:27.870 :     dateMaxFacture = 
24/07/2025 : 12:57:27.870 :     sortcolumn = numfac
24/07/2025 : 12:57:27.871 :     sortdir = ascending
24/07/2025 : 12:57:34.554 :     Count = 8832132
24/07/2025 : 12:57:34.554 : Leaving
24/07/2025 : 12:57:35.080 : Entering
24/07/2025 : 12:57:35.080 :     dateMinFacture = 
24/07/2025 : 12:57:35.080 :     dateMaxFacture = 
24/07/2025 : 12:57:35.081 :     sortcolumn = numfac
24/07/2025 : 12:57:35.081 :     sortdir = ascending
24/07/2025 : 12:57:38.057 :     Count = 8832132
24/07/2025 : 12:57:38.057 : Leaving
24/07/2025 : 12:57:44.416 : Entering
24/07/2025 : 12:57:44.416 :     dateMinFacture = 
24/07/2025 : 12:57:44.418 :     dateMaxFacture = 
24/07/2025 : 12:57:44.418 :     sortcolumn = numfac
24/07/2025 : 12:57:44.419 :     sortdir = ascending
24/07/2025 : 12:57:46.604 :     Count = 50665
24/07/2025 : 12:57:46.604 : Leaving
24/07/2025 : 12:58:01.047 : Entering
24/07/2025 : 12:58:01.048 :     dateMinFacture = 15/07/2022 00:00:00
24/07/2025 : 12:58:01.049 :     dateMaxFacture = 
24/07/2025 : 12:58:01.051 :     sortcolumn = numfac
24/07/2025 : 12:58:01.052 :     sortdir = ascending
24/07/2025 : 12:58:05.806 :     Count = 5569
24/07/2025 : 12:58:05.808 : Leaving
24/07/2025 : 14:07:20.618 : Entering
24/07/2025 : 14:07:20.624 :     dateMinFacture = 
24/07/2025 : 14:07:20.625 :     dateMaxFacture = 
24/07/2025 : 14:07:20.625 :     sortcolumn = numfac
24/07/2025 : 14:07:20.625 :     sortdir = ascending
24/07/2025 : 14:07:29.659 :     Count = 8832132
24/07/2025 : 14:07:29.659 : Leaving
24/07/2025 : 14:08:30.275 : Entering
24/07/2025 : 14:08:30.276 :     dateMinFacture = 
24/07/2025 : 14:08:30.277 :     dateMaxFacture = 
24/07/2025 : 14:08:30.277 :     sortcolumn = numfac
24/07/2025 : 14:08:30.277 :     sortdir = ascending
24/07/2025 : 14:08:33.248 :     Count = 8832132
24/07/2025 : 14:08:33.249 : Leaving
24/07/2025 : 14:08:33.332 : Entering
24/07/2025 : 14:08:33.332 :     dateMinFacture = 
24/07/2025 : 14:08:33.333 :     dateMaxFacture = 
24/07/2025 : 14:08:33.333 :     sortcolumn = numfac
24/07/2025 : 14:08:33.333 :     sortdir = ascending
24/07/2025 : 14:08:35.503 :     Count = 50665
24/07/2025 : 14:08:35.504 : Leaving
24/07/2025 : 14:09:11.925 : Entering
24/07/2025 : 14:09:11.927 :     dateMinFacture = 24/07/2020 00:00:00
24/07/2025 : 14:09:11.928 :     dateMaxFacture = 
24/07/2025 : 14:09:11.928 :     sortcolumn = numfac
24/07/2025 : 14:09:11.929 :     sortdir = ascending
24/07/2025 : 14:09:18.664 :     Count = 8762
24/07/2025 : 14:09:18.666 : Leaving
