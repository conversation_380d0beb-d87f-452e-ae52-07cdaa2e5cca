{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\CarSystemsWebApp.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\CarSystemsWebApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\CarSystemsWebApp.csproj", "projectName": "CarSystemsWebApp", "projectPath": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\CarSystemsWebApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CarSystemsWebApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.3, )"}, "Syncfusion.Blazor.Buttons": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Calendars": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Charts": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.DataForm": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Diagram": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.DropDowns": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Grid": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Inputs": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Layouts": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Lists": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Navigations": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Popups": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.ProgressBar": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.RichTextEditor": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Schedule": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.Themes": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Blazor.TreeGrid": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.Pdf.Net.Core": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.XlsIO.Net.Core": {"target": "Package", "version": "[28.1.33, )"}, "Syncfusion.XlsIORenderer.Net.Core": {"target": "Package", "version": "[28.1.33, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}