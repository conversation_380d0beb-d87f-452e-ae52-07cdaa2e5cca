22/07/2025 : 08:39:45.151 : Entering
22/07/2025 : 08:39:45.155 :     Client           = 
22/07/2025 : 08:39:45.156 :     DateFactureStart = 
22/07/2025 : 08:39:45.156 :     DateFactureEnd   = 
22/07/2025 : 08:39:45.156 :     SQL Para<PERSON> - Client: '', DateStart: '', DateEnd: '', Etat: '', TypeFacture: ''
22/07/2025 : 08:39:59.132 :     Count = 8832132
22/07/2025 : 08:39:59.133 : Leaving
22/07/2025 : 08:43:33.593 : Entering
22/07/2025 : 08:43:33.595 :     Client           = 
22/07/2025 : 08:43:33.596 :     DateFactureStart = 
22/07/2025 : 08:43:33.596 :     DateFactureEnd   = 
22/07/2025 : 08:43:41.577 :     Count = 8832132
22/07/2025 : 08:43:41.577 : Leaving
22/07/2025 : 08:43:41.898 : Entering
22/07/2025 : 08:43:41.898 :     Client           = 
22/07/2025 : 08:43:41.898 :     DateFactureStart = 
22/07/2025 : 08:43:41.898 :     DateFactureEnd   = 
22/07/2025 : 08:43:44.285 :     Count = 8832132
22/07/2025 : 08:43:44.285 : Leaving
22/07/2025 : 08:43:44.379 : Entering
22/07/2025 : 08:43:44.379 :     Client           = 0026
22/07/2025 : 08:43:44.379 :     DateFactureStart = 
22/07/2025 : 08:43:44.379 :     DateFactureEnd   = 
22/07/2025 : 08:43:47.125 :     Count = 55904
22/07/2025 : 08:43:47.126 : Leaving
22/07/2025 : 08:43:47.186 : Entering
22/07/2025 : 08:43:47.186 :     Client           = 0026
22/07/2025 : 08:43:47.186 :     DateFactureStart = 
22/07/2025 : 08:43:47.188 :     DateFactureEnd   = 
22/07/2025 : 08:43:49.514 :     Count = 55904
22/07/2025 : 08:43:49.515 : Leaving
22/07/2025 : 08:44:23.542 : Entering
22/07/2025 : 08:44:23.542 :     Client           = 0026
22/07/2025 : 08:44:23.543 :     DateFactureStart = 06/06/2019 00:00:00
22/07/2025 : 08:44:23.543 :     DateFactureEnd   = 09/07/2021 00:00:00
22/07/2025 : 08:44:24.225 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
22/07/2025 : 09:06:19.146 : Entering
22/07/2025 : 09:06:19.148 :     Client           = 
22/07/2025 : 09:06:19.149 :     DateFactureStart = 
22/07/2025 : 09:06:19.149 :     DateFactureEnd   = 
22/07/2025 : 09:06:26.774 :     Count = 8832132
22/07/2025 : 09:06:26.777 : Leaving
22/07/2025 : 09:12:07.795 : Entering
22/07/2025 : 09:12:07.798 :     Client           = 
22/07/2025 : 09:12:07.799 :     DateFactureStart = 
22/07/2025 : 09:12:07.799 :     DateFactureEnd   = 
22/07/2025 : 09:12:14.292 :     Count = 8832132
22/07/2025 : 09:12:14.292 : Leaving
22/07/2025 : 09:16:57.918 : Entering
22/07/2025 : 09:16:57.921 :     Client           = 
22/07/2025 : 09:16:57.922 :     DateFactureStart = 
22/07/2025 : 09:16:57.922 :     DateFactureEnd   = 
22/07/2025 : 09:17:04.794 :     Count = 8832132
22/07/2025 : 09:17:04.795 : Leaving
22/07/2025 : 09:17:05.201 : Entering
22/07/2025 : 09:17:05.202 :     Client           = 
22/07/2025 : 09:17:05.202 :     DateFactureStart = 
22/07/2025 : 09:17:05.202 :     DateFactureEnd   = 
22/07/2025 : 09:17:07.524 :     Count = 8832132
22/07/2025 : 09:17:07.524 : Leaving
22/07/2025 : 09:17:44.392 : Entering
22/07/2025 : 09:17:44.392 :     Client           = 0026
22/07/2025 : 09:17:44.392 :     DateFactureStart = 
22/07/2025 : 09:17:44.393 :     DateFactureEnd   = 
22/07/2025 : 09:17:46.548 :     Count = 55904
22/07/2025 : 09:17:46.549 : Leaving
22/07/2025 : 09:18:24.016 : Entering
22/07/2025 : 09:18:24.017 :     Client           = 0026
22/07/2025 : 09:18:24.017 :     DateFactureStart = 
22/07/2025 : 09:18:24.021 :     DateFactureEnd   = 
22/07/2025 : 09:18:26.228 :     Count = 237
22/07/2025 : 09:18:26.228 : Leaving
22/07/2025 : 09:18:36.848 : Entering
22/07/2025 : 09:18:36.849 :     Client           = 0026
22/07/2025 : 09:18:36.849 :     DateFactureStart = 
22/07/2025 : 09:18:36.850 :     DateFactureEnd   = 
22/07/2025 : 09:18:37.267 :     Count = 0
22/07/2025 : 09:18:37.268 : Leaving
22/07/2025 : 09:19:45.049 : Entering
22/07/2025 : 09:19:45.060 :     Client           = 0026
22/07/2025 : 09:19:45.061 :     DateFactureStart = 
22/07/2025 : 09:19:45.061 :     DateFactureEnd   = 
22/07/2025 : 09:19:45.286 :     Invalid column name 'a'.
22/07/2025 : 09:19:47.009 :     Entering
22/07/2025 : 09:19:47.010 :         Client           = 0026
22/07/2025 : 09:19:47.011 :         DateFactureStart = 
22/07/2025 : 09:19:47.012 :         DateFactureEnd   = 
22/07/2025 : 09:19:47.124 :         Invalid column name 'a'.
22/07/2025 : 09:19:59.233 :         Entering
22/07/2025 : 09:19:59.234 :             Client           = 0026
22/07/2025 : 09:19:59.236 :             DateFactureStart = 
22/07/2025 : 09:19:59.236 :             DateFactureEnd   = 
22/07/2025 : 09:19:59.514 :             Count = 0
22/07/2025 : 09:19:59.514 :         Leaving
22/07/2025 : 09:20:05.181 :         Entering
22/07/2025 : 09:20:05.182 :             Client           = 0026
22/07/2025 : 09:20:05.183 :             DateFactureStart = 
22/07/2025 : 09:20:05.184 :             DateFactureEnd   = 
22/07/2025 : 09:20:05.382 :             Invalid column name 'a'.
22/07/2025 : 09:21:54.970 : Entering
22/07/2025 : 09:21:54.973 :     Client           = 
22/07/2025 : 09:21:54.973 :     DateFactureStart = 
22/07/2025 : 09:21:54.974 :     DateFactureEnd   = 
22/07/2025 : 09:22:01.110 :     Count = 8832132
22/07/2025 : 09:22:01.112 : Leaving
22/07/2025 : 09:22:01.529 : Entering
22/07/2025 : 09:22:01.529 :     Client           = 
22/07/2025 : 09:22:01.529 :     DateFactureStart = 
22/07/2025 : 09:22:01.529 :     DateFactureEnd   = 
22/07/2025 : 09:22:04.234 :     Count = 8832132
22/07/2025 : 09:22:04.235 : Leaving
22/07/2025 : 09:22:29.275 : Entering
22/07/2025 : 09:22:29.276 :     Client           = 0026
22/07/2025 : 09:22:29.276 :     DateFactureStart = 
22/07/2025 : 09:22:29.276 :     DateFactureEnd   = 
22/07/2025 : 09:22:30.079 :     Count = 0
22/07/2025 : 09:22:30.080 : Leaving
22/07/2025 : 09:22:40.966 : Entering
22/07/2025 : 09:22:40.967 :     Client           = 0026
22/07/2025 : 09:22:40.968 :     DateFactureStart = 
22/07/2025 : 09:22:40.969 :     DateFactureEnd   = 
22/07/2025 : 09:22:41.164 :     Count = 0
22/07/2025 : 09:22:41.165 : Leaving
22/07/2025 : 09:24:48.215 : Entering
22/07/2025 : 09:24:48.215 :     Client           = 
22/07/2025 : 09:24:48.216 :     DateFactureStart = 
22/07/2025 : 09:24:48.216 :     DateFactureEnd   = 
22/07/2025 : 09:24:53.286 :     Count = 8832132
22/07/2025 : 09:24:53.287 : Leaving
22/07/2025 : 09:24:53.389 : Entering
22/07/2025 : 09:24:53.390 :     Client           = 
22/07/2025 : 09:24:53.390 :     DateFactureStart = 
22/07/2025 : 09:24:53.390 :     DateFactureEnd   = 
22/07/2025 : 09:24:56.133 :     Count = 8832132
22/07/2025 : 09:24:56.134 : Leaving
22/07/2025 : 09:24:56.239 : Entering
22/07/2025 : 09:24:56.239 :     Client           = 
22/07/2025 : 09:24:56.240 :     DateFactureStart = 
22/07/2025 : 09:24:56.241 :     DateFactureEnd   = 
22/07/2025 : 09:25:00.250 :     Count = 8832132
22/07/2025 : 09:25:00.251 : Leaving
22/07/2025 : 09:25:09.086 : Entering
22/07/2025 : 09:25:09.086 :     Client           = 0026
22/07/2025 : 09:25:09.087 :     DateFactureStart = 
22/07/2025 : 09:25:09.087 :     DateFactureEnd   = 
22/07/2025 : 09:25:12.562 :     Count = 237
22/07/2025 : 09:25:12.563 : Leaving
22/07/2025 : 09:35:01.550 : Entering
22/07/2025 : 09:35:01.552 :     Client           = 
22/07/2025 : 09:35:01.553 :     DateFactureStart = 
22/07/2025 : 09:35:01.553 :     DateFactureEnd   = 
22/07/2025 : 09:35:15.098 :     Count = 8832132
22/07/2025 : 09:35:15.100 : Leaving
22/07/2025 : 09:35:15.657 : Entering
22/07/2025 : 09:35:15.657 :     Client           = 0026
22/07/2025 : 09:35:15.658 :     DateFactureStart = 
22/07/2025 : 09:35:15.658 :     DateFactureEnd   = 
22/07/2025 : 09:35:17.644 :     Count = 55904
22/07/2025 : 09:35:17.645 : Leaving
22/07/2025 : 09:35:17.783 : Entering
22/07/2025 : 09:35:17.783 :     Client           = 0026
22/07/2025 : 09:35:17.783 :     DateFactureStart = 
22/07/2025 : 09:35:17.784 :     DateFactureEnd   = 
22/07/2025 : 09:35:19.757 :     Count = 55904
22/07/2025 : 09:35:19.758 : Leaving
22/07/2025 : 09:39:30.574 : Entering
22/07/2025 : 09:39:30.574 :     Client           = 0026
22/07/2025 : 09:39:30.575 :     DateFactureStart = 
22/07/2025 : 09:39:30.576 :     DateFactureEnd   = 
22/07/2025 : 09:39:32.771 :     Count = 237
22/07/2025 : 09:39:32.771 : Leaving
22/07/2025 : 09:43:07.177 : Entering
22/07/2025 : 09:43:07.178 :     Client           = 0026
22/07/2025 : 09:43:07.178 :     DateFactureStart = 11/06/2011 00:00:00
22/07/2025 : 09:43:07.180 :     DateFactureEnd   = 11/07/2025 00:00:00
22/07/2025 : 09:43:07.770 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
22/07/2025 : 09:58:58.419 :     Entering
22/07/2025 : 09:58:58.426 :         Client           = 0026
22/07/2025 : 09:58:58.427 :         DateFactureStart = 11/06/2011 00:00:00
22/07/2025 : 09:58:58.428 :         DateFactureEnd   = 11/07/2025 00:00:00
22/07/2025 : 09:58:59.607 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
22/07/2025 : 10:17:07.246 : Entering
22/07/2025 : 10:17:07.250 :     Client           = 
22/07/2025 : 10:17:07.251 :     DateFactureStart = 
22/07/2025 : 10:17:07.251 :     DateFactureEnd   = 
22/07/2025 : 10:17:10.550 :     The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:17:10.792 :     Entering
22/07/2025 : 10:17:10.792 :         Client           = 
22/07/2025 : 10:17:10.792 :         DateFactureStart = 
22/07/2025 : 10:17:10.792 :         DateFactureEnd   = 
22/07/2025 : 10:17:11.379 :         The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:17:45.188 :         Entering
22/07/2025 : 10:17:45.189 :             Client           = 
22/07/2025 : 10:17:45.189 :             DateFactureStart = 
22/07/2025 : 10:17:45.190 :             DateFactureEnd   = 16/09/2020 00:00:00
22/07/2025 : 10:17:45.837 :             The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:17:50.558 :             Entering
22/07/2025 : 10:17:50.558 :                 Client           = 
22/07/2025 : 10:17:50.559 :                 DateFactureStart = 
22/07/2025 : 10:17:50.559 :                 DateFactureEnd   = 16/09/2020 00:00:00
22/07/2025 : 10:17:51.012 :                 The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:17:54.495 :                 Entering
22/07/2025 : 10:17:54.495 :                     Client           = 
22/07/2025 : 10:17:54.496 :                     DateFactureStart = 
22/07/2025 : 10:17:54.497 :                     DateFactureEnd   = 
22/07/2025 : 10:17:55.010 :                     The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:27:01.229 : Entering
22/07/2025 : 10:27:01.232 :     Client           = 
22/07/2025 : 10:27:01.234 :     DateFactureStart = 
22/07/2025 : 10:27:01.234 :     DateFactureEnd   = 
22/07/2025 : 10:27:05.460 :     Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:27:05.792 :     Entering
22/07/2025 : 10:27:05.792 :         Client           = 
22/07/2025 : 10:27:05.793 :         DateFactureStart = 
22/07/2025 : 10:27:05.793 :         DateFactureEnd   = 
22/07/2025 : 10:27:07.791 :         Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:27:26.230 :         Entering
22/07/2025 : 10:27:26.230 :             Client           = 
22/07/2025 : 10:27:26.231 :             DateFactureStart = 
22/07/2025 : 10:27:26.232 :             DateFactureEnd   = 
22/07/2025 : 10:27:26.779 :             Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:32:39.454 : Entering
22/07/2025 : 10:32:39.457 :     Client           = 
22/07/2025 : 10:32:39.458 :     DateFactureStart = 
22/07/2025 : 10:32:39.458 :     DateFactureEnd   = 
22/07/2025 : 10:32:42.948 :     Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:37:19.525 : Entering
22/07/2025 : 10:37:19.528 :     Client           = 
22/07/2025 : 10:37:19.529 :     DateFactureStart = 
22/07/2025 : 10:37:19.529 :     DateFactureEnd   = 
22/07/2025 : 10:37:25.183 :     Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:37:25.456 :     Entering
22/07/2025 : 10:37:25.457 :         Client           = 
22/07/2025 : 10:37:25.460 :         DateFactureStart = 
22/07/2025 : 10:37:25.460 :         DateFactureEnd   = 
22/07/2025 : 10:37:26.337 :         Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:39:52.791 :         Entering
22/07/2025 : 10:39:52.792 :             Client           = 0026
22/07/2025 : 10:39:52.793 :             DateFactureStart = 
22/07/2025 : 10:39:52.794 :             DateFactureEnd   = 
22/07/2025 : 10:39:53.370 :             Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:43:00.162 : Entering
22/07/2025 : 10:43:00.164 :     Client           = 
22/07/2025 : 10:43:00.165 :     DateFactureStart = 
22/07/2025 : 10:43:00.165 :     DateFactureEnd   = 
22/07/2025 : 10:43:04.716 :     Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:43:05.007 :     Entering
22/07/2025 : 10:43:05.007 :         Client           = 
22/07/2025 : 10:43:05.009 :         DateFactureStart = 
22/07/2025 : 10:43:05.009 :         DateFactureEnd   = 
22/07/2025 : 10:43:05.832 :         Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 10:47:54.104 : Entering
22/07/2025 : 10:47:54.107 :     Client           = 
22/07/2025 : 10:47:54.108 :     DateFactureStart = 
22/07/2025 : 10:47:54.108 :     DateFactureEnd   = 
22/07/2025 : 10:47:58.456 :     The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:47:58.751 :     Entering
22/07/2025 : 10:47:58.752 :         Client           = 
22/07/2025 : 10:47:58.752 :         DateFactureStart = 
22/07/2025 : 10:47:58.752 :         DateFactureEnd   = 
22/07/2025 : 10:47:59.295 :         The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:49:31.682 :         Entering
22/07/2025 : 10:49:31.683 :             Client           = 
22/07/2025 : 10:49:31.685 :             DateFactureStart = 
22/07/2025 : 10:49:31.685 :             DateFactureEnd   = 
22/07/2025 : 10:49:32.415 :             The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 10:49:32.429 :             Entering
22/07/2025 : 10:49:32.429 :                 Client           = 
22/07/2025 : 10:49:32.430 :                 DateFactureStart = 
22/07/2025 : 10:49:32.430 :                 DateFactureEnd   = 
22/07/2025 : 10:49:33.061 :                 The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:11:53.564 : Entering
22/07/2025 : 11:11:53.567 :     Client           = 
22/07/2025 : 11:11:53.567 :     DateFactureStart = 
22/07/2025 : 11:11:53.567 :     DateFactureEnd   = 
22/07/2025 : 11:11:57.376 :     Error in DataAdaptor.Read: The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:11:57.377 :     Stack trace:    at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.BuildIndexMap(IReadOnlyList`1 columnNames, DbDataReader dataReader)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 539
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Read(DataManagerRequest dm, String key) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 499
22/07/2025 : 11:11:57.585 :     Entering
22/07/2025 : 11:11:57.585 :         Client           = 
22/07/2025 : 11:11:57.585 :         DateFactureStart = 
22/07/2025 : 11:11:57.586 :         DateFactureEnd   = 
22/07/2025 : 11:11:58.309 :         Error in DataAdaptor.Read: The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:11:58.310 :         Stack trace:    at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.BuildIndexMap(IReadOnlyList`1 columnNames, DbDataReader dataReader)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 539
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Read(DataManagerRequest dm, String key) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 499
22/07/2025 : 11:25:51.813 : Entering
22/07/2025 : 11:25:51.816 :     Client           = 
22/07/2025 : 11:25:51.817 :     DateFactureStart = 
22/07/2025 : 11:25:51.817 :     DateFactureEnd   = 
22/07/2025 : 11:25:56.999 :     Error in DataAdaptor.Read: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 11:25:57.001 :     Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 611
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Read(DataManagerRequest dm, String key) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 571
22/07/2025 : 11:25:57.365 :     Entering
22/07/2025 : 11:25:57.366 :         Client           = 
22/07/2025 : 11:25:57.369 :         DateFactureStart = 
22/07/2025 : 11:25:57.369 :         DateFactureEnd   = 
22/07/2025 : 11:25:58.329 :         Error in DataAdaptor.Read: Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 11:25:58.329 :         Stack trace:    at Microsoft.Data.SqlClient.SqlBuffer.get_DateTime()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDateTime(Int32 i)
   at lambda_method118(Closure, QueryContext, DbDataReader, Int32[])
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 611
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Read(DataManagerRequest dm, String key) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 571
22/07/2025 : 11:27:02.320 : Entering
22/07/2025 : 11:27:02.322 :     Client           = 
22/07/2025 : 11:27:02.323 :     DateFactureStart = 
22/07/2025 : 11:27:02.323 :     DateFactureEnd   = 
22/07/2025 : 11:27:05.918 :     The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:27:06.184 :     Entering
22/07/2025 : 11:27:06.184 :         Client           = 
22/07/2025 : 11:27:06.185 :         DateFactureStart = 
22/07/2025 : 11:27:06.185 :         DateFactureEnd   = 
22/07/2025 : 11:27:07.456 :         The required column 'datefac2' was not present in the results of a 'FromSql' operation.
22/07/2025 : 11:56:35.661 : Entering
22/07/2025 : 11:56:35.665 :     Client           = 
22/07/2025 : 11:56:35.666 :     DateFactureStart = 
22/07/2025 : 11:56:35.666 :     DateFactureEnd   = 
22/07/2025 : 11:56:35.667 :     NO SORTING: Using default sort
22/07/2025 : 11:56:38.474 :     Items retrieved: 0
22/07/2025 : 11:57:08.919 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 11:57:09.201 :     Entering
22/07/2025 : 11:57:09.202 :         Client           = 
22/07/2025 : 11:57:09.202 :         DateFactureStart = 
22/07/2025 : 11:57:09.203 :         DateFactureEnd   = 
22/07/2025 : 11:57:09.203 :         NO SORTING: Using default sort
22/07/2025 : 11:57:09.598 :         Items retrieved: 0
22/07/2025 : 11:57:39.672 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 12:03:46.853 : Entering
22/07/2025 : 12:05:11.279 :     Client           = 
22/07/2025 : 12:05:11.279 :     DateFactureStart = 
22/07/2025 : 12:05:11.279 :     DateFactureEnd   = 
22/07/2025 : 12:05:23.637 :     Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:05:23.833 :     Entering
22/07/2025 : 12:05:23.833 :         Client           = 
22/07/2025 : 12:05:23.834 :         DateFactureStart = 
22/07/2025 : 12:05:23.834 :         DateFactureEnd   = 
22/07/2025 : 12:05:25.381 :         Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:05:25.495 :         Entering
22/07/2025 : 12:05:25.495 :             Client           = 
22/07/2025 : 12:05:25.497 :             DateFactureStart = 
22/07/2025 : 12:05:25.497 :             DateFactureEnd   = 
22/07/2025 : 12:05:26.557 :             Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:08:00.132 : Entering
22/07/2025 : 12:08:00.134 :     Client           = 
22/07/2025 : 12:08:00.135 :     DateFactureStart = 
22/07/2025 : 12:08:00.135 :     DateFactureEnd   = 
22/07/2025 : 12:08:15.573 :     Count = 8832132
22/07/2025 : 12:08:15.574 : Leaving
22/07/2025 : 12:08:16.129 : Entering
22/07/2025 : 12:08:16.130 :     Client           = 
22/07/2025 : 12:08:16.130 :     DateFactureStart = 
22/07/2025 : 12:08:16.130 :     DateFactureEnd   = 
22/07/2025 : 12:08:22.215 :     Count = 8832132
22/07/2025 : 12:08:22.215 : Leaving
22/07/2025 : 12:08:38.411 : Entering
22/07/2025 : 12:08:38.411 :     Client           = 
22/07/2025 : 12:08:38.413 :     DateFactureStart = 22/07/2025 00:00:00
22/07/2025 : 12:08:38.413 :     DateFactureEnd   = 11/07/2019 00:00:00
22/07/2025 : 12:08:45.726 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
22/07/2025 : 12:08:57.245 :     Entering
22/07/2025 : 12:08:57.246 :         Client           = 
22/07/2025 : 12:08:57.246 :         DateFactureStart = 22/07/2000 00:00:00
22/07/2025 : 12:08:57.247 :         DateFactureEnd   = 11/07/2019 00:00:00
22/07/2025 : 12:09:28.264 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 12:10:17.159 :         Entering
22/07/2025 : 12:10:17.160 :             Client           = 
22/07/2025 : 12:10:17.160 :             DateFactureStart = 22/07/2000 00:00:00
22/07/2025 : 12:10:17.160 :             DateFactureEnd   = 11/07/2019 00:00:00
22/07/2025 : 12:10:47.715 :             Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 12:16:31.005 : Entering
22/07/2025 : 12:16:31.007 :     Client           = 
22/07/2025 : 12:16:31.008 :     DateFactureStart = 
22/07/2025 : 12:16:31.008 :     DateFactureEnd   = 
22/07/2025 : 12:16:34.890 :     Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:16:35.146 :     Entering
22/07/2025 : 12:16:35.146 :         Client           = 
22/07/2025 : 12:16:35.147 :         DateFactureStart = 
22/07/2025 : 12:16:35.147 :         DateFactureEnd   = 
22/07/2025 : 12:16:35.884 :         Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:17:48.034 :         Entering
22/07/2025 : 12:17:48.035 :             Client           = 
22/07/2025 : 12:17:48.036 :             DateFactureStart = 
22/07/2025 : 12:17:48.036 :             DateFactureEnd   = 
22/07/2025 : 12:17:48.621 :             Unable to cast object of type 'System.String' to type 'System.DateTime'.
22/07/2025 : 12:26:13.113 : Entering
22/07/2025 : 12:26:13.115 :     Client           = 
22/07/2025 : 12:26:13.116 :     DateFactureStart = 
22/07/2025 : 12:26:13.116 :     DateFactureEnd   = 
22/07/2025 : 12:26:24.065 :     Count = 8832132
22/07/2025 : 12:26:24.066 : Leaving
22/07/2025 : 12:26:25.179 : Entering
22/07/2025 : 12:26:25.179 :     Client           = 
22/07/2025 : 12:26:25.179 :     DateFactureStart = 
22/07/2025 : 12:26:25.180 :     DateFactureEnd   = 
22/07/2025 : 12:26:29.677 :     Count = 8832132
22/07/2025 : 12:26:29.677 : Leaving
22/07/2025 : 12:26:29.743 : Entering
22/07/2025 : 12:26:29.744 :     Client           = 
22/07/2025 : 12:26:29.744 :     DateFactureStart = 
22/07/2025 : 12:26:29.744 :     DateFactureEnd   = 
22/07/2025 : 12:26:33.338 :     Count = 8832132
22/07/2025 : 12:26:33.339 : Leaving
22/07/2025 : 12:26:54.431 : Entering
22/07/2025 : 12:26:54.431 :     Client           = 
22/07/2025 : 12:26:54.433 :     DateFactureStart = 
22/07/2025 : 12:26:54.434 :     DateFactureEnd   = 
22/07/2025 : 12:26:57.592 :     Count = 8832132
22/07/2025 : 12:26:57.593 : Leaving
22/07/2025 : 12:27:10.851 : Entering
22/07/2025 : 12:27:10.852 :     Client           = 
22/07/2025 : 12:27:10.853 :     DateFactureStart = 
22/07/2025 : 12:27:10.854 :     DateFactureEnd   = 
22/07/2025 : 12:27:42.522 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 12:27:42.539 :     Entering
22/07/2025 : 12:27:42.539 :         Client           = 
22/07/2025 : 12:27:42.541 :         DateFactureStart = 
22/07/2025 : 12:27:42.542 :         DateFactureEnd   = 
22/07/2025 : 14:11:57.900 : Entering
22/07/2025 : 14:11:57.903 :     Client           = 
22/07/2025 : 14:11:57.905 :     DateFactureStart = 
22/07/2025 : 14:11:57.905 :     DateFactureEnd   = 
22/07/2025 : 14:12:08.468 :     Count = 8832132
22/07/2025 : 14:12:08.469 : Leaving
22/07/2025 : 14:12:09.234 : Entering
22/07/2025 : 14:12:09.234 :     Client           = 
22/07/2025 : 14:12:09.235 :     DateFactureStart = 
22/07/2025 : 14:12:09.235 :     DateFactureEnd   = 
22/07/2025 : 14:12:12.493 :     Count = 8832132
22/07/2025 : 14:12:12.494 : Leaving
22/07/2025 : 14:12:17.300 : Entering
22/07/2025 : 14:12:17.300 :     Client           = 
22/07/2025 : 14:12:17.302 :     DateFactureStart = 
22/07/2025 : 14:12:17.303 :     DateFactureEnd   = 
22/07/2025 : 14:12:48.344 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
22/07/2025 : 14:12:48.376 :     Entering
22/07/2025 : 14:12:48.377 :         Client           = 
22/07/2025 : 14:12:48.379 :         DateFactureStart = 
22/07/2025 : 14:12:48.380 :         DateFactureEnd   = 
22/07/2025 : 14:13:09.017 :         Count = 8832132
22/07/2025 : 14:13:09.018 :     Leaving
