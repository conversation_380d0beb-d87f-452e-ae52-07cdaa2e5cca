@page "/ContratLD"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using CarSystemsWebApp.Services
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection
@using Syncfusion.Drawing

@inject IJSRuntime      _jsRuntime
@inject SfDialogService _dialogService
@inject PageTitleService PageTitleService

<!-- ============== -->
<!-- PROFESSIONAL FILTER BAR -->
<!-- ============== -->

<div class="professional-filter-container">
    <div class="filter-header">
        <div class="filter-title">Filtres</div>
        <button class="btn-clear-all" @onclick="ClearAllFilters">Effacer tout</button>
    </div>

    <div class="row filter-row align-items-center">
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedClient" ShowClearButton="true" Placeholder="Client..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfTextBox @bind-Value="@SelectedParc" ShowClearButton="true" Placeholder="Parc..." CssClass="compact-input-text"></SfTextBox>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date début contrat..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDateDebutContrat"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfDatePicker TValue="DateTime?"
                         Placeholder="Date restitution..."
                         ShowClearButton="true"
                         @bind-Value="@SelectedDaterestitution"
                         Width="240px"></SfDatePicker>
        </div>
        <div class="col-auto">
            <SfButton Type="button" @onclick="Helper_Refresh" CssClass="btn-inline">
                Appliquer
            </SfButton>
        </div>
        <div class="col-auto">
            <SfButton OnClick="OnButtonExport" CssClass="btn-inline-export">
                Export
            </SfButton>
        </div>
    </div>
</div>



@*  *@

@* style="height: calc(100vh - 205px) !important;" *@

<div>
    <SfGrid ID="MyGrid"
    TValue="CS_LDANX_ITEM"
    @ref="@MyGrid"
    Query="@MyQuery"
    AllowPaging="true"
    AllowFiltering="false"
    AllowSorting="true"
    AllowResizing="true" EnablePersistence="true"
    ShowColumnChooser="true" Toolbar=@ToolbarItems
    EnableInfiniteScrolling="false"
    Height="100%">
        <GridEvents TValue="CS_LDANX_ITEM"
        RowSelected="OnRowSelected" 
        OnDataBound="DataBoundHandler"
        OnActionBegin="OnActionBegin"
        OnActionComplete="OnActionComplete"/>
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="22" />
        <GridColumns>
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codesociete) HeaderText="codesociete" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numldct) Visible="false" ShowInColumnChooser="true" HeaderText="numldct" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeclient) HeaderText="codeclient" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numldanx)   Visible="false" ShowInColumnChooser="true" HeaderText="numldanx" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeparc) HeaderText="codeparc" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.parcattente)  Visible="false" ShowInColumnChooser="true"  HeaderText="parcattente" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codegenre) Visible="false" ShowInColumnChooser="true" HeaderText="codegenre" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeldcfac)  Visible="false" ShowInColumnChooser="true" HeaderText="codeldcfac" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datedeb2) HeaderText="datedeb" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datefin) HeaderText="datefin" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.duree)  Visible="false" ShowInColumnChooser="true"  HeaderText="duree" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.mutation) Visible="false" ShowInColumnChooser="true" HeaderText="mutation" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeadresse) Visible="false" ShowInColumnChooser="true" HeaderText="codeadresse" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codelieu) HeaderText="codelieu" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeagence) HeaderText="codeagence" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.commercial) Visible="false" ShowInColumnChooser="true" HeaderText="commercial" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codefourniinter) Visible="false" ShowInColumnChooser="true" HeaderText="codefourniinter" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeconduct) Visible="false" ShowInColumnChooser="true"  HeaderText="codeconduct" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numdevis) HeaderText="numdevis" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.depotgarantie)  Visible="false" ShowInColumnChooser="true" HeaderText="depotgarantie" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.rachatcli) Visible="false" ShowInColumnChooser="true" HeaderText="rachatcli" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.relais)  Visible="false" ShowInColumnChooser="true" HeaderText="relais" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.typerelais) Visible="false" ShowInColumnChooser="true" HeaderText="typerelais" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.nbjrelais) Visible="false" ShowInColumnChooser="true" HeaderText="nbjrelais" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datelivraison) HeaderText="datelivraison" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.relcptdeb) Visible="false" ShowInColumnChooser="true" HeaderText="relcptdeb" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.daterelcptdeb) Visible="false" ShowInColumnChooser="true" HeaderText="daterelcptdeb" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datearretfac) Visible="false" ShowInColumnChooser="true" HeaderText="datearretfac" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.daterestitution2) HeaderText="daterestitution" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.nummvt) HeaderText="nummvt" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.typefaccpt)  Visible="false" ShowInColumnChooser="true" HeaderText="typefaccpt" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.pervalcpt)  Visible="false" ShowInColumnChooser="true" HeaderText="pervalcpt" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.moisreffaccpt)   Visible="false" ShowInColumnChooser="true" HeaderText="moisreffaccpt" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.mois1faccpt)  Visible="false" ShowInColumnChooser="true" HeaderText="mois1faccpt" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.tranchecpt1) Visible="false" ShowInColumnChooser="true" HeaderText="tranchecpt1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.tranchecpt2) Visible="false" ShowInColumnChooser="true" HeaderText="tranchecpt2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codegroupecompens) Visible="false" ShowInColumnChooser="true" HeaderText="codegroupecompens" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeetat) Visible="false" ShowInColumnChooser="true" HeaderText="codeetat" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datefinderperfac) HeaderText="datefinderperfac" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.derreltraite) Visible="false" ShowInColumnChooser="true" HeaderText="derreltraite" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederreltraite) Visible="false" ShowInColumnChooser="true"  HeaderText="datederreltraite" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.derreltraiteestime)   Visible="false" ShowInColumnChooser="true" HeaderText="derreltraiteestime" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.derrelmini) Visible="false" ShowInColumnChooser="true" HeaderText="derrelmini" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederrelmini) HeaderText="datederrelmini" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.derrelpk) Visible="false" ShowInColumnChooser="true" HeaderText="derrelpk" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederrelpk) Visible="false" ShowInColumnChooser="true" HeaderText="datederrelpk" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.grpanx) Visible="false" ShowInColumnChooser="true" HeaderText="grpanx" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.tri) Visible="false" ShowInColumnChooser="true" HeaderText="tri" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.classement) Visible="false" ShowInColumnChooser="true" HeaderText="classement" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.cumulcptfacauto) Visible="false" ShowInColumnChooser="true" HeaderText="cumulcptfacauto" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.cumulcptfacman) Visible="false" ShowInColumnChooser="true" HeaderText="cumulcptfacman" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.cumulrelaismini) Visible="false" ShowInColumnChooser="true" HeaderText="cumulrelaismini" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.cumulcptrelais) Visible="false" ShowInColumnChooser="true" HeaderText="cumulcptrelais" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numfacbase) Visible="false" ShowInColumnChooser="true" HeaderText="numfacbase" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numfacregul) Visible="false" ShowInColumnChooser="true" HeaderText="numfacregul" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numfacexcep) Visible="false" ShowInColumnChooser="true" HeaderText="numfacexcep" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numfaccompens) Visible="false" ShowInColumnChooser="true" HeaderText="numfaccompens" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.moisencours) Visible="false" ShowInColumnChooser="true" HeaderText="moisencours" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.bloquee) Visible="false" ShowInColumnChooser="true" HeaderText="bloquee" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datefinblocage)  Visible="false" ShowInColumnChooser="true" HeaderText="datefinblocage" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.deblocauto) Visible="false" ShowInColumnChooser="true" HeaderText="deblocauto" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datecrea) Visible="false" ShowInColumnChooser="true" HeaderText="datecrea" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datemodi) Visible="false" ShowInColumnChooser="true"  HeaderText="datemodi" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeutiliscrea) Visible="false" ShowInColumnChooser="true" HeaderText="codeutiliscrea" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeutilismodi) Visible="false" ShowInColumnChooser="true" HeaderText="codeutilismodi" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zalp1) Visible="false" ShowInColumnChooser="true" HeaderText="zalp1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zalp2) Visible="false" ShowInColumnChooser="true"  HeaderText="zalp2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zalp3)  Visible="false" ShowInColumnChooser="true" HeaderText="zalp3" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zdat1)  Visible="false" ShowInColumnChooser="true" HeaderText="zdat1" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zdat2)   Visible="false" ShowInColumnChooser="true" HeaderText="zdat2" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zdat3) Visible="false" ShowInColumnChooser="true" HeaderText="zdat3" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.znum1) Visible="false" ShowInColumnChooser="true" HeaderText="znum1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.znum2) Visible="false" ShowInColumnChooser="true" HeaderText="znum2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.znum3) Visible="false" ShowInColumnChooser="true" HeaderText="znum3" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zonecle) Visible="false" ShowInColumnChooser="true" HeaderText="zonecle" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.indexee) Visible="false" ShowInColumnChooser="true" HeaderText="indexee" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.clifou) Visible="false" ShowInColumnChooser="true" HeaderText="clifou" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.daterelestime) Visible="false" ShowInColumnChooser="true" HeaderText="daterelestime" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.relestime) Visible="false" ShowInColumnChooser="true" HeaderText="relestime" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.dureeorigine)  Visible="false" ShowInColumnChooser="true"  HeaderText="dureeorigine" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeadresseexploit) Visible="false" ShowInColumnChooser="true" HeaderText="codeadresseexploit" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.depotgarantieeuro) Visible="false" ShowInColumnChooser="true" HeaderText="depotgarantieeuro" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.depotgarantiepays) Visible="false" ShowInColumnChooser="true" HeaderText="depotgarantiepays" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.deviserefeuro) Visible="false" ShowInColumnChooser="true" HeaderText="deviserefeuro" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.devisesignature) Visible="false" ShowInColumnChooser="true" HeaderText="devisesignature" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30alp1)  Visible="false" ShowInColumnChooser="true" HeaderText="z30alp1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30alp2) Visible="false" ShowInColumnChooser="true" HeaderText="z30alp2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30alp3) Visible="false" ShowInColumnChooser="true" HeaderText="z30alp3" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30alp4) Visible="false" ShowInColumnChooser="true" HeaderText="z30alp4" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30alp5) Visible="false" ShowInColumnChooser="true" HeaderText="z30alp5" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30dat1) Visible="false" ShowInColumnChooser="true" HeaderText="z30dat1" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30dat2) Visible="false" ShowInColumnChooser="true" HeaderText="z30dat2" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30dat3) Visible="false" ShowInColumnChooser="true" HeaderText="z30dat3" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont1) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont1euro) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont1euro" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont1pays) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont1pays" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont2) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont2euro) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont2euro" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont2pays) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont2pays" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont3) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont3" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont3euro) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont3euro" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30mont3pays) Visible="false" ShowInColumnChooser="true" HeaderText="z30mont3pays" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30num1) Visible="false" ShowInColumnChooser="true" HeaderText="z30num1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30num2) Visible="false" ShowInColumnChooser="true" HeaderText="z30num2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z30num3) Visible="false" ShowInColumnChooser="true" HeaderText="z30num3" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zone30cle1) Visible="false" ShowInColumnChooser="true" HeaderText="zone30cle1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.zone30cle2) Visible="false" ShowInColumnChooser="true" HeaderText="zone30cle2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeldtype) Visible="false" ShowInColumnChooser="true" HeaderText="codeldtype" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.typeldanx) Visible="false" ShowInColumnChooser="true" HeaderText="typeldanx" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codemodele) Visible="false" ShowInColumnChooser="true" HeaderText="codemodele" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.grpmodele) Visible="false" ShowInColumnChooser="true" HeaderText="grpmodele" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.parcencmd) Visible="false" ShowInColumnChooser="true" HeaderText="parcencmd" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.exclusif) Visible="false" ShowInColumnChooser="true" HeaderText="exclusif" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.permanent) Visible="false" ShowInColumnChooser="true" HeaderText="permanent" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.calensem) Visible="false" ShowInColumnChooser="true" HeaderText="calensem" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.tranchecpt3) Visible="false" ShowInColumnChooser="true" HeaderText="tranchecpt3" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.chauffeur) Visible="false" ShowInColumnChooser="true" HeaderText="chauffeur" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.kmcontrat) Visible="false" ShowInColumnChooser="true" HeaderText="kmcontrat" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.supplementaire) Visible="false" ShowInColumnChooser="true" HeaderText="supplementaire" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.recondauto) Visible="false" ShowInColumnChooser="true" HeaderText="recondauto" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.dureerecond) Visible="false" ShowInColumnChooser="true" HeaderText="dureerecond" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.cautiondepot) Visible="false" ShowInColumnChooser="true" HeaderText="cautiondepot" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.dateechcaution) Visible="false" ShowInColumnChooser="true" HeaderText="dateechcaution" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.refcaution) Visible="false" ShowInColumnChooser="true" HeaderText="refcaution" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.recondcaution) Visible="false" ShowInColumnChooser="true" HeaderText="recondcaution" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeagenceaffec) Visible="false" ShowInColumnChooser="true" HeaderText="codeagenceaffec" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.assurecli) Visible="false" ShowInColumnChooser="true" HeaderText="assurecli" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.achatvign) Visible="false" ShowInColumnChooser="true" HeaderText="achatvign" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.nbserv)  Visible="false" ShowInColumnChooser="true" HeaderText="nbserv" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.carburant) Visible="false" ShowInColumnChooser="true" HeaderText="carburant" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.reference) Visible="false" ShowInColumnChooser="true" HeaderText="reference" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeparcprevu) Visible="false" ShowInColumnChooser="true" HeaderText="codeparcprevu" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.preavis) Visible="false" ShowInColumnChooser="true" HeaderText="preavis" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codesocietefacbase) Visible="false" ShowInColumnChooser="true" HeaderText="codesocietefacbase" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codesocietefacregul) Visible="false" ShowInColumnChooser="true" HeaderText="codesocietefacregul" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codesocietefaccompens) Visible="false" ShowInColumnChooser="true" HeaderText="codesocietefaccompens" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codesocietefacexcep) Visible="false" ShowInColumnChooser="true" HeaderText="codesocietefacexcep" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.moisfacbase) Visible="false" ShowInColumnChooser="true" HeaderText="moisfacbase" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.moisfacregul) Visible="false" ShowInColumnChooser="true" HeaderText="moisfacregul" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.moisfacexcep) Visible="false" ShowInColumnChooser="true" HeaderText="moisfacexcep" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.moisfaccompens) Visible="false" ShowInColumnChooser="true" HeaderText="moisfaccompens" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.cumuldeduittraite) Visible="false" ShowInColumnChooser="true" HeaderText="cumuldeduittraite" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.transfexploit) Visible="false" ShowInColumnChooser="true" HeaderText="transfexploit" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.typetiers) Visible="false" ShowInColumnChooser="true" HeaderText="typetiers" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.nature) Visible="false" ShowInColumnChooser="true" HeaderText="nature" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.derrelsaisi) Visible="false" ShowInColumnChooser="true" HeaderText="derrelsaisi" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederrelsaisi) Visible="false" ShowInColumnChooser="true" HeaderText="datederrelsaisi" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.heuredeb) HeaderText="heuredeb" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.heurefin) HeaderText="heurefin" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codesocietefackmfdm) Visible="false" ShowInColumnChooser="true" HeaderText="codesocietefackmfdm" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numfackmfdm) Visible="false" ShowInColumnChooser="true" HeaderText="numfackmfdm" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.moisfackmfdm) Visible="false" ShowInColumnChooser="true" HeaderText="moisfackmfdm" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederrelsaisianx) Visible="false" ShowInColumnChooser="true" HeaderText="datederrelsaisianx" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederreltraiteanx) HeaderText="datederreltraiteanx" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.derrelprec) Visible="false" ShowInColumnChooser="true" HeaderText="derrelprec" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederrelprec) Visible="false" ShowInColumnChooser="true" HeaderText="datederrelprec" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.dureeanxorig) Visible="false" ShowInColumnChooser="true" HeaderText="dureeanxorig" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.codeclientgrpcompens) Visible="false" ShowInColumnChooser="true" HeaderText="codeclientgrpcompens" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.assurld) Visible="false" ShowInColumnChooser="true" HeaderText="assurld" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.assurldanx) Visible="false" ShowInColumnChooser="true" HeaderText="assurldanx" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datedebassclisoc) Visible="false" ShowInColumnChooser="true" HeaderText="datedebassclisoc" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.dateechassur) Visible="false" ShowInColumnChooser="true" HeaderText="dateechassur" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.datederchgassurld) Visible="false" ShowInColumnChooser="true" HeaderText="datederchgassurld" Format="dd/MM/yyyy" Type=ColumnType.DateOnly />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.montfranchise) Visible="false" ShowInColumnChooser="true" HeaderText="montfranchise" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.montfranchiseeuro) Visible="false" ShowInColumnChooser="true" HeaderText="montfranchiseeuro" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.montfranchisepays) Visible="false" ShowInColumnChooser="true" HeaderText="montfranchisepays" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.nbfranchgratuite) Visible="false" ShowInColumnChooser="true" HeaderText="nbfranchgratuite" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.trtautorefacfranchise) Visible="false" ShowInColumnChooser="true" HeaderText="trtautorefacfranchise" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.reportunitforf) Visible="false" ShowInColumnChooser="true" HeaderText="reportunitforf" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.grpsoc) Visible="false" ShowInColumnChooser="true" HeaderText="grpsoc" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.derreltraiteestim) Visible="false" ShowInColumnChooser="true" HeaderText="derreltraiteestim" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.refanxorig) Visible="false" ShowInColumnChooser="true" HeaderText="refanxorig" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.refassur) Visible="false" ShowInColumnChooser="true" HeaderText="refassur" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.numldctv) Visible="false" ShowInColumnChooser="true" HeaderText="numldctv" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.extractDW) Visible="false" ShowInColumnChooser="true" HeaderText="extractDW" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z40alp1) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp1" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z40alp2) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp2" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z40alp3) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp3" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z40alp4) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp4" />
            <GridColumn Field=@nameof(CS_LDANX_ITEM.z40alp5) Visible="false" ShowInColumnChooser="true" HeaderText="z40alp5" />
        </GridColumns>
    </SfGrid>
</div>

<script type="text/javascript">

    function saveAsFile(filename, bytesBase64) {
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++) {
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_LDANX_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;

    private string      SelectedClient;
    private DateTime?   SelectedDateDebutContrat;
    private DateTime?   SelectedDaterestitution;
    private string      SelectedParc;


    protected override void OnInitialized()
    {
        // Set page title
        PageTitleService.SetTitle("Contrats LD");

        this.MyQuery = new Query()
            .AddParams("Client", null)
            .AddParams("DateDebutContrat", null)
            .AddParams("DateRestitution", null)
            .AddParams("Parc", null);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_LDANX_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_LDANX_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_LDANX_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_LDANX_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["Client"]            = this.SelectedClient;
        MyQuery.Queries.Params["DateDebutContrat"]  = this.SelectedDateDebutContrat;
        MyQuery.Queries.Params["DateRestitution"]   = this.SelectedDaterestitution;
        MyQuery.Queries.Params["Parc"]              = this.SelectedParc;

        // Reset pagination to first page when applying new filters
        await MyGrid.GoToPageAsync(1);
        await MyGrid.Refresh();
    }

    private async Task ClearAllFilters()
    {
        SelectedClient = null;
        SelectedDateDebutContrat = null;
        SelectedDaterestitution = null;
        SelectedParc = null;
        await Helper_Refresh();
    }

    private async void OnButtonExport()
    {
        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // Create a workbook with 1 worksheet
            IWorkbook workbook = application.Workbooks.Create(1);
            IWorksheet worksheet = workbook.Worksheets[0];
            worksheet.Name = "CONTRATS_LD";

            // Get data for export (large limit to get all data)
            string sortcolumn = "code_societe";
            string sortdir = "ascending";

            var items = MyCustomAdaptor.Helper_GetData(
                this.SelectedDateDebutContrat,
                this.SelectedDaterestitution,
                this.SelectedClient,
                this.SelectedParc,
                0, 100000,
                sortcolumn, sortdir, 900); // 15 minute timeout for export

            // Create a professional header design with WafaLLD orange branding
            // Main title with orange background
            worksheet["A1"].Text = "EXPORT CONTRATS LD";
            worksheet["A1"].CellStyle.Font.Bold = true;
            worksheet["A1"].CellStyle.Font.Size = 16;
            worksheet["A1"].CellStyle.Font.Color = ExcelKnownColors.White;
            worksheet["A1"].CellStyle.Color = Color.FromArgb(255, 140, 0); // WafaLLD Orange background
            worksheet["A1"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            worksheet["A1"].CellStyle.VerticalAlignment = ExcelVAlign.VAlignCenter;
            worksheet.Range["A1:R1"].Merge();
            worksheet.Range["A1:R1"].RowHeight = 25;

            // Export info with light orange background
            worksheet["A2"].Text = $"Exporté le: {DateTime.Now:dd/MM/yyyy HH:mm}";
            worksheet["A2"].CellStyle.Font.Size = 10;
            worksheet["A2"].CellStyle.Font.Italic = true;
            worksheet["A2"].CellStyle.Color = Color.FromArgb(255, 228, 196); // Light orange background
            worksheet["A2"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A2:R2"].Merge();

            // Add filter criteria info
            string filterInfo = "Filtres appliqués: ";
            if (SelectedDateDebutContrat.HasValue)
            {
                filterInfo += $"Date Début: {SelectedDateDebutContrat?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (SelectedDaterestitution.HasValue)
            {
                filterInfo += $"Date Restitution: {SelectedDaterestitution?.ToString("dd/MM/yyyy") ?? "..."} | ";
            }
            if (!string.IsNullOrEmpty(SelectedClient))
            {
                filterInfo += $"Client: {SelectedClient} | ";
            }
            if (!string.IsNullOrEmpty(SelectedParc))
            {
                filterInfo += $"Parc: {SelectedParc} | ";
            }
            if (filterInfo.EndsWith(" | "))
            {
                filterInfo = filterInfo.Substring(0, filterInfo.Length - 3);
            }
            else if (filterInfo == "Filtres appliqués: ")
            {
                filterInfo = "Aucun filtre appliqué";
            }

            worksheet["A3"].Text = filterInfo;
            worksheet["A3"].CellStyle.Font.Size = 9;
            worksheet["A3"].CellStyle.Color = Color.FromArgb(255, 248, 240); // Very light orange/cream
            worksheet["A3"].CellStyle.HorizontalAlignment = ExcelHAlign.HAlignLeft;
            worksheet.Range["A3:R3"].Merge();

            // Add a separator line with orange accent
            worksheet.Range["A4:R4"].CellStyle.Color = Color.FromArgb(255, 165, 0); // Orange accent line
            worksheet.Range["A4:R4"].RowHeight = 3;

            // Convert to DataTable for Excel export
            DataTable table = new DataTable();
            Type type = typeof(CS_LDANX_ITEM);
            PropertyInfo[] properties = type.GetProperties();

            // Create columns
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);

            // Add data rows
            foreach (var item in items)
            {
                DataRow row = table.NewRow();
                int k = 0;
                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }
                table.Rows.Add(row);
            }

            // Export data to Excel worksheet starting from row 6 to leave space for header design
            worksheet.ImportDataTable(table, true, 6, 1);

            // Style the data header row (column names) with orange theme
            IRange headerRange = worksheet.Range[6, 1, 6, properties.Length];
            headerRange.CellStyle.Font.Bold = true;
            headerRange.CellStyle.Font.Color = ExcelKnownColors.White;
            headerRange.CellStyle.Color = Color.FromArgb(255, 165, 0); // Medium orange background
            headerRange.CellStyle.HorizontalAlignment = ExcelHAlign.HAlignCenter;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
            headerRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
            headerRange.RowHeight = 20;

            // Add alternating row colors and borders to all data
            if (items.Count > 0)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    int rowIndex = 7 + i; // Start from row 7 (after header)
                    IRange rowRange = worksheet.Range[rowIndex, 1, rowIndex, properties.Length];

                    // Alternating row colors with orange theme
                    if (i % 2 == 0)
                    {
                        rowRange.CellStyle.Color = Color.FromArgb(255, 250, 240); // Very light orange/cream
                    }
                    else
                    {
                        rowRange.CellStyle.Color = Color.White;
                    }

                    // Add borders
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeTop].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeBottom].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeLeft].LineStyle = ExcelLineStyle.Thin;
                    rowRange.CellStyle.Borders[ExcelBordersIndex.EdgeRight].LineStyle = ExcelLineStyle.Thin;
                }
            }

            worksheet.UsedRange.AutofitColumns();

            // Save and download
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                string filename = $"ExportContratsLD_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "CONTRATLD-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                List<CS_LDANX_ITEM> empty;
                empty = Enumerable.Empty<CS_LDANX_ITEM>().ToList();

                if (dm.Params is null || dm.Params.Count == 0)
                {
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Nothing to do", true);
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                    return dm.RequiresCounts ? new DataResult() { Result = empty, Count = 0 } : (object)empty;
                }

                var client              = dm.Params["Client"] as string;
                var dateDebutContrat    = dm.Params["DateDebutContrat"] as DateTime?;
                var dateRestitution     = dm.Params["DateRestitution"] as DateTime?;
                var parc                = dm.Params["Parc"] as string;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Client           = {client}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateDebutContrat = {dateDebutContrat}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"DateRestitution  = {dateRestitution}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Parc             = {parc}", true);

                // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
                object dateDebutParam = dateDebutContrat.HasValue ? (object)dateDebutContrat.Value.ToString("yyyy-MM-dd") : DBNull.Value;
                object dateRestitutionParam = dateRestitution.HasValue ? (object)dateRestitution.Value.ToString("yyyy-MM-dd") : DBNull.Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateDebutParam = {dateDebutParam}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateRestitutionParam = {dateRestitutionParam}", true);

                // string sortcolumn   = "code_societe";  // OLD: Hardcoded sorting
                // string sortdir      = "ascending";     // OLD: Hardcoded sorting

                // NEW: Dynamic sorting from DataManagerRequest
                string sortcolumn   = "codesociete";  // Default sort column
                string sortdir      = "ascending";  // Default sort direction

                // Extract sorting information from DataManagerRequest
                if (dm.Sorted != null && dm.Sorted.Count > 0)
                {
                    var sortInfo = dm.Sorted[0]; // Get the first sort column
                    sortcolumn = sortInfo.Name;
                    sortdir = sortInfo.Direction;
                }

                CarSystemsContext model;
                model = new CarSystemsContext();

                var items = Helper_GetData(dateDebutContrat, dateRestitution, client, parc, dm.Skip, dm.Take, sortcolumn, sortdir);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetContratLDItemsCount_V2 {0}, {1}, {2}, {3} ",
                        client,
                        dateDebutParam,
                        dateRestitutionParam,
                        parc)
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);
                return null;
            }
        }

        public static List<CS_LDANX_ITEM> Helper_GetData(DateTime? dateDebutContrat, DateTime? daterestitution, string client, string parc, int skip, int take, string sortcolumn, string sortdir, int timeoutSeconds = 30)
        {
            var logKey = "CONTRATLD-HELPER_GETDATA";
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateDebutContrat = {dateDebutContrat}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"daterestitution = {daterestitution}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"client = {client}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"parc = {parc}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortcolumn = {sortcolumn}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"sortdir = {sortdir}", true);

            CarSystemsContext model;
            model = new CarSystemsContext();

            // Set command timeout for long-running operations
            model.Database.SetCommandTimeout(timeoutSeconds);

            // Pass dates as strings in the exact format used in SQL Server console: '2023-03-01'
            object dateDebutParam = dateDebutContrat.HasValue ? (object)dateDebutContrat.Value.ToString("yyyy-MM-dd") : DBNull.Value;
            object dateRestitutionParam = daterestitution.HasValue ? (object)daterestitution.Value.ToString("yyyy-MM-dd") : DBNull.Value;

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateDebutParam = {dateDebutParam}", true);
            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Formatted dateRestitutionParam = {dateRestitutionParam}", true);

            var items = model.CS_LDANX_ITEMS.FromSqlRaw("EXECUTE dbo.GetContratLDItems_V2 {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}",
                client,
                dateDebutParam,
                dateRestitutionParam,
                parc,
                skip,
                take,
                sortcolumn,
                sortdir).ToList();

            CarSystemsWebApp.Utils.Helper_Trace(logKey, $"Retrieved {items.Count} items", true);
            return items;
        }
    }
}
