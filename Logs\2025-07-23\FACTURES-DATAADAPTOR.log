23/07/2025 : 08:46:05.566 : Entering
23/07/2025 : 08:46:05.572 :     dateMinFacture = 
23/07/2025 : 08:46:05.573 :     dateMaxFacture = 
23/07/2025 : 08:46:17.362 :     Count = 8832132
23/07/2025 : 08:46:17.364 : Leaving
23/07/2025 : 08:46:18.098 : Entering
23/07/2025 : 08:46:18.099 :     dateMinFacture = 
23/07/2025 : 08:46:18.099 :     dateMaxFacture = 
23/07/2025 : 08:46:20.758 :     Count = 8832132
23/07/2025 : 08:46:20.759 : Leaving
23/07/2025 : 08:47:05.670 : Entering
23/07/2025 : 08:47:05.670 :     dateMinFacture = 
23/07/2025 : 08:47:05.671 :     dateMaxFacture = 
23/07/2025 : 10:25:41.700 : Entering
23/07/2025 : 10:25:41.702 :     dateMinFacture = 
23/07/2025 : 10:25:41.703 :     dateMaxFacture = 
23/07/2025 : 10:25:41.703 :     sortcolumn = numfac
23/07/2025 : 10:25:41.703 :     sortdir = ascending
23/07/2025 : 10:25:53.662 :     Count = 8832132
23/07/2025 : 10:25:53.662 : Leaving
23/07/2025 : 10:25:53.950 : Entering
23/07/2025 : 10:25:53.950 :     dateMinFacture = 
23/07/2025 : 10:25:53.950 :     dateMaxFacture = 
23/07/2025 : 10:25:53.950 :     sortcolumn = numfac
23/07/2025 : 10:25:53.951 :     sortdir = ascending
23/07/2025 : 10:25:57.445 :     Count = 8832132
23/07/2025 : 10:25:57.445 : Leaving
23/07/2025 : 10:27:26.555 : Entering
23/07/2025 : 10:27:26.556 :     dateMinFacture = 
23/07/2025 : 10:27:26.556 :     dateMaxFacture = 
23/07/2025 : 10:27:26.556 :     sortcolumn = montant
23/07/2025 : 10:27:26.556 :     sortdir = ascending
23/07/2025 : 10:27:57.398 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 10:27:57.436 :     Entering
23/07/2025 : 10:27:57.437 :         dateMinFacture = 
23/07/2025 : 10:27:57.437 :         dateMaxFacture = 
23/07/2025 : 10:27:57.437 :         sortcolumn = montant
23/07/2025 : 10:27:57.437 :         sortdir = descending
23/07/2025 : 10:28:29.328 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 10:28:38.712 :         Entering
23/07/2025 : 10:28:38.713 :             dateMinFacture = 
23/07/2025 : 10:28:38.716 :             dateMaxFacture = 
23/07/2025 : 10:28:38.718 :             sortcolumn = numfac
23/07/2025 : 10:28:38.719 :             sortdir = ascending
23/07/2025 : 10:28:43.473 :             Count = 8832132
23/07/2025 : 10:28:43.474 :         Leaving
23/07/2025 : 10:28:43.525 :         Entering
23/07/2025 : 10:28:43.526 :             dateMinFacture = 
23/07/2025 : 10:28:43.529 :             dateMaxFacture = 
23/07/2025 : 10:28:43.530 :             sortcolumn = montant
23/07/2025 : 10:28:43.531 :             sortdir = ascending
23/07/2025 : 10:29:09.051 :             Count = 8832132
23/07/2025 : 10:29:09.051 :         Leaving
23/07/2025 : 10:29:27.872 :         Entering
23/07/2025 : 10:29:27.873 :             dateMinFacture = 
23/07/2025 : 10:29:27.877 :             dateMaxFacture = 
23/07/2025 : 10:29:27.878 :             sortcolumn = datefac
23/07/2025 : 10:29:27.879 :             sortdir = ascending
23/07/2025 : 10:29:42.887 :             Count = 8832132
23/07/2025 : 10:29:42.888 :         Leaving
23/07/2025 : 10:29:42.910 :         Entering
23/07/2025 : 10:29:42.910 :             dateMinFacture = 
23/07/2025 : 10:29:42.912 :             dateMaxFacture = 
23/07/2025 : 10:29:42.915 :             sortcolumn = datefac
23/07/2025 : 10:29:42.915 :             sortdir = descending
23/07/2025 : 10:29:59.079 :             Count = 8832132
23/07/2025 : 10:29:59.080 :         Leaving
23/07/2025 : 10:30:08.774 :         Entering
23/07/2025 : 10:30:08.775 :             dateMinFacture = 
23/07/2025 : 10:30:08.778 :             dateMaxFacture = 
23/07/2025 : 10:30:08.780 :             sortcolumn = datefac
23/07/2025 : 10:30:08.781 :             sortdir = descending
23/07/2025 : 10:30:13.397 :             Count = 55904
23/07/2025 : 10:30:13.398 :         Leaving
23/07/2025 : 10:30:55.655 :         Entering
23/07/2025 : 10:30:55.657 :             dateMinFacture = 23/06/2023 00:00:00
23/07/2025 : 10:30:55.657 :             dateMaxFacture = 
23/07/2025 : 10:30:55.658 :             sortcolumn = datefac
23/07/2025 : 10:30:55.659 :             sortdir = descending
23/07/2025 : 10:30:56.986 :             The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 10:39:21.183 : Entering
23/07/2025 : 10:39:21.186 :     dateMinFacture = 
23/07/2025 : 10:39:21.187 :     dateMaxFacture = 
23/07/2025 : 10:39:21.187 :     sortcolumn = numfac
23/07/2025 : 10:39:21.187 :     sortdir = ascending
23/07/2025 : 10:39:33.109 :     Count = 8832132
23/07/2025 : 10:39:33.110 : Leaving
23/07/2025 : 10:39:34.296 : Entering
23/07/2025 : 10:39:34.297 :     dateMinFacture = 
23/07/2025 : 10:39:34.298 :     dateMaxFacture = 
23/07/2025 : 10:39:34.299 :     sortcolumn = datefac
23/07/2025 : 10:39:34.300 :     sortdir = descending
23/07/2025 : 10:40:05.940 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 10:41:19.772 :     Entering
23/07/2025 : 10:41:19.773 :         dateMinFacture = 
23/07/2025 : 10:41:19.776 :         dateMaxFacture = 
23/07/2025 : 10:41:19.777 :         sortcolumn = datefac
23/07/2025 : 10:41:19.779 :         sortdir = descending
23/07/2025 : 10:41:40.741 :         Count = 8832132
23/07/2025 : 10:41:40.742 :     Leaving
23/07/2025 : 10:41:40.852 :     Entering
23/07/2025 : 10:41:40.852 :         dateMinFacture = 
23/07/2025 : 10:41:40.853 :         dateMaxFacture = 
23/07/2025 : 10:41:40.853 :         sortcolumn = datefac
23/07/2025 : 10:41:40.853 :         sortdir = descending
23/07/2025 : 10:41:43.430 :         Count = 55904
23/07/2025 : 10:41:43.431 :     Leaving
23/07/2025 : 10:41:51.059 :     Entering
23/07/2025 : 10:41:51.061 :         dateMinFacture = 
23/07/2025 : 10:41:51.064 :         dateMaxFacture = 
23/07/2025 : 10:41:51.065 :         sortcolumn = datefac
23/07/2025 : 10:41:51.067 :         sortdir = descending
23/07/2025 : 10:42:08.709 :         Count = 891896
23/07/2025 : 10:42:08.710 :     Leaving
23/07/2025 : 10:42:20.933 :     Entering
23/07/2025 : 10:42:20.934 :         dateMinFacture = 
23/07/2025 : 10:42:20.941 :         dateMaxFacture = 
23/07/2025 : 10:42:20.942 :         sortcolumn = numfac
23/07/2025 : 10:42:20.945 :         sortdir = ascending
23/07/2025 : 10:42:23.473 :         Count = 891896
23/07/2025 : 10:42:23.474 :     Leaving
23/07/2025 : 10:42:27.426 :     Entering
23/07/2025 : 10:42:27.427 :         dateMinFacture = 
23/07/2025 : 10:42:27.429 :         dateMaxFacture = 
23/07/2025 : 10:42:27.431 :         sortcolumn = datefac
23/07/2025 : 10:42:27.436 :         sortdir = ascending
23/07/2025 : 10:42:41.052 :         Count = 891896
23/07/2025 : 10:42:41.052 :     Leaving
23/07/2025 : 10:43:08.733 :     Entering
23/07/2025 : 10:43:08.734 :         dateMinFacture = 
23/07/2025 : 10:43:08.740 :         dateMaxFacture = 
23/07/2025 : 10:43:08.741 :         sortcolumn = datecrea
23/07/2025 : 10:43:08.743 :         sortdir = ascending
23/07/2025 : 10:43:12.064 :         Count = 891896
23/07/2025 : 10:43:12.065 :     Leaving
23/07/2025 : 10:43:22.905 :     Entering
23/07/2025 : 10:43:22.906 :         dateMinFacture = 
23/07/2025 : 10:43:22.909 :         dateMaxFacture = 
23/07/2025 : 10:43:22.910 :         sortcolumn = datecrea
23/07/2025 : 10:43:22.912 :         sortdir = descending
23/07/2025 : 10:43:25.569 :         Count = 891896
23/07/2025 : 10:43:25.570 :     Leaving
23/07/2025 : 10:43:25.601 :     Entering
23/07/2025 : 10:43:25.601 :         dateMinFacture = 
23/07/2025 : 10:43:25.604 :         dateMaxFacture = 
23/07/2025 : 10:43:25.608 :         sortcolumn = numfac
23/07/2025 : 10:43:25.608 :         sortdir = ascending
23/07/2025 : 10:43:28.038 :         Count = 891896
23/07/2025 : 10:43:28.039 :     Leaving
23/07/2025 : 10:43:43.468 :     Entering
23/07/2025 : 10:43:43.470 :         dateMinFacture = 25/05/2023 00:00:00
23/07/2025 : 10:43:43.471 :         dateMaxFacture = 
23/07/2025 : 10:43:43.472 :         sortcolumn = numfac
23/07/2025 : 10:43:43.478 :         sortdir = ascending
23/07/2025 : 10:44:05.023 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 11:38:41.958 : Entering
23/07/2025 : 11:38:41.961 :     dateMinFacture = 
23/07/2025 : 11:38:41.962 :     dateMaxFacture = 
23/07/2025 : 11:38:41.962 :     sortcolumn = numfac
23/07/2025 : 11:38:41.962 :     sortdir = ascending
23/07/2025 : 11:38:55.325 :     Count = 8832132
23/07/2025 : 11:38:55.325 : Leaving
23/07/2025 : 11:38:55.738 : Entering
23/07/2025 : 11:38:55.744 :     dateMinFacture = 
23/07/2025 : 11:38:55.744 :     dateMaxFacture = 
23/07/2025 : 11:38:55.745 :     sortcolumn = numfac
23/07/2025 : 11:38:55.745 :     sortdir = ascending
23/07/2025 : 11:39:01.385 :     Count = 8832132
23/07/2025 : 11:39:01.385 : Leaving
23/07/2025 : 11:39:15.415 : Entering
23/07/2025 : 11:39:15.416 :     dateMinFacture = 09/05/2023 00:00:00
23/07/2025 : 11:39:15.416 :     dateMaxFacture = 
23/07/2025 : 11:39:15.416 :     sortcolumn = numfac
23/07/2025 : 11:39:15.416 :     sortdir = ascending
23/07/2025 : 11:39:45.819 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 11:53:42.290 : Entering
23/07/2025 : 11:53:42.293 :     dateMinFacture = 
23/07/2025 : 11:53:42.293 :     dateMaxFacture = 
23/07/2025 : 11:53:42.294 :     sortcolumn = numfac
23/07/2025 : 11:53:42.294 :     sortdir = ascending
23/07/2025 : 11:53:49.295 :     Count = 8832132
23/07/2025 : 11:53:49.298 : Leaving
23/07/2025 : 11:53:49.743 : Entering
23/07/2025 : 11:53:49.743 :     dateMinFacture = 
23/07/2025 : 11:53:49.744 :     dateMaxFacture = 
23/07/2025 : 11:53:49.744 :     sortcolumn = numfac
23/07/2025 : 11:53:49.744 :     sortdir = ascending
23/07/2025 : 11:53:52.210 :     Count = 8832132
23/07/2025 : 11:53:52.210 : Leaving
23/07/2025 : 11:55:48.945 : Entering
23/07/2025 : 11:55:48.945 :     dateMinFacture = 
23/07/2025 : 11:55:48.945 :     dateMaxFacture = 
23/07/2025 : 11:55:48.945 :     sortcolumn = montant
23/07/2025 : 11:55:48.946 :     sortdir = ascending
23/07/2025 : 11:56:19.948 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 11:56:19.980 :     Entering
23/07/2025 : 11:56:19.980 :         dateMinFacture = 
23/07/2025 : 11:56:19.980 :         dateMaxFacture = 
23/07/2025 : 11:56:19.980 :         sortcolumn = montant
23/07/2025 : 11:56:19.981 :         sortdir = descending
23/07/2025 : 11:56:45.414 :         Count = 8832132
23/07/2025 : 11:56:45.415 :     Leaving
23/07/2025 : 11:56:59.793 :     Entering
23/07/2025 : 11:56:59.794 :         dateMinFacture = 02/07/2025 00:00:00
23/07/2025 : 11:56:59.796 :         dateMaxFacture = 
23/07/2025 : 11:56:59.796 :         sortcolumn = montant
23/07/2025 : 11:56:59.797 :         sortdir = descending
23/07/2025 : 11:57:05.988 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 12:06:11.486 : Entering
23/07/2025 : 12:06:11.491 :     dateMinFacture = 
23/07/2025 : 12:06:11.492 :     dateMaxFacture = 
23/07/2025 : 12:06:11.492 :     sortcolumn = numfac
23/07/2025 : 12:06:11.492 :     sortdir = ascending
23/07/2025 : 12:06:18.970 :     Count = 8832132
23/07/2025 : 12:06:18.972 : Leaving
23/07/2025 : 12:06:19.477 : Entering
23/07/2025 : 12:06:19.478 :     dateMinFacture = 
23/07/2025 : 12:06:19.478 :     dateMaxFacture = 
23/07/2025 : 12:06:19.478 :     sortcolumn = montant
23/07/2025 : 12:06:19.479 :     sortdir = descending
23/07/2025 : 12:06:38.475 :     Count = 8832132
23/07/2025 : 12:06:38.475 : Leaving
23/07/2025 : 12:07:12.177 : Entering
23/07/2025 : 12:07:12.178 :     dateMinFacture = 17/05/2023 00:00:00
23/07/2025 : 12:07:12.179 :     dateMaxFacture = 
23/07/2025 : 12:07:12.179 :     sortcolumn = montant
23/07/2025 : 12:07:12.179 :     sortdir = descending
23/07/2025 : 12:07:22.032 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 12:18:28.412 : Entering
23/07/2025 : 12:18:28.414 :     dateMinFacture = 
23/07/2025 : 12:18:28.415 :     dateMaxFacture = 
23/07/2025 : 12:18:28.415 :     sortcolumn = numfac
23/07/2025 : 12:18:28.415 :     sortdir = ascending
23/07/2025 : 12:18:37.016 :     Count = 8832132
23/07/2025 : 12:18:37.017 : Leaving
23/07/2025 : 12:18:37.586 : Entering
23/07/2025 : 12:18:37.586 :     dateMinFacture = 
23/07/2025 : 12:18:37.587 :     dateMaxFacture = 
23/07/2025 : 12:18:37.587 :     sortcolumn = montant
23/07/2025 : 12:18:37.587 :     sortdir = descending
23/07/2025 : 12:19:08.784 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 12:22:26.285 :     Entering
23/07/2025 : 12:22:26.285 :         dateMinFacture = 
23/07/2025 : 12:22:26.286 :         dateMaxFacture = 
23/07/2025 : 12:22:26.286 :         sortcolumn = montant
23/07/2025 : 12:22:26.286 :         sortdir = descending
23/07/2025 : 12:22:56.700 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 12:23:06.369 :         Entering
23/07/2025 : 12:23:06.370 :             dateMinFacture = 
23/07/2025 : 12:23:06.370 :             dateMaxFacture = 
23/07/2025 : 12:23:06.370 :             sortcolumn = montant
23/07/2025 : 12:23:06.370 :             sortdir = descending
23/07/2025 : 12:23:11.435 :             Count = 55904
23/07/2025 : 12:23:11.436 :         Leaving
23/07/2025 : 12:23:25.607 :         Entering
23/07/2025 : 12:23:25.610 :             dateMinFacture = 16/05/2023 00:00:00
23/07/2025 : 12:23:25.610 :             dateMaxFacture = 
23/07/2025 : 12:23:25.614 :             sortcolumn = montant
23/07/2025 : 12:23:25.614 :             sortdir = descending
23/07/2025 : 12:23:26.678 :             The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 12:23:32.832 :             Entering
23/07/2025 : 12:23:32.833 :                 dateMinFacture = 16/05/2023 00:00:00
23/07/2025 : 12:23:32.834 :                 dateMaxFacture = 
23/07/2025 : 12:23:32.835 :                 sortcolumn = facturation
23/07/2025 : 12:23:32.835 :                 sortdir = ascending
23/07/2025 : 12:23:35.041 :                 The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 12:38:43.173 :                 Entering
23/07/2025 : 12:38:43.174 :                     dateMinFacture = 
23/07/2025 : 12:38:43.174 :                     dateMaxFacture = 
23/07/2025 : 12:38:43.175 :                     sortcolumn = numfac
23/07/2025 : 12:38:43.175 :                     sortdir = ascending
23/07/2025 : 12:38:53.359 :                     Count = 8832132
23/07/2025 : 12:38:53.360 :                 Leaving
23/07/2025 : 12:39:53.470 :                 Entering
23/07/2025 : 12:39:53.471 :                     dateMinFacture = 
23/07/2025 : 12:39:53.472 :                     dateMaxFacture = 
23/07/2025 : 12:39:53.473 :                     sortcolumn = numfac
23/07/2025 : 12:39:53.479 :                     sortdir = ascending
23/07/2025 : 12:40:00.152 :                     Count = 8832132
23/07/2025 : 12:40:00.152 :                 Leaving
23/07/2025 : 12:40:55.394 :                 Entering
23/07/2025 : 12:40:55.395 :                     dateMinFacture = 
23/07/2025 : 12:40:55.398 :                     dateMaxFacture = 03/07/2025 00:00:00
23/07/2025 : 12:40:55.399 :                     sortcolumn = numfac
23/07/2025 : 12:40:55.400 :                     sortdir = ascending
23/07/2025 : 12:40:57.649 :                     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 12:52:25.838 : Entering
23/07/2025 : 12:52:25.843 :     dateMinFacture = 
23/07/2025 : 12:52:25.843 :     dateMaxFacture = 
23/07/2025 : 12:52:25.844 :     sortcolumn = numfac
23/07/2025 : 12:52:25.844 :     sortdir = ascending
23/07/2025 : 12:52:35.142 :     Count = 8832132
23/07/2025 : 12:52:35.143 : Leaving
23/07/2025 : 12:52:35.812 : Entering
23/07/2025 : 12:52:35.812 :     dateMinFacture = 
23/07/2025 : 12:52:35.813 :     dateMaxFacture = 
23/07/2025 : 12:52:35.813 :     sortcolumn = numfac
23/07/2025 : 12:52:35.813 :     sortdir = ascending
23/07/2025 : 12:52:39.439 :     Count = 8832132
23/07/2025 : 12:52:39.440 : Leaving
23/07/2025 : 12:52:46.516 : Entering
23/07/2025 : 12:52:46.519 :     dateMinFacture = 11/07/2025 00:00:00
23/07/2025 : 12:52:46.519 :     dateMaxFacture = 
23/07/2025 : 12:52:46.520 :     sortcolumn = numfac
23/07/2025 : 12:52:46.520 :     sortdir = ascending
23/07/2025 : 12:53:17.242 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 12:53:27.746 :     Entering
23/07/2025 : 12:53:27.750 :         dateMinFacture = 11/07/2025 00:00:00
23/07/2025 : 12:53:27.752 :         dateMaxFacture = 
23/07/2025 : 12:53:27.753 :         sortcolumn = numfac
23/07/2025 : 12:53:27.754 :         sortdir = ascending
23/07/2025 : 12:53:29.231 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 12:56:05.258 : Entering
23/07/2025 : 12:56:05.263 :     dateMinFacture = 
23/07/2025 : 12:56:05.264 :     dateMaxFacture = 
23/07/2025 : 12:56:05.264 :     sortcolumn = numfac
23/07/2025 : 12:56:05.265 :     sortdir = ascending
23/07/2025 : 12:56:13.972 :     Count = 8832132
23/07/2025 : 12:56:13.973 : Leaving
23/07/2025 : 12:56:14.532 : Entering
23/07/2025 : 12:56:14.532 :     dateMinFacture = 
23/07/2025 : 12:56:14.532 :     dateMaxFacture = 
23/07/2025 : 12:56:14.533 :     sortcolumn = numfac
23/07/2025 : 12:56:14.533 :     sortdir = ascending
23/07/2025 : 12:56:17.706 :     Count = 8832132
23/07/2025 : 12:56:17.707 : Leaving
23/07/2025 : 12:56:20.099 : Entering
23/07/2025 : 12:56:20.099 :     dateMinFacture = 
23/07/2025 : 12:56:20.100 :     dateMaxFacture = 
23/07/2025 : 12:56:20.100 :     sortcolumn = numfac
23/07/2025 : 12:56:20.100 :     sortdir = ascending
23/07/2025 : 12:56:22.481 :     Count = 55904
23/07/2025 : 12:56:22.482 : Leaving
23/07/2025 : 12:56:36.563 : Entering
23/07/2025 : 12:56:36.564 :     dateMinFacture = 
23/07/2025 : 12:56:36.574 :     dateMaxFacture = 
23/07/2025 : 12:56:36.576 :     sortcolumn = numfac
23/07/2025 : 12:56:36.577 :     sortdir = ascending
23/07/2025 : 12:56:38.782 :     Count = 50665
23/07/2025 : 12:56:38.782 : Leaving
23/07/2025 : 12:56:38.797 : Entering
23/07/2025 : 12:56:38.797 :     dateMinFacture = 
23/07/2025 : 12:56:38.798 :     dateMaxFacture = 
23/07/2025 : 12:56:38.799 :     sortcolumn = numfac
23/07/2025 : 12:56:38.799 :     sortdir = ascending
23/07/2025 : 12:56:40.704 :     Count = 50665
23/07/2025 : 12:56:40.705 : Leaving
23/07/2025 : 12:56:46.263 : Entering
23/07/2025 : 12:56:46.264 :     dateMinFacture = 
23/07/2025 : 12:56:46.268 :     dateMaxFacture = 
23/07/2025 : 12:56:46.270 :     sortcolumn = numfac
23/07/2025 : 12:56:46.271 :     sortdir = ascending
23/07/2025 : 12:56:48.729 :     Count = 0
23/07/2025 : 12:56:48.729 : Leaving
23/07/2025 : 12:57:00.310 : Entering
23/07/2025 : 12:57:00.311 :     dateMinFacture = 
23/07/2025 : 12:57:00.314 :     dateMaxFacture = 
23/07/2025 : 12:57:00.316 :     sortcolumn = numfac
23/07/2025 : 12:57:00.321 :     sortdir = ascending
23/07/2025 : 12:57:02.189 :     Count = 0
23/07/2025 : 12:57:02.189 : Leaving
23/07/2025 : 12:58:04.375 : Entering
23/07/2025 : 12:58:04.377 :     dateMinFacture = 19/12/2023 00:00:00
23/07/2025 : 12:58:04.377 :     dateMaxFacture = 
23/07/2025 : 12:58:04.377 :     sortcolumn = numfac
23/07/2025 : 12:58:04.378 :     sortdir = ascending
23/07/2025 : 12:58:06.065 :     Count = 0
23/07/2025 : 12:58:06.068 : Leaving
23/07/2025 : 13:00:18.976 : Entering
23/07/2025 : 13:00:18.979 :     dateMinFacture = 
23/07/2025 : 13:00:18.980 :     dateMaxFacture = 
23/07/2025 : 13:00:18.980 :     sortcolumn = numfac
23/07/2025 : 13:00:18.981 :     sortdir = ascending
23/07/2025 : 13:00:35.193 :     Count = 8832132
23/07/2025 : 13:00:35.194 : Leaving
23/07/2025 : 13:00:35.821 : Entering
23/07/2025 : 13:00:35.821 :     dateMinFacture = 
23/07/2025 : 13:00:35.822 :     dateMaxFacture = 
23/07/2025 : 13:00:35.822 :     sortcolumn = numfac
23/07/2025 : 13:00:35.822 :     sortdir = ascending
23/07/2025 : 13:00:45.640 :     Count = 8832132
23/07/2025 : 13:00:45.641 : Leaving
23/07/2025 : 13:00:53.485 : Entering
23/07/2025 : 13:00:53.486 :     dateMinFacture = 
23/07/2025 : 13:00:53.487 :     dateMaxFacture = 
23/07/2025 : 13:00:53.488 :     sortcolumn = numfac
23/07/2025 : 13:00:53.489 :     sortdir = ascending
23/07/2025 : 13:00:57.518 :     Count = 55904
23/07/2025 : 13:00:57.518 : Leaving
23/07/2025 : 13:01:04.489 : Entering
23/07/2025 : 13:01:04.491 :     dateMinFacture = 06/02/2025 00:00:00
23/07/2025 : 13:01:04.491 :     dateMaxFacture = 
23/07/2025 : 13:01:04.492 :     sortcolumn = numfac
23/07/2025 : 13:01:04.493 :     sortdir = ascending
23/07/2025 : 13:01:06.536 :     The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 13:01:58.857 : Entering
23/07/2025 : 13:01:58.862 :     dateMinFacture = 
23/07/2025 : 13:01:58.863 :     dateMaxFacture = 
23/07/2025 : 13:01:58.863 :     sortcolumn = numfac
23/07/2025 : 13:01:58.863 :     sortdir = ascending
23/07/2025 : 13:02:08.486 :     Count = 8832132
23/07/2025 : 13:02:08.488 : Leaving
23/07/2025 : 13:02:08.809 : Entering
23/07/2025 : 13:02:08.809 :     dateMinFacture = 
23/07/2025 : 13:02:08.810 :     dateMaxFacture = 
23/07/2025 : 13:02:08.810 :     sortcolumn = numfac
23/07/2025 : 13:02:08.810 :     sortdir = ascending
23/07/2025 : 13:02:17.085 :     Count = 8832132
23/07/2025 : 13:02:17.086 : Leaving
23/07/2025 : 13:02:21.113 : Entering
23/07/2025 : 13:02:21.117 :     dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:02:21.119 :     dateMaxFacture = 
23/07/2025 : 13:02:21.123 :     sortcolumn = numfac
23/07/2025 : 13:02:21.124 :     sortdir = ascending
23/07/2025 : 13:02:51.367 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 13:03:15.214 :     Entering
23/07/2025 : 13:03:15.215 :         dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:03:15.216 :         dateMaxFacture = 
23/07/2025 : 13:03:15.217 :         sortcolumn = numfac
23/07/2025 : 13:03:15.218 :         sortdir = ascending
23/07/2025 : 13:03:43.598 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 13:03:43.636 :         Entering
23/07/2025 : 13:03:43.637 :             dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:03:43.639 :             dateMaxFacture = 
23/07/2025 : 13:03:43.640 :             sortcolumn = numfac
23/07/2025 : 13:03:43.642 :             sortdir = ascending
23/07/2025 : 13:04:13.884 :             Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 13:08:40.258 :             Entering
23/07/2025 : 13:08:40.259 :                 dateMinFacture = 17/07/2025 00:00:00
23/07/2025 : 13:08:40.260 :                 dateMaxFacture = 
23/07/2025 : 13:08:40.261 :                 sortcolumn = numfac
23/07/2025 : 13:08:40.262 :                 sortdir = ascending
23/07/2025 : 13:09:04.131 :                 The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 19:41:09.593 : Entering
23/07/2025 : 19:41:09.596 :     dateMinFacture = 
23/07/2025 : 19:41:09.596 :     dateMaxFacture = 
23/07/2025 : 19:41:09.596 :     sortcolumn = numfac
23/07/2025 : 19:41:09.596 :     sortdir = ascending
23/07/2025 : 19:41:19.021 :     Count = 8832132
23/07/2025 : 19:41:19.022 : Leaving
23/07/2025 : 19:41:19.730 : Entering
23/07/2025 : 19:41:19.731 :     dateMinFacture = 
23/07/2025 : 19:41:19.731 :     dateMaxFacture = 
23/07/2025 : 19:41:19.732 :     sortcolumn = numfac
23/07/2025 : 19:41:19.732 :     sortdir = ascending
23/07/2025 : 19:41:25.881 :     Count = 8832132
23/07/2025 : 19:41:25.881 : Leaving
23/07/2025 : 19:43:48.075 : Entering
23/07/2025 : 19:43:48.076 :     dateMinFacture = 
23/07/2025 : 19:43:48.077 :     dateMaxFacture = 
23/07/2025 : 19:43:48.078 :     sortcolumn = numfac
23/07/2025 : 19:43:48.080 :     sortdir = ascending
23/07/2025 : 19:43:52.540 :     Count = 8832132
23/07/2025 : 19:43:52.540 : Leaving
23/07/2025 : 19:43:52.640 : Entering
23/07/2025 : 19:43:52.641 :     dateMinFacture = 
23/07/2025 : 19:43:52.641 :     dateMaxFacture = 
23/07/2025 : 19:43:52.642 :     sortcolumn = numfac
23/07/2025 : 19:43:52.643 :     sortdir = ascending
23/07/2025 : 19:43:55.190 :     Count = 8832132
23/07/2025 : 19:43:55.191 : Leaving
23/07/2025 : 19:44:07.348 : Entering
23/07/2025 : 19:44:07.349 :     dateMinFacture = 
23/07/2025 : 19:44:07.350 :     dateMaxFacture = 
23/07/2025 : 19:44:07.351 :     sortcolumn = datefac
23/07/2025 : 19:44:07.352 :     sortdir = ascending
23/07/2025 : 19:44:38.817 :     Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 19:44:38.857 :     Entering
23/07/2025 : 19:44:38.858 :         dateMinFacture = 
23/07/2025 : 19:44:38.859 :         dateMaxFacture = 
23/07/2025 : 19:44:38.863 :         sortcolumn = datefac
23/07/2025 : 19:44:38.867 :         sortdir = descending
23/07/2025 : 19:44:54.471 :         Count = 8832132
23/07/2025 : 19:44:54.471 :     Leaving
23/07/2025 : 19:45:06.137 :     Entering
23/07/2025 : 19:45:06.138 :         dateMinFacture = 12/07/2025 00:00:00
23/07/2025 : 19:45:06.139 :         dateMaxFacture = 
23/07/2025 : 19:45:06.139 :         sortcolumn = datefac
23/07/2025 : 19:45:06.139 :         sortdir = descending
23/07/2025 : 19:45:15.400 :         The conversion of a varchar data type to a datetime data type resulted in an out-of-range value.
23/07/2025 : 20:10:23.297 :         Entering
23/07/2025 : 20:10:23.305 :             dateMinFacture = 12/07/2025 00:00:00
23/07/2025 : 20:10:23.306 :             dateMaxFacture = 
23/07/2025 : 20:10:23.306 :             sortcolumn = datefac
23/07/2025 : 20:10:23.306 :             sortdir = descending
23/07/2025 : 20:10:24.112 :             Incorrect syntax near the keyword 'DESC'.
Invalid usage of the option NEXT in the FETCH statement.
23/07/2025 : 23:40:22.498 : Entering
23/07/2025 : 23:40:22.503 :     dateMinFacture = 
23/07/2025 : 23:40:22.504 :     dateMaxFacture = 
23/07/2025 : 23:40:22.504 :     sortcolumn = numfac
23/07/2025 : 23:40:22.504 :     sortdir = ascending
23/07/2025 : 23:40:27.219 :     Unable to cast object of type 'System.DateTime' to type 'System.String'.
23/07/2025 : 23:40:27.564 :     Entering
23/07/2025 : 23:40:27.565 :         dateMinFacture = 
23/07/2025 : 23:40:27.565 :         dateMaxFacture = 
23/07/2025 : 23:40:27.565 :         sortcolumn = datefac
23/07/2025 : 23:40:27.566 :         sortdir = descending
23/07/2025 : 23:40:58.251 :         Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 23:41:12.951 :         Entering
23/07/2025 : 23:41:12.951 :             dateMinFacture = 
23/07/2025 : 23:41:12.952 :             dateMaxFacture = 
23/07/2025 : 23:41:12.952 :             sortcolumn = datefac
23/07/2025 : 23:41:12.953 :             sortdir = descending
23/07/2025 : 23:41:13.883 :             Unable to cast object of type 'System.DateTime' to type 'System.String'.
23/07/2025 : 23:41:20.727 :             Entering
23/07/2025 : 23:41:20.727 :                 dateMinFacture = 
23/07/2025 : 23:41:20.728 :                 dateMaxFacture = 
23/07/2025 : 23:41:20.728 :                 sortcolumn = datefac
23/07/2025 : 23:41:20.728 :                 sortdir = descending
23/07/2025 : 23:41:21.113 :                 Unable to cast object of type 'System.DateTime' to type 'System.String'.
23/07/2025 : 23:48:55.512 : Entering
23/07/2025 : 23:48:55.516 :     dateMinFacture = 
23/07/2025 : 23:48:55.516 :     dateMaxFacture = 
23/07/2025 : 23:48:55.517 :     sortcolumn = numfac
23/07/2025 : 23:48:55.517 :     sortdir = ascending
23/07/2025 : 23:48:59.120 :     Unable to cast object of type 'System.DateTime' to type 'System.String'.
23/07/2025 : 23:48:59.395 :     Entering
23/07/2025 : 23:48:59.395 :         dateMinFacture = 
23/07/2025 : 23:48:59.396 :         dateMaxFacture = 
23/07/2025 : 23:48:59.396 :         sortcolumn = datefac
23/07/2025 : 23:48:59.396 :         sortdir = descending
23/07/2025 : 23:49:18.631 :         Unable to cast object of type 'System.DateTime' to type 'System.String'.
23/07/2025 : 23:57:40.390 : Entering
23/07/2025 : 23:57:40.392 :     dateMinFacture = 
23/07/2025 : 23:57:40.393 :     dateMaxFacture = 
23/07/2025 : 23:57:40.393 :     sortcolumn = numfac
23/07/2025 : 23:57:40.393 :     sortdir = ascending
23/07/2025 : 23:57:47.623 :     Count = 8832132
23/07/2025 : 23:57:47.625 : Leaving
23/07/2025 : 23:57:48.029 : Entering
23/07/2025 : 23:57:48.029 :     dateMinFacture = 
23/07/2025 : 23:57:48.029 :     dateMaxFacture = 
23/07/2025 : 23:57:48.030 :     sortcolumn = datefac
23/07/2025 : 23:57:48.030 :     sortdir = descending
23/07/2025 : 23:58:07.712 :     Count = 8832132
23/07/2025 : 23:58:07.713 : Leaving
23/07/2025 : 23:58:17.000 : Entering
23/07/2025 : 23:58:17.000 :     dateMinFacture = 
23/07/2025 : 23:58:17.001 :     dateMaxFacture = 
23/07/2025 : 23:58:17.001 :     sortcolumn = datefac
23/07/2025 : 23:58:17.001 :     sortdir = descending
23/07/2025 : 23:58:23.214 :     Count = 0
23/07/2025 : 23:58:23.215 : Leaving
23/07/2025 : 23:58:40.632 : Entering
23/07/2025 : 23:58:40.633 :     dateMinFacture = 
23/07/2025 : 23:58:40.634 :     dateMaxFacture = 10/07/2020 00:00:00
23/07/2025 : 23:58:40.634 :     sortcolumn = datefac
23/07/2025 : 23:58:40.634 :     sortdir = descending
23/07/2025 : 23:58:49.140 :     Count = 0
23/07/2025 : 23:58:49.140 : Leaving
