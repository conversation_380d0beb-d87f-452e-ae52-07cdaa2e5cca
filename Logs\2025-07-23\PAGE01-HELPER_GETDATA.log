23/07/2025 : 08:04:53.508 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 08:04:55.144 : Successfully retrieved 14 items
23/07/2025 : 08:04:58.965 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=baseamort, sortdir=descending
23/07/2025 : 08:04:59.593 : Successfully retrieved 14 items
23/07/2025 : 08:05:10.573 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
23/07/2025 : 08:05:41.434 : Error executing stored procedure: Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
23/07/2025 : 08:05:41.448 : Stack trace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at CarSystemsWebApp.Components.Pages.Page01FacturesAIAnnuel.MyCustomAdaptor.Helper_GetData(String client, Nullable`1 dateFactureStart, Nullable`1 dateFactureEnd, String etat, String typeFacture, Int32 skip, Int32 take, String sortcolumn, String sortdir) in C:\SVN\CarSystemsWebApp\Components\Pages\Page01FacturesAIAnnuel.razor:line 509
23/07/2025 : 08:05:41.606 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=descending
23/07/2025 : 08:06:00.170 : Successfully retrieved 14 items
23/07/2025 : 08:06:06.732 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 08:06:07.296 : Successfully retrieved 14 items
23/07/2025 : 08:06:15.927 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=ascending
23/07/2025 : 08:06:30.009 : Successfully retrieved 14 items
23/07/2025 : 08:06:32.175 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=montant, sortdir=descending
23/07/2025 : 08:06:44.775 : Successfully retrieved 14 items
23/07/2025 : 08:06:47.149 : Executing stored procedure with params: client=, dateStart=, dateEnd=, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 08:06:47.745 : Successfully retrieved 14 items
23/07/2025 : 08:07:01.896 : Executing stored procedure with params: client=, dateStart=, dateEnd=05/02/2020 00:00:00, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 08:07:02.388 : Successfully retrieved 14 items
23/07/2025 : 08:24:26.754 : Executing stored procedure with params: client=, dateStart=23/04/2025 09:24:26, dateEnd=23/07/2025 09:24:26, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 08:24:59.827 : Successfully retrieved 0 items
23/07/2025 : 08:25:01.588 : Executing stored procedure with params: client=, dateStart=23/04/2025 09:24:26, dateEnd=23/07/2025 09:24:26, etat=, typeFacture=, skip=0, take=14, sortcolumn=numfac, sortdir=ascending
23/07/2025 : 08:25:04.258 : Successfully retrieved 0 items
