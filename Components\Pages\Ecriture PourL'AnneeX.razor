﻿@page "/ecritures"

@rendermode InteractiveServer

@using CarSystemsWebApp.Models
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.XlsIO
@using System.Data
@using System.Reflection

@inject IJSRuntime      _jsRuntime
@inject SfDialogService _dialogService

<style>

    /* #MyGrid .e-gridcontent {
    height: calc(100% - 0px) !important;
    } */

</style>

<h1>Ecritures</h1>

<!-- ============== -->
<!-- SEARCH FILTERS -->
<!-- ============== -->

<div style="margin-bottom: 10px; border: 1px solid #BBBBBB; border-radius: 5px; padding: 14px; background-color: transparent;">

    <div class="row" style="margin-bottom: 10px">


        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD">Code journal :</div>
            <SfDropDownList TValue="string" TItem="string" DataSource="@Codes" ShowClearButton="true" Placeholder="Choisissez un code" PopupHeight="220" @bind-Value="@SelectedCodeJournal">
            </SfDropDownList>
        </div>


        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD">Compte général :</div>
            <SfTextBox @bind-Value="@SelectedNumCompteGeneral" Placeholder="Saisissez le compte général" Width="200"></SfTextBox>
        </div>

        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD">Compte auxiliaire :</div>
            <SfTextBox @bind-Value="@SelectedNumCompteAux" Placeholder="Saisissez le compte auxiliaire" Width="200"></SfTextBox>
        </div>

    </div>

    <div class="row">

        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD MYFORMFIELD2">Date min :</div>
            <SfDateTimePicker TValue="DateTime?" Placeholder="Choisir une date" Format="dd-MM-yyyy HH:mm" @bind-Value="@SelectedDateMin"></SfDateTimePicker>
        </div>

        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD MYFORMFIELD2">Date max :</div>
            <SfDateTimePicker TValue="DateTime?" Placeholder="Choisir une date" Format="dd-MM-yyyy HH:mm" @bind-Value="@SelectedDateMax"></SfDateTimePicker>
        </div>

        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD">Numéro d'écriture :</div>
            <SfTextBox @bind-Value="@SelectedNumEcriture" Placeholder="Saisissez le numéro d'écriture" Width="200"></SfTextBox>
        </div>

    </div>

    @*
    <div class="row">
        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD MYFORMFIELD2">Agent :</div>
            <SfMultiSelect TValue="string[]" TItem="User" Placeholder="Choisissez un ou plusieurs agents" DataSource="@Agents" @bind-Value="@SelectedAgents" OnValueSelect="@OnDropDownAgentsValueChange">
                <MultiSelectFieldSettings Text="Name" Value="UserID"></MultiSelectFieldSettings>
            </SfMultiSelect>
        </div>

        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD MYFORMFIELD2">Noté par :</div>
            <SfDropDownList TValue="string" TItem="User" DataSource="@Supervisors" ShowClearButton="true" Placeholder="Choisissez un superviseur" PopupHeight="220" @bind-Value="@SelectedSupervisorID">
                <DropDownListFieldSettings Text="Name" Value="UserID"></DropDownListFieldSettings>
                <DropDownListEvents TValue="string" TItem="User" ValueChange="OnDropdownListUserValueChange"></DropDownListEvents>
            </SfDropDownList>
        </div>

        <div class="col-3">
            <div class="FORMFIELD1 MYFORMFIELD MYFORMFIELD2">Code de Qualification :</div>
            <SfDropDownTree TItem="QualifItem" TValue="string" Placeholder="Choisissez un code de qualification">
                <DropDownTreeField TItem="QualifItem" ID="id" Text="name" Child="child" DataSource="@QualifItems"></DropDownTreeField>
            </SfDropDownTree>
        </div>
    </div>
    *@

    <div class="row">
        <div class="col-3">
            <div style="margin-top: 20px">
                <span>
                    <SfButton Type="button" @onclick="Helper_Refresh" IsPrimary="true">Appliquer</SfButton>                    
                </span>
                <span style="margin-left: 8px">
                    <SfButton OnClick="OnButtonExport">Exporter</SfButton>
                </span>
            </div>
        </div>
    </div> 

</div>

<!-- ======= -->
<!-- RESULTS -->
<!-- ======= -->

@* <SfToolbar>
    <ToolbarItems>

        <ToolbarItem Id=HRRefreshGrid
        PrefixIcon="e-icons e-refresh"
        Tooltiptext="Actualiser"
        OnClick="OnToolbarButtonRefresh" />

        <ToolbarItem Type=ItemType.Separator />

    </ToolbarItems>
</SfToolbar> *@

@* style="height: calc(100vh - 205px) !important;" *@

<div>
    <SfGrid ID="MyGrid" 
    TValue="CS_ECR_ITEM"
    @ref="@MyGrid"
    Query="@MyQuery"
    AllowPaging="true"
    AllowFiltering="false"
    AllowSorting="true"
    AllowResizing="true" EnablePersistence="true"
    ShowColumnChooser="true" Toolbar=@ToolbarItems
    EnableInfiniteScrolling="false"
    Height="100%">
        <GridEvents TValue="CS_ECR_ITEM"
        RowSelected="OnRowSelected" 
        OnDataBound="DataBoundHandler"
        OnActionBegin="OnActionBegin"
        OnActionComplete="OnActionComplete"/>
        <SfDataManager AdaptorInstance="@typeof(MyCustomAdaptor)" Adaptor="Adaptors.CustomAdaptor" />
        <GridSelectionSettings Mode="Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single" />
        <GridPageSettings PageSize="15" />
        <GridColumns>
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_societe) HeaderText="code_societe" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_ex) HeaderText="num_ex" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_jrn) HeaderText="code_jrn" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datecompta2) HeaderText="datecompta" Format="dd/MM/yyy" Type=ColumnType.DateOnly Width="145" />

            <GridColumn Field=@nameof(CS_ECR_ITEM.num_ecr) HeaderText="num_ecr" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numlig) HeaderText="numlig" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numlig_let) HeaderText="numlig_let" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numlig_tva) HeaderText="numlig_tva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_etablissement) HeaderText="code_etablissement" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.type) HeaderText="type" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.etat) HeaderText="etat" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.typpiece) HeaderText="typpiece" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numpiece) HeaderText="numpiece" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datepiece) HeaderText="datepiece" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_compte_gen) HeaderText="num_compte_gen" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_compte_aux) HeaderText="num_compte_aux" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.libelle) HeaderText="libelle" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.debit) HeaderText="debit" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.credit) HeaderText="credit" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.dateech) HeaderText="dateech" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_reg) HeaderText="code_reg" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.reflettrage) HeaderText="reflettrage" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.let) HeaderText="let" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.letpartiel) HeaderText="letpartiel" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datelettrage) HeaderText="datelettrage" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.letsuivant) HeaderText="letsuivant" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.pointe) HeaderText="pointe" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_tva) HeaderText="code_tva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.typassutva) HeaderText="typassutva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.litige) HeaderText="litige" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.central) HeaderText="central" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.annul) HeaderText="annul" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.transfer) HeaderText="transfer" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.dateheuretransfer) HeaderText="dateheuretransfer" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_parc) HeaderText="code_parc" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_prest) HeaderText="code_prest" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_agence) HeaderText="code_agence" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datecrea) HeaderText="datecrea" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datemodi) HeaderText="datemodi" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_utilis_crea) HeaderText="code_utilis_crea" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.code_utilis_modi) HeaderText="code_utilis_modi" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zalp1) HeaderText="zalp1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zalp2) HeaderText="zalp2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zalp3) HeaderText="zalp3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zdat1) HeaderText="zdat1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zdat2) HeaderText="zdat2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zdat3) HeaderText="zdat3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.znum1) HeaderText="znum1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.znum2) HeaderText="znum2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.znum3) HeaderText="znum3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.zonecle) HeaderText="zonecle" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.saisie) HeaderText="saisie" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.debiteuro) HeaderText="debiteuro" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.crediteuro) HeaderText="crediteuro" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.debitpays) HeaderText="debitpays" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.creditpays) HeaderText="creditpays" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.deviserefeuro) HeaderText="deviserefeuro" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.moistva) HeaderText="moistva" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.datevaleur) HeaderText="datevaleur" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.num_reg) HeaderText="num_reg" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.contrepartie) HeaderText="contrepartie" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.moyenpaie) HeaderText="moyenpaie" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.numfacfisc) HeaderText="numfacfisc" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.extractDW) HeaderText="extractDW" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp1) HeaderText="z40alp1" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp2) HeaderText="z40alp2" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp3) HeaderText="z40alp3" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp4) HeaderText="z40alp4" />
            <GridColumn Field=@nameof(CS_ECR_ITEM.z40alp5 ) HeaderText="z40alp5" />
        </GridColumns>
    </SfGrid>
</div>

<script type="text/javascript">

    function saveAsFile(filename, bytesBase64) {
    if (navigator.msSaveBlob) {
    //Download document in Edge browser
    var data = window.atob(bytesBase64);
    var bytes = new Uint8Array(data.length);
    for (var i = 0; i < data.length; i++) {
    bytes[i] = data.charCodeAt(i);
    }
    var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
    navigator.msSaveBlob(blob, filename);
    }
    else {
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link); // Needed for Firefox
    link.click();
    document.body.removeChild(link);
    }
    }

</script>

@code {

    private SfGrid<CS_ECR_ITEM> MyGrid;
    private Query               MyQuery;
    private int                 ItemsCount;
    private string[]            ToolbarItems = new string[] { "ColumnChooser" };
    private List<string>        Codes;

    private DateTime?   SelectedDateMin;
    private DateTime?   SelectedDateMax;
    private string      SelectedNumCompteGeneral;
    private string      SelectedNumCompteAux;
    private string      SelectedCodeJournal;
    private string      SelectedNumEcriture;


    protected override void OnInitialized()
    {
        this.MyQuery = new Query()
            .AddParams("DateMin", null)
            .AddParams("DateMax", null)
            .AddParams("NumCompteGeneral", null)
            .AddParams("NumCompteAux", null)
            .AddParams("CodeJournal", null)
            .AddParams("NumEcriture", null);

        this.Codes = new List<string>() {
            "WA", "AS", "AN", "EF", "OD",
            "FE", "CA", "EN", "V1", "DE",
            "PA", "FO", "BQ", "PR", "CV",
            "VV", "FR", "FV", "AP", "FL", "VT"
        };

        this.Codes.Sort();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender == true)
        {
            await Helper_Refresh();
        }

        // return base.OnAfterRenderAsync(firstRender);
    }

    public void OnRowSelected(RowSelectEventArgs<CS_ECR_ITEM> args) { }

    public void DataBoundHandler(BeforeDataBoundArgs<CS_ECR_ITEM> args)
    {
        this.ItemsCount = args.Count;

        StateHasChanged();
    }

    public void OnActionBegin(ActionEventArgs<CS_ECR_ITEM> args) { }

    public void OnActionComplete(ActionEventArgs<CS_ECR_ITEM> args) { }

    private async void OnToolbarButtonRefresh()
    {
        await MyGrid.Refresh();
    }

    private async Task Helper_Refresh()
    {
        MyQuery.Queries.Params["DateMin"]           = this.SelectedDateMin;
        MyQuery.Queries.Params["DateMax"]           = this.SelectedDateMax;
        MyQuery.Queries.Params["NumCompteGeneral"]  = this.SelectedNumCompteGeneral;
        MyQuery.Queries.Params["NumCompteAux"]      = this.SelectedNumCompteAux;
        MyQuery.Queries.Params["CodeJournal"]       = this.SelectedCodeJournal;
        MyQuery.Queries.Params["NumEcriture"]       = this.SelectedNumEcriture;

        await MyGrid.Refresh();
    }

    private async void OnButtonExport()
    {
        // if (this.DataSourceGridShifts == null && this.DataSourceGridResults == null)
        // {
        //     await _dialogService.AlertAsync("Aucune donnée à exporter");
        //     return;
        // }

        //Create an instance of ExcelEngine.
        using (ExcelEngine excelEngine = new ExcelEngine())
        {
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            // ===================================
            // Create a workbook with 2 worksheets
            // ===================================

            IWorkbook workbook = application.Workbooks.Create(2);

            IWorksheet worksheet;
            DataTable table;

            // =====================
            // Worksheet #0 : shifts
            // =====================

            worksheet = workbook.Worksheets[0];

            worksheet.Name = "ECRITURES";

            // var dateMin             = MyQuery.Params["DateMin"] as DateTime?;
            // var dateMax             = MyQuery.Params["DateMax"] as DateTime?;
            // var numCompteGeneral    = MyQuery.Params["NumCompteGeneral"] as string;
            // var numCompteAux        = MyQuery.Params["NumCompteAux"] as string;

            string sortcolumn   = "code_societe";
            string sortdir      = "ascending";

            var items = MyCustomAdaptor.Helper_GetData(
                this.SelectedDateMin, 
                this.SelectedDateMax, 
                this.SelectedNumCompteGeneral, 
                this.SelectedNumCompteAux, 
                this.SelectedCodeJournal,
                this.SelectedNumEcriture,
                0, 100000, 
                sortcolumn, sortdir);

            CarSystemsContext model;
            model = new CarSystemsContext();

            int count;
            count = model.Set<IntReturn>()
                .FromSqlRaw("EXECUTE dbo.GetEcrItemsCount {0}, {1}, {2}, {3}, {4}, {5}",
                    this.SelectedDateMin,
                    this.SelectedDateMax,
                    this.SelectedNumCompteGeneral,
                    this.SelectedNumCompteAux,
                    this.SelectedCodeJournal,
                    this.SelectedNumEcriture)
                .AsEnumerable()
                .First().Value;

            Type type = typeof(CS_ECR_ITEM);
            PropertyInfo[] properties = type.GetProperties(); // Get all properties

            // Create and fill a DataTable
            table = new DataTable();
            foreach (PropertyInfo prop in properties)
                table.Columns.Add(prop.Name);
            
            foreach (var item in items)
            {
                DataRow row;
                row = table.NewRow();

                int k = 0;

                foreach (PropertyInfo prop in properties)
                {
                    row[k] = prop.GetValue(item);
                    k++;
                }

                table.Rows.Add(row);
            }

            // Export data from DataTable to Excel worksheet.
            worksheet.ImportDataTable(table, true, 1, 1);

            worksheet.UsedRange.AutofitColumns();

            // ===================================================
            // Save the document as a stream and return the stream
            // ===================================================

            using (MemoryStream stream = new MemoryStream())
            {
                // Save the created Excel document to MemoryStream
                workbook.SaveAs(stream);

                // Download the excel file
                string filename;
                filename = string.Format("ExportEcritures.xlsx");
                await Helper_SaveAs(_jsRuntime, filename, stream.ToArray());
            }
        }
    }

    public ValueTask<object> Helper_SaveAs(IJSRuntime js, string filename, byte[] data)
    {
        return js.InvokeAsync<object>(
                "saveAsFile",
                filename,
                Convert.ToBase64String(data));
    }

    // ===========================================

    public class MyCustomAdaptor : DataAdaptor
    {
        public MyCustomAdaptor()
        {

        }

        public override object Read(DataManagerRequest dm, string key = null)
        {
            var logKey = "ECRITURES-DATAADAPTOR";

            CarSystemsWebApp.Utils.Helper_Trace(logKey, "Entering", true);

            try
            {
                List<CS_ECR_ITEM> empty;
                empty = Enumerable.Empty<CS_ECR_ITEM>().ToList();

                if (dm.Params is null || dm.Params.Count == 0)
                {
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Nothing to do", true);
                    CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                    return dm.RequiresCounts ? new DataResult() { Result = empty, Count = 0 } : (object)empty;
                }

                var dateMin             = dm.Params["DateMin"] as DateTime?;
                var dateMax             = dm.Params["DateMax"] as DateTime?;
                var numCompteGeneral    = dm.Params["NumCompteGeneral"] as string;
                var numCompteAux        = dm.Params["NumCompteAux"] as string;
                var codeJournal         = dm.Params["CodeJournal"] as string;
                var numEcriture         = dm.Params["NumEcriture"] as string;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMin = {dateMin}", true);
                CarSystemsWebApp.Utils.Helper_Trace(logKey, $"dateMax = {dateMax}", true);

                string sortcolumn   = "code_societe";
                string sortdir      = "ascending";

                CarSystemsContext model;
                model = new CarSystemsContext();

                /*
                var items = model.CS_ECR_ITEMS.FromSqlRaw("EXECUTE dbo.GetEcrItems {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}",
                    dateMin,
                    dateMax,
                    numCompteGeneral,
                    numCompteAux,
                    dm.Skip,
                    dm.Take,
                    sortcolumn,
                    sortdir).ToList();
                */

                var items = Helper_GetData(dateMin, dateMax, numCompteGeneral, numCompteAux, codeJournal, numEcriture, dm.Skip, dm.Take, sortcolumn, sortdir);

                int count;
                count = model.Set<IntReturn>()
                    .FromSqlRaw("EXECUTE dbo.GetEcrItemsCount {0}, {1}, {2}, {3}, {4}, {5}",
                        dateMin,
                        dateMax,
                        numCompteGeneral,
                        numCompteAux,
                        codeJournal,
                        numEcriture)
                    .AsEnumerable()
                    .First().Value;

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Count = " + count, true);

                CarSystemsWebApp.Utils.Helper_Trace(logKey, "Leaving", true);

                return dm.RequiresCounts ? new DataResult() { Result = items, Count = count } : (object)items;
            }
            catch (Exception ex)
            {
                CarSystemsWebApp.Utils.Helper_Trace(logKey, ex.Message, true);

                return null;
            }
        }

        public static List<CS_ECR_ITEM> Helper_GetData(DateTime? dateMin, DateTime? dateMax, string numCompteGeneral, string numCompteAux, string codeJournal, string numEcriture, int skip, int take, string sortcolumn, string sortdir)
        {
            CarSystemsContext model;
            model = new CarSystemsContext();

            var items = model.CS_ECR_ITEMS.FromSqlRaw("EXECUTE dbo.GetEcrItems {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}",
                dateMin,
                dateMax,
                numCompteGeneral,
                numCompteAux,
                codeJournal,
                numEcriture,
                skip,
                take,
                sortcolumn,
                sortdir).ToList();

            return items;
        }
    }
}
